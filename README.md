# MacOS-Launcher

mac版本游戏启动器，支持的功能：

- 单版本游戏的安装、更新、启动、删除
- 多版本游戏(标准版、极速版、先遣版本等)的切换、安装、更新、启动、删除
- 问题反馈
- Launcher自动更新

# 工程目录说明

- **LauncherSDK**
    - **WMLauncherCore**：Launcher核心模块，包含使用patcher进行游戏下载、游戏启动、删除等纯接口功能
    - **WMLauncherUI**：界面模块，功能通过调用Core中接口
    - **WMLauncherDependency**：依赖的其他未提交到私有库的SDK，包含PatcherSDK、Sparkle，后续可单独更新
        - **Sparkle**：Sparkle库，负责Launcher的自检测更新
            - **Sparkle.framework**：WMMacLauncherUI.podspec中会依赖
            - **bin**：Sparkle命令行工具，发布新版本Launcher会用到
- **LauncherDemo**：LauncherDemo主工程，用于开发时测试
    - **ZhuXianWorldLanucher/Resources/launcherConfig.json**：项目配置文件，包含Patcher使用的游戏资源包、问题反馈用的对象存储等配置
    - **Release**：Launcher版本打包发布配置
        - **appcast.xml**：Sparkle的generate_appcast工具负责生成、更新，也可手动修改
        - **appdmg.json**：制作dmg配置文件
        - **ExportOptions.plist**：Xcode打包时证书及描述文件配置
- **Scripts**：打包归档脚本
    - **archive.sh**：归档脚本，包含.app生成、.dmg制作、Sparkle更新配置的生成
    - **notarization**：发布时对dmg包公证脚本
- **Doc/quark-uploader-0.7.24-bin**：dev后台资源上传命令行工具，可在dev后台查看使用文档
    - **uploadLauncher.sh**：新版本Launcher自动化上传脚本，使用archive.sh后，通过执行该脚本，将Release上传到dev后台
- **sonar-project.properties**：静态分析SonarQube配置文件，Jenkins打包机需要

# 如何快速创建不同场景的工程

根据游戏需求，可能会出面向不同场景的Launcher App，比如用于测游戏性能的Beanchmark版本和正式版本。Beanchmark版本和正式版本又会分为Dev、Test、OB三个App，每种包对应不同的bid和pathcer配置等。要创建新的不同场景工程，可直接拷贝`ZXLauncher_BenchMark_DEV`工程目录，修改为目标场景名，然后根据下表中，依次修改。

## Info.plist中基础配置
- BundleId
- ProductName
- 版本号
- SUFeedURL： Launcher自更新检测URL

## 本地化语言修改
### Launcher中语言修改方式

默认中文，会读取Info.plist中`CFBundleDevelopmentRegion`字段自动设置为繁体中文

```
LauncherLocalization.setLanguage("zh-Hant") //设置为繁体中文
```

### Sparkle更新提示框中语言修改

修`.xcodeproj/project.pbxproj`中`knownRegions` 和 `developmentRegion`字段，没有的话手动添加目标语言。

```
developmentRegion = "zh-Hans";
knownRegions = (
	en,
	Base,
	"zh-Hans",
);

```


## 不同场景Launcher配置
### Release/Resources/launcherConfig.json
```json
{
    "config": {
        "launcherName": "诛仙世界Dev",   //launcher启动后，顶部标题栏显示的名字
        "multiConfig": [
            {
                "versionName": "BenchMark", //只支持单个游戏时，下载游戏包时，下载进度面板显示的名字
                "patcherConfig": "1304_ZX_BenchMarkMac_test_MacOS", //pathcer配置文件
                "client": "Game/ZXBenchmark-Mac-Shipping.app",  //Shipping环境对应.app路径
                "clientDev": "Game/ZXBenchmark.app",    //Develop环境对应.app路径
                "clientTest": "Game/ZXBenchmark-Mac-Test.app",  //test环境对应.app路径
                "cachePath": "ZX_BenchMark_Dev",     //pathcer下载的游戏包缓存目录
                "iconPath": "standardLogo", //侧边栏显示图标
                "bootstrapBundleId": "com.wanmei.zxsjmac.benchmark_bootstrap",   //真正游戏包的引导程序bid，用于开始游戏时，根据该id正确匹配要启动的.app，如没有引导程序，和下面的bundleId一致即可。
                "bundleId": "com.wanmei.zxsjmac.benchmark", //真实游戏.app的BundleId，用于实时监控游戏是否正在运行中
                "launcherwebpart": "https://zxsj.wanmei.com/launcher/launcher_platformBM.html", //主背景webView url
            },
            {
                "versionName": "极速版MacBenchmark",
                "patcherConfig": "1304_ZX_Speed_BenchmarkMac_MacOS",
                "client": "Game/ZXBenchmark-Mac-Shipping.app",
                "clientDev": "Game/ZXBenchmark.app",
                "clientTest": "Game/ZXBenchmark-Mac-Test.app",
                "cachePath": "ZX_BenchMark_Speed_Dev",
                "iconPath": "speedLogo",
                "bootstrapBundleId": "com.wanmei.zxsjmac.benchmark_bootstrap",
                "bundleId": "com.wanmei.zxsjmac.benchmark",
                "launcherwebpart": "https://zxsj.wanmei.com/launcher/launcher_platformBM.html",
                "ostype": "20"
            },
        ],
        "protocolList": "https://www.wanmei.com/safestatic/protocolList.html"   //设置-关于中，用户协议url
    },
    "oudConfig": {  //对象存储初始化参数，用于问题反馈时，上报日志
        "appId":"1001",
        "appKey":"d44aad69bb50d9bb321fa1298c1cdeed",
        "channelId":"9"
    },
    "analysisConfig": {  //统计SDK初始化参数
        "appId":"1001",
        "channelId":"9",
        "mediaId":"114"
    },
    "crashConfig": {     //CrashSDK初始化参数
        "appId":"1180042",
        "appKey":"b4ef3219495180d0e19bf76843324d41"
    }
}

```
### Podfile： 
- 用于修改是正常Launcher还是Benchmark版本
- 用于设置是Dev、Test、OB版本
通过在Pofile中设置环境，定义不同的宏，配合`WMLauncherUI.podspec`中`s.xcconfig `来实现兼容不同场景的包
    
```ruby
# archiveApp.sh打包时会获取环境(dev、test)和包名字段
ENV['SCENCE_ENV'] = 'dev'
#ENV['AREA_ENV'] = 'oversea' # 大陆 or 海外，会使用不同的统计配置文件
ENV['PACKAGE_NAME'] = 'ZXLauncher_BenchMark'
ENV['LAUNCHER_MACRO_ENV'] = 'LAUNCHER_TYPE_BENCHMARK' #其他需要定义的宏，没有默认为正式版
```

#### SCENCE_ENV
设置最终包面向的场景，podfile中不设置默认为OB版本

**可选**,默认为OB版本

| 环境变量值 | 使用场景  | 备注 |
| :--: | :--: | :--: | 
| dev | 内部开发环境 | 内部开发，可以回退资源|
| test | 正式资源的测试客户端 | 运营测试，可以回退资源 |
| ob | 正式玩家的诛仙世界客户端 | 玩家使用，资源不可回退 |

#### LAUNCHER_MACRO_ENV
实现不同类型包的其他特定宏，如有多个，用空格分离

**可选**,默认为正常包

| 环境变量值 | 使用场景  | 备注 |
| :--: | :--: | :--: | 
| LAUNCHER_TYPE_BENCHMARK | 用于前期测试游戏性能的Launcher版本，功能较简单 | 也会分dev、test、ob不同版本|

#### PACKAGE_NAME
**必选**

最终归档包时生成的dmg前缀名，比如`ZXLauncher_BenchMark_DEV_1.0.0_93cdb0f.dmg`,规则为`ZXLauncher_环境_版本号_gitCommitId.dmg`

#### AREA_ENV
**可选**,默认为大陆

| 环境变量值   | 备注 |
| :--: |:--: | 
| oversea |使用海外统计配置文件|


## 打包参数配置
### 需要注意的配置文件
不同环境会对应不同的配置文件，主要在项目/Release目录下

| 文件名  | 用途 | 需要修改的字段|
| :--: |:--: | :--: | 
|dmg-background.png|制作dmg的背景图||
|zhuxiansj.icns|制作dmg使用的图标||
|ExportOptions.plist|打包使用的证书、描述文件信息|provisioningProfiles 和 teamID|
|notarization.plist|对dmg进行公证使用的配置信息|CODE_SIGN_IDENTITY 和 KEYCHAIN_PROFILE|


#### dmg打包配置
修改Release/appdmg.json,重点关注以下字段：
- title: launcher安装器顶部显示的title，建议和Info.plist中`ProductName`设置一样的值。
- icon: launcher安装器里的图标，按需修改
- appName: 必须和Info.plist中`ProductName`一致
> {"contents":[{"x":182,"y":170,"type":"file","path":"诛仙世界BenchMark_Dev.app"}]}

#### Launcher自更新配置
Release/appcast.xml，不需要手动编辑，通过打包脚本传入`-u`会自动更新，每次对外发布后，将归档包中的appcast.xml更新到Release目录下即可，并提交git存档即可。

#### 签名信息设置
修改Release/ExportOptions.plist

#### dmg公证参数配置
修改Release/notarization.plist

## 打包命令
通过`Scripts/archiveApp.sh`完成打包归档，完成后，将在`Project workspace path`的`build/Archive`目录下。

```shell
usage() {
    echo "Usage: $0 [-p value] [-s value] [-j]  [-u] [-n]"
    echo
    echo "Options:"
    echo "  -p    Project workspace path"
    echo "  -s    Project scheme name"
    echo "  -j    Packaged with jenkins"
    echo "  -u    Generate the upgrade xml file"
    echo "  -n    Do notarization of the dmg"
    exit 1
}
```
**注意:** Jenkins上打包，必须添加-j参数，否则会无法生成`sonar`使用的`compile_commands.json`文件，导致静态分析报错。

打包示例:

- 归档Benchmark的Dev包，并更新launcher自更新配置，进行公证
    > ./Scripts/archiveApp.sh -p ./ZXLauncher_BenchMark_DEV -s ZXLauncher -n -u

- Jenkins归档LauncherDmeo的Dev包，并进行公证
    > ./Scripts/archiveApp.sh -p ./LauncherDemo -s ZhuXianWorldLanucher -n -j


# 版本更新
## SDK更新

- **WMMacPatcher、CrashSDK**：没单独提交到私有库，直接替换WMMacDependency目录对应文件，并更新`WMMacDependency.podspec`里的版本信息。
- **Sparkle**：没单独提交私有库，直接替换`Sparkle/Sparkle.framework`,版本号可看`Sparkle/CHANGELOG`。


## 测试Launcher内游戏版本的更新
**注意事项：**
- 必须文件`appcast.xml`和`*.dmg`
- 生成`appcast.xml`时，必须更新版本号(CFBundleShortVersionString) 和 build号(CFBundleVersion)，否则生成的`appcast.xml`会有问题

### Launcher Demo
Lancher支持多版本游戏(标准版、极速版、先遣版本等)的切换、安装等功能，可通过修改`ZhuXianWorldLanucher/Resources/launcherConfig.json`进行测试。

目前不同版本测试包，是通过在`dev后台` -> `OneSDK测试应用1001_国内(不含港澳台地区)` -> `客户端资源管理` -> `分支列表` -> `正式` 里的`OneSDKDemo-MacOS(OneSDKDemo)` 和 `LaohuDemo-MacOS(LaohuDemo)`两个分支进行测试。

如果要上传新版本，可到`客户端资源管理` -> `资源列表` 里通过`新增`,选择`OneSDKDemo-MacOS(OneSDKDemo)` 或 `LaohuDemo-MacOS(LaohuDemo)`创建新版本，并上传游戏包进行测试。

### 诛仙世界性能测试工具

Dev后台项目：诛仙世界性能测试工具_国内(不含港澳台地区)

分支：`客户端资源管理` -> `分支列表` -> `游戏自定义资源` 里的 `诛仙世界MacLauncher更新-OB`、`诛仙世界MacLauncher更新-Test`、`诛仙世界MacLauncher更新-DEV`三个分支
上传新版本：`客户端资源管理` -> `资源列表` -> `游戏自定义资源`里，选择需要的`OB`或`Test`或`DEV`分支名称，可查询当前已有版本。
右上角点新增后，创建新的版本号，并上传`appcast.xml`和`*.dmg`。如`appcast.xml`有问题，可再单独升级版本号后，单独更新`appcast.xml`文件。

## 测试Launcher自检测更新

- 执行`archive.sh`归档脚本后 (主要是generate_appcast工具)，会将新版本.dmg放到/Release目录，并且/Release/appcast.xml也会更新。

- 执行 `./quark-uploader-0.7.24-bin/uploadLauncher.sh ./Release`会将新版本上传到dev后台。

- 过一会，老版本可进行更新检测测试。

> Launcher的更新测试，使用的是`全球SDKDemo` - `客户端资源管理` -> `分支列表` -> `游戏自定义资源` 里的 `macupdate`分支。
> 除了通过uploadLauncher.sh脚本上传外，也可到`客户端资源管理` -> `资源列表` -> `游戏自定义资源`里自己创建新版本，并上传`appcast.xml`和`*.dmg`

## OB包获取对内资源进行测试

1. 获取设备ID
    打开`Launcher`，在对应分支游戏图标`右键` - `打开游戏目录`(单一版本的，左上角菜单里)，找到类似`log/log/ZX_GAT_MAC_Release.log`patcher日志，完整路径类似`/Users/<USER>/Library/Application\ Support/com.iwplay.gatzxsjmac.launcher/ZX_GAT_MAC_Release/log/log/ZX_GAT_MAC_Release.log`,搜索`got device id`
2. 将获取的deviceID在Dev后台添加为测试设备
3. 新建`testdevice.txt`，内容如下
    ```json
    # 不同游戏改一下 appid
    {"appid": "2000014", "internal": "true","whitelist":"https://download.wmupd.com:47317/"}
    ```
4. 将`testdevice.txt`放到`步骤1`中打开目录的`log/`目录下，类似`/Users/<USER>/Library/Application\ Support/com.iwplay.gatzxsjmac.launcher/ZX_GAT_MAC_Release/log`
5. 重新启动Launcher后，通过patcher日志能看到请求资源走的是`https://download.wmupd.com:47317`