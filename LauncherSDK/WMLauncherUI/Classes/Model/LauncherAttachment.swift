//
//  LauncherAttachment.swift
//  WMMacLauncherUI
//
//  Created by zhangjia on 2024/11/19.
//

import Foundation

enum LauncherAttachmentType: UInt {
    case add = 0
    case image
    case video
    case zip
}

class LauncherAttachment {
    var type: LauncherAttachmentType?
    
    var url: URL?
    var enable: Bool
    init(type: LauncherAttachmentType? = .add, url: URL? = nil, enable: Bool = true) {
        self.type = type
        self.url = url
        self.enable = enable
    }
}
