//
//  LauncherConfig.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/6.
//

import Foundation

struct LauncherConfigModel: Codable {
    let config: AppConfig
    let oudConfig: FeedbackOudConfig?
    let analysisConfig: AnalysisConfig?
    let crashConfig: CrashConfig?
}

struct AppConfig: Codable {
    let launcherName: String
    let multiConfig: [MultiConfig]!
    let protocolList: String
    let interceptUrl: [String]?
    let gameLogPath: String?

    enum CodingKeys: String, CodingKey {
        case launcherName
        case multiConfig
        case protocolList
        case interceptUrl
        case gameLogPath
    }
}

// multiConfig 数组中的结构
struct MultiConfig: Codable {
    let versionName: String
    let patcherConfig: String
    var client: String
    let clientCmdLineArgs: [String]?
    var clientDev: String?
    let clientDevCmdLineArgs: [String]?
    var clientTest: String?
    let clientTestCmdLineArgs: [String]?
    let cachePath: String
    let iconPath: String
    let bootstrapBundleId: String
    let bundleId: String
    let launcherWebPart: String
    let ostype: String?
    
    enum CodingKeys: String, CodingKey {
        case versionName
        case patcherConfig
        case client
        case clientDev
        case clientTest
        case cachePath
        case iconPath
        case bootstrapBundleId
        case bundleId
        case launcherWebPart = "launcherwebpart" // 自定义键名
        case ostype
    }
    
    // 计算属性，获取 cachePath 的 MD5 前 16 位
    var uniqueId: String {
        return cachePath.md5Prefix16()
    }
    
    // 自定义解码方法
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        self.versionName = try container.decode(String.self, forKey: .versionName)
        self.patcherConfig = try container.decode(String.self, forKey: .patcherConfig)
        self.client = try container.decode(String.self, forKey: .client)
        self.clientDev = try container.decodeIfPresent(String.self, forKey: .clientDev)
        self.clientTest = try container.decodeIfPresent(String.self, forKey: .clientTest)
        self.cachePath = try container.decode(String.self, forKey: .cachePath)
        self.iconPath = try container.decode(String.self, forKey: .iconPath)
        self.bootstrapBundleId = try container.decode(String.self, forKey: .bootstrapBundleId)
        self.bundleId = try container.decode(String.self, forKey: .bundleId)
        self.launcherWebPart = try container.decode(String.self, forKey: .launcherWebPart)
        self.ostype = try container.decodeIfPresent(String.self, forKey: .ostype)
        
        // 解析 client 和 clientArgs
        let clientParts = self.client.split(separator: " ").map { String($0) }
        if clientParts.count > 0 {
            self.client = clientParts[0] // 解析为 client
            self.clientCmdLineArgs = clientParts.count > 1 ? Array(clientParts[1...]) : nil // 剩余部分作为 clientArgs
        } else {
            self.clientCmdLineArgs = nil
        }
        
        if let tempClientDev = self.clientDev {
            let clientDevParts = tempClientDev.split(separator: " ").map { String($0) }
            if clientDevParts.count > 0 {
                self.clientDev = clientDevParts[0] // 解析为 client
                self.clientDevCmdLineArgs = clientDevParts.count > 1 ? Array(clientDevParts[1...]) : nil // 剩余部分作为 clientArgs
            } else {
                self.clientDevCmdLineArgs = nil
            }
        } else {
            self.clientDevCmdLineArgs = nil
        }
        
        if let tempClientTest = self.clientTest {
            let clientTestParts = tempClientTest.split(separator: " ").map { String($0) }
            if clientTestParts.count > 0 {
                self.clientTest = clientTestParts[0] // 解析为 client
                self.clientTestCmdLineArgs = clientTestParts.count > 1 ? Array(clientTestParts[1...]) : nil // 剩余部分作为 clientArgs
            } else {
                self.clientTestCmdLineArgs = nil
            }
        } else {
            self.clientTestCmdLineArgs = nil
        }
    }
}


// 启动目标应用的信息
struct LauncherAppConfig {
    var cacheBasePath: String = ""   //app下载的basePath
    var clientPath: String = ""      //.app应用程序所在路径
    var bootstrapBundleId: String = ""   //引导程序bundleId，应用标识
    var bundleId: String = ""        //bundleId，应用标识
    var patcherConfig: String = ""   //patcherConfig路径
    var resPath: String = ""         //patcher资源路径
    var patcherPath: String = ""     //patcher路径
    var logPath: String = ""         //patcher log路径
    var displayAppName: String? = nil  //支持多个App切换时，所选AppName
    var uniqueId: String = ""         //应用唯一标识md5(clientPath)
    var cmdLineArgs: [String]?        //启动app时命令行参数
}


// 对象存储初始化参数数据结构
struct FeedbackOudConfig: Codable {
    let appId: String
    let appKey: String
    let channelId: String
    let hostUrl: String
    let areaType: String
}

//统计SDK初始化参数数据结构
struct AnalysisConfig: Codable {
    let appId: String
    let channelId: String
    let mediaId: String?
}

// CrashSDK初始化参数数据结构
struct CrashConfig: Codable {
    let appId: String
    let appKey: String
}
