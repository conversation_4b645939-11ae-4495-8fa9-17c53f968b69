//
//  LauncherSettingsItemView.swift
//  WMLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/27.
//

import Foundation
import WMMasonry

enum CheckBoxButtonType: String {
    case playBgVideo = "play_bg_video"
    case noAutoUpdate = "no_auto_update"
    case autoUpdate = "auto_update"
    case unlimitedSpeed = "unlimited_speed"
    case limitedSpeed = "limited_speed"
    case defaultLine = "default_line"
    case otherLine = "other_line"
}

struct CheckBoxButtonConfig {
    let type: CheckBoxButtonType
    var isSelected: Bool
    var vaule: String?
    var extend: String?
}

class SettingsCategoryTableCellView: NSTableCellView {

    private let titleLabel: NSTextField
    private let flagImageView: NSImageView

    override init(frame frameRect: NSRect) {
        titleLabel = NSTextField(labelWithString: "")
        flagImageView = NSImageView()
        super.init(frame: frameRect)

        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.clear.cgColor
        
        titleLabel.isEditable = false
        titleLabel.isBordered = false
        titleLabel.drawsBackground = false
        titleLabel.textColor = NSColor.wmxkit_color(withHexString: "#BBBABF")
        titleLabel.font = NSFont.systemFont(ofSize: 24)
        addSubview(titleLabel)
        titleLabel.my_mas_makeConstraints { make in
            make?.center.offset()(0)
        }
        
        flagImageView.image = NSImage.load(inLauncherBundle: "setting_selected_flag")
        flagImageView.isHidden = true
        addSubview(flagImageView)
        flagImageView.my_mas_makeConstraints { make in
            make?.size.equalTo()(CGSizeMake(40, 40))
            make?.centerY.equalTo()(titleLabel)
            make?.left.equalTo()(titleLabel.my_mas_right)?.offset()(-10)
        }
    }
    
    func updateCell(title: String, isSelected: Bool) {
        if isSelected {
            titleLabel.textColor = NSColor.wmxkit_color(withHexString: "#EDDDBE")
            flagImageView.isHidden = false
        } else {
            titleLabel.textColor = NSColor.wmxkit_color(withHexString: "#BBBABF")
            flagImageView.isHidden = true
        }
        titleLabel.stringValue = title
    }
}


class SettingsSubMenuTableCellView: NSTableCellView {

    private let titleLabel: NSTextField
    private let contentLabel: NSTextField
    private let actionButton: LauncherCustomButton
    
    // 按钮点击回调
    var buttonActionCallback: ((LauncherSettingsType) -> Void)?

    // 当前的动作类型
    private var currentActionType: LauncherSettingsType?

    override init(frame frameRect: NSRect) {
        titleLabel = NSTextField(labelWithString: "")
        actionButton = LauncherCustomButton()
        contentLabel = NSTextField(labelWithString: "")
        super.init(frame: frameRect)

        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.clear.cgColor
        
        titleLabel.isEditable = false
        titleLabel.isBordered = false
        titleLabel.drawsBackground = false
        titleLabel.textColor = NSColor.wmxkit_color(withHexString: "#FEFEFE")
        titleLabel.font = NSFont.systemFont(ofSize: 22)
        addSubview(titleLabel)

        contentLabel.isEditable = false
        contentLabel.isBordered = false
        contentLabel.drawsBackground = false
        contentLabel.textColor = NSColor.wmxkit_color(withHexString: "#BBBABF")
        contentLabel.font = NSFont.systemFont(ofSize: 20)
        addSubview(contentLabel)
        
        actionButton.target = self
        actionButton.bezelColor = NSColor.clear
        actionButton.wantsLayer = true
        actionButton.layer?.backgroundColor = NSColor.clear.cgColor
        actionButton.imageScaling = .scaleProportionallyUpOrDown
        actionButton.action = #selector(buttonClicked)
        addSubview(actionButton)
        
        titleLabel.my_mas_makeConstraints { make in
            make?.left.offset()(20)
            make?.top.offset()(5)
        }
        
        contentLabel.my_mas_makeConstraints { make in
            make?.left.equalTo()(titleLabel)
            make?.centerY.equalTo()(actionButton)
        }
        
        actionButton.my_mas_makeConstraints { make in
            make?.left.equalTo()(contentLabel.my_mas_right)
            make?.height.mas_equalTo()(50)
            make?.width.mas_greaterThanOrEqualTo()(150)
            make?.top.equalTo()(titleLabel.my_mas_bottom)?.offset()(6)
        }
    }
    
    // 配置内容
    func configure(with actionType: LauncherSettingsType, callback: @escaping (LauncherSettingsType) -> Void) {
        // 设置标题和按钮内容
        currentActionType = actionType
        buttonActionCallback = callback
        
        if actionType == .version {
            contentLabel.stringValue = getAppVersionAndBuild()
        } else {
            contentLabel.stringValue = ""
        }
        
        let titles = actionType.titles
        titleLabel.stringValue = titles.title
        
        if actionType == .about {
            actionButton.updateStateImage(normal: nil)
            
            let normalColor = NSColor.wmxkit_color(withHexString: "#EDDDBE")
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.alignment = .left
            let attributedTitle = NSMutableAttributedString(string: titles.buttonTitle)
            let range = NSRange(location: 0, length: attributedTitle.length)
            attributedTitle.addAttributes([
                .underlineStyle: NSUnderlineStyle.single.rawValue,  // 单线下划线
                .underlineColor: normalColor!.cgColor,              // 下划线颜色
                .foregroundColor: normalColor!.cgColor,             // 文字颜色
                .paragraphStyle: paragraphStyle,
                .font: NSFont.systemFont(ofSize: 20)
            ], range: range)
            actionButton.attributedTitle = attributedTitle
            
        } else {
            let normalImage = NSImage.load(inLauncherBundle: "setting_btn_normal")!
            actionButton.updateStateImage(normal: normalImage, hover: nil, pressed: nil)
            
            let normalColor = NSColor.wmxkit_color(withHexString: "#FFFFFF")
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.alignment = .center
            let attributedTitle = NSMutableAttributedString(string: titles.buttonTitle)
            let range = NSRange(location: 0, length: attributedTitle.length)
            attributedTitle.addAttributes([
                .foregroundColor: normalColor!.cgColor,
                .paragraphStyle: paragraphStyle,
                .font: NSFont.systemFont(ofSize: 20)
            ], range: range)
            actionButton.attributedTitle = attributedTitle
        }
    }

    
    func getAppVersionAndBuild() -> String {
        let version = Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "UnknownVersion"

        let build = Bundle.main.object(forInfoDictionaryKey: "CFBundleVersion") as? String ?? "UnknownBuild"
        // 合并版本号和构建号
        let appVersion = String(format: LauncherLocalization.localizedString(forKey: "app_version"), version, build)
        return appVersion
    }
    
    // 按钮点击事件
    @objc private func buttonClicked() {
        guard let actionType = currentActionType else { return }
        buttonActionCallback?(actionType)
    }
}


// MARK: - 选择框TableCell

class SettingsCheckBoxTableCellView: NSTableCellView {
    private let titleLabel: NSTextField = NSTextField(labelWithString: "")
    private var inputTextField: NSTextField?
    private var buttons: [LauncherImageTextButton] = []
    private var allowsMultipleSelection: Bool = false
    private var stackView: NSStackView!
    private var buttonConfigs: [CheckBoxButtonConfig] = []
    var selectionChangedHandler: (([CheckBoxButtonConfig]) -> Void)?
    
    private let textColor = NSColor.wmxkit_color(withHexString: "#BBBABF")!
    private let singleNormalImage = NSImage.load(inLauncherBundle: "single_choice_normal")!
    private let singleHoverImage = NSImage.load(inLauncherBundle: "single_choice_normal_hover")!
    private let singleSelectedImage = NSImage.load(inLauncherBundle: "single_choice_selected")!
    private let singleSelectedHoverImage = NSImage.load(inLauncherBundle: "single_choice_selected_hover")!
    
    private let multipleNormalImage = NSImage.load(inLauncherBundle: "multiple_choice_normal")!
    private let multipleHoverImage = NSImage.load(inLauncherBundle: "multiple_choice_normal_hover")!
    private let multipleSelectedImage = NSImage.load(inLauncherBundle: "multiple_choice_selected")!
    private let multipleSelectedHoverImage = NSImage.load(inLauncherBundle: "multiple_choice_selected_hover")!
    
    private var stackViewTopConstraint:WMMASConstraint?
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.clear.cgColor
        
        titleLabel.isEditable = false
        titleLabel.isBordered = false
        titleLabel.drawsBackground = false
        titleLabel.textColor = NSColor.wmxkit_color(withHexString: "#FEFEFE")
        titleLabel.font = NSFont.systemFont(ofSize: 22)
        addSubview(titleLabel)
        titleLabel.my_mas_makeConstraints { make in
            make?.left.offset()(20)
            make?.top.offset()(5)
        }
        
        stackView = NSStackView()
        stackView.orientation = .horizontal
        stackView.alignment = .left
        addSubview(stackView)
        
        stackView.my_mas_makeConstraints { make in
            make?.left.equalTo()(titleLabel.my_mas_left)
            self.stackViewTopConstraint = make?.top.equalTo()(titleLabel.my_mas_bottom)?.offset()(8)
            make?.right.offset()(-20)
            make?.bottom.offset()(0)
        }
    }
    
    private func getInputTextView() -> NSTextField {
        let speedInputText = NSTextField()
        speedInputText.isEditable = false
        speedInputText.isBordered = false
        speedInputText.focusRingType = .none
        speedInputText.textColor = NSColor.wmxkit_color(withHexString: "#8E8E8E")!
        speedInputText.font = NSFont.systemFont(ofSize: 20.0)
        speedInputText.backgroundColor = NSColor.black
        speedInputText.layer?.borderWidth = 0.5
        speedInputText.layer?.borderColor = NSColor.init(white: 1.0, alpha: 0.5).cgColor
        speedInputText.delegate = self
        return speedInputText
    }
    
    func configure(with buttonConfigs: [CheckBoxButtonConfig], actionType: LauncherSettingsType, allowsMultipleSelection: Bool, isVertical: Bool) {
        self.buttonConfigs = buttonConfigs
        self.allowsMultipleSelection = allowsMultipleSelection
        stackView.orientation = isVertical ? .vertical : .horizontal
        stackView.spacing = isVertical ? 0 : 50
        let titles = actionType.titles
        titleLabel.stringValue = titles.title
        
        for (index, config) in buttonConfigs.enumerated() {
            let button: LauncherImageTextButton
            if index < buttons.count {
                button = buttons[index]
                button.isHidden = false
            } else {
                button = LauncherImageTextButton()
                button.target = self
                button.action = #selector(checkboxButtonClicked(_:))
                buttons.append(button)
                stackView.addArrangedSubview(button)
            }
            
            if allowsMultipleSelection {
                button.setImages(normal: multipleNormalImage, normalHover: multipleHoverImage, selected: multipleSelectedImage, selectedHover: multipleSelectedHoverImage)
                self.stackViewTopConstraint?.offset()(15)
            } else {
                button.setImages(normal: singleNormalImage, normalHover: singleHoverImage, selected: singleSelectedImage, selectedHover: singleSelectedHoverImage)
                self.stackViewTopConstraint?.offset()(8)
            }
            
            if config.type == .limitedSpeed {
                inputTextField = getInputTextView()
                inputTextField!.frame = CGRect(x: 50, y: 12, width: 80, height: 24)
                inputTextField!.isEditable = config.isSelected
                if let speedValue = config.vaule, !speedValue.isEmpty {
                    inputTextField?.stringValue = speedValue
                } else {
                    inputTextField?.stringValue = "4096"
                }
                button.addSubview(inputTextField!)
            } else {
                inputTextField?.removeFromSuperview()
                inputTextField = nil
            }
            
            var titleValue = LauncherLocalization.localizedString(forKey: config.type.rawValue) 
            if config.type == .otherLine {
                titleValue = String(format: "%@%@",titleValue, config.extend ?? "")
            }
            button.setCustomTitle(titleValue, font: NSFont.systemFont(ofSize: 20), color: textColor)
            button.setSelected(config.isSelected)
            button.tag = index
        }
        
        if buttons.count > buttonConfigs.count {
            for i in buttonConfigs.count..<buttons.count {
                buttons[i].isHidden = true
            }
        }
    }
    
    @objc private func checkboxButtonClicked(_ sender: LauncherImageTextButton) {
        let index = sender.tag
        guard index < buttonConfigs.count else { return }
        
        if !allowsMultipleSelection {
            for (i, button) in buttons.enumerated() {
                let isSelected = (i == index)
                button.setSelected(isSelected)
                buttonConfigs[i].isSelected = isSelected
                
                let config = buttonConfigs[i]
                if config.type == .limitedSpeed {
                    inputTextField?.isEditable = isSelected
                    if isSelected {
                        buttonConfigs[index].vaule = inputTextField?.stringValue
                        inputTextField?.becomeFirstResponder()
                    } else {
                        self.window?.makeFirstResponder(nil)
                        inputTextField?.resignFirstResponder()
                    }
                }
            }
        } else {
            let newState = !sender.isSelectedState()
            sender.setSelected(newState)
            buttonConfigs[index].isSelected = newState
        }
        
        selectionChangedHandler?(buttonConfigs)
    }
}

extension SettingsCheckBoxTableCellView: NSTextFieldDelegate {
    func controlTextDidChange(_ obj: Notification) {
        if let textField = obj.object as? NSTextField {
            var filtered = textField.stringValue.filter { $0.isNumber }
            if let number = Int(filtered), number > 40960 {
                filtered = "40960"
            }
            textField.stringValue = filtered
            if let superButton = textField.superview as? LauncherImageTextButton {
                if superButton.tag < buttonConfigs.count {
                    buttonConfigs[superButton.tag].vaule = textField.stringValue
                    selectionChangedHandler?(buttonConfigs)
                }
            }
            
        }
    }
}
