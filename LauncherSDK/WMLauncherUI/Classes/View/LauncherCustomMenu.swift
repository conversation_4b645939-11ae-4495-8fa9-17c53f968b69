//
//  LauncherCustomMenuItem.swift
//  WMMacLauncherUI
//
//  Created by zhangjia on 2024/11/13.
//

import Cocoa
import WMMasonry
import WMCategories
import WMXAppKit

class LauncherCustomMenu: NSView {
    public var menuItemWidth: CGFloat = 124
    public var menuItemHeight: CGFloat = 36
    private var containerView: WMXOverlayContainerView!
    private let stackView = NSStackView()
    private var callbackHandler: ((AppOperateType) -> Void)?
    public var viewHeight = 6.0
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.black.cgColor
        layer?.cornerRadius = 4.0
        
        stackView.orientation = .vertical
        stackView.spacing = 0
        addSubview(stackView)
        stackView.my_mas_makeConstraints { make in
            make?.left.offset()(0)
            make?.right.offset()(0)
            make?.top.offset()(viewHeight/2.0)
            make?.bottom.offset()((viewHeight/2.0 * -1.0))
        }
    }
    
    public func addItem(type: AppOperateType, handler: ((AppOperateType) -> Void)? = nil) {
        
        callbackHandler = handler
        let normalBackgroundColor: NSColor = NSColor.wmxkit_color(withHexString: "#000000", alpha: 1.0)
        let hoverBackgroundColor: NSColor = NSColor.wmxkit_color(withHexString: "#59513C", alpha: 1.0)
        let pressBackgroundColor: NSColor = NSColor.wmxkit_color(withHexString: "#28261f", alpha: 1.0)
        let normalTextColor: NSColor = NSColor.wmxkit_color(withHexString: "#FFFFFF", alpha: 1.0)
        
        let menuItem = LauncherCustomButton()
        menuItem.target = self
        menuItem.action = #selector(menuItemClicked(_:))
        menuItem.bezelColor = NSColor.clear
        menuItem.wantsLayer = true
        menuItem.layer?.backgroundColor = NSColor.clear.cgColor
        menuItem.font = NSFont.systemFont(ofSize: 16)
        menuItem.title = type.description()

        menuItem.updateStateBackgroundColor(normal: normalBackgroundColor, hover: hoverBackgroundColor, pressed: pressBackgroundColor)
        menuItem.updateTitleColor(normal: normalTextColor)
        menuItem.tag = type.rawValue
        stackView.addArrangedSubview(menuItem)
        menuItem.my_mas_makeConstraints { make in
            make?.left.offset()(0)
            make?.right.offset()(0)
            make?.height.equalTo()(menuItemHeight)
        }
        viewHeight += menuItemHeight
    }
    
    @objc private func menuItemClicked(_ sender: NSButton) {
        if let type = AppOperateType(rawValue: sender.tag) {
            callbackHandler?(type)
        }
    }
    
    //MARK: 显示隐藏方法
    public func show(inView: NSView?, topOffset: CGFloat?) {
        var alertSuperView = inView
        if alertSuperView == nil {
            let showWindow = NSWindow.wmxkit_applicationKey()
            alertSuperView = showWindow.contentView
        }
        
        containerView = WMXOverlayContainerView.init(frame: alertSuperView!.bounds)
        containerView.delegate = self
        containerView.backgroundColor = NSColor.clear
        containerView.addSubview(self)
        self.my_mas_makeConstraints { make in
            make?.width.mas_equalTo()(self.menuItemWidth)
            make?.top.offset()(topOffset ?? 0)
            make?.right.offset()(-5.0)
        }
        alertSuperView?.addSubview(containerView)
        containerView.my_mas_makeConstraints { make in
            make?.edges.offset()(0)
        }
        //成为第一响应者，使其相应键盘事件
        window?.makeFirstResponder(self)
    }
    
    public func dismiss() {
        self.removeFromSuperview()
        containerView.removeFromSuperview()
        for view in stackView.arrangedSubviews {
            view.removeFromSuperview()
        }
        stackView.removeFromSuperview()
        callbackHandler = nil
    }
    
    override func keyDown(with event: NSEvent) {
        if event.keyCode == 53 { // ESC 键的 keyCode 是 53
            dismiss()
            return
        }
        super.keyDown(with: event)
    }
}

extension LauncherCustomMenu: WMXOverlayContainerViewDelegate {
    func backgroundViewDidTapped() {
        dismiss()
    }
}


