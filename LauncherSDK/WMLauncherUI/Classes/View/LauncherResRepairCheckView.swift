//
//  LauncherResRepairCheckView.swift
//  WMMacLauncherUI
//
//  Created by z<PERSON>ji<PERSON> on 2024/12/9.
//

import Foundation

extension LauncherResRepairCheckView {
    static func show(cancelHandler: (() -> Void)? = nil) -> (LauncherResRepairCheckView,LauncherCommonAlertView) {
        let resRepairCheckView = LauncherResRepairCheckView()
        let alertCustomView : LauncherAlertCustom = LauncherAlertCustom.init()
        alertCustomView.setup(mainView: resRepairCheckView)
        alertCustomView.constraint =  LauncherConstraint(top: 32, left: 0, right: 0, height: 130)
        
        let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "one_key_repair"), message: nil)
        alertView.add(cutsomView: alertCustomView)
        alertView.addDismissButton {
            LauncherResRepairCheckView.showCancelAlert(checkResView: alertView, cancelHandler: cancelHandler)
        }
        alertView.show(inView: nil)
        return (resRepairCheckView,alertView)
    }
    
    private static func showCancelAlert(checkResView:LauncherCommonAlertView, cancelHandler: (() -> Void)? = nil) {
        let cancelAlertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "cancel_repair"), message: LauncherLocalization.localizedString(forKey: "game_file_integrity_check_not_complete"))
        cancelAlertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "confirm_cancel"), handler: { action in
            checkResView.dismiss()
            cancelHandler?()
        }))
        cancelAlertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "continue_repair"), handler: { action in
      
        }))
        cancelAlertView.show(inView: nil)
    }
}

class LauncherResRepairCheckView: NSView {
    let fileCountImageView = NSImageView()
    let fileCountLabel = NSTextField(labelWithString: LauncherLocalization.localizedString(forKey: "checking_game_file_integrity"))
    
    let timeImageView = NSImageView()
    let timeLabel = NSTextField(labelWithString: LauncherLocalization.localizedString(forKey: "estimated_remaining_time_calculating"))
    
    let progressIndicator = LauncherProgressIndicator()
    let progressLable = NSTextField(labelWithString: "")
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupCheckView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupCheckView()
    }

    private func setupCheckView() {
        
        wantsLayer = true
        layer?.backgroundColor = NSColor.clear.cgColor
        
        fileCountImageView.image = NSImage.load(inLauncherBundle: "repair_check_icon")
        addSubview(fileCountImageView)
        fileCountLabel.font = NSFont.systemFont(ofSize: 16)
        fileCountLabel.textColor = NSColor.wmxkit_color(withHexString: "#878787")
        addSubview(fileCountLabel)
        
        timeImageView.image = NSImage.load(inLauncherBundle: "repair_time_icon")
        addSubview(timeImageView)
        timeLabel.font = NSFont.systemFont(ofSize: 16)
        timeLabel.textColor = NSColor.wmxkit_color(withHexString: "#878787")
        addSubview(timeLabel)
        
        progressIndicator.backgroundColor = NSColor.wmxkit_color(withHexString: "#878787")
        progressIndicator.progressColor = NSColor.wmxkit_color(withHexString: "#AA753E")
        addSubview(progressIndicator)
        
        progressLable.font = NSFont.systemFont(ofSize: 16)
        progressLable.textColor = NSColor.wmxkit_color(withHexString: "#878787")
        addSubview(progressLable)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        let edgeOffset = 26.0
        fileCountImageView.my_mas_makeConstraints { make in
            make?.top.offset()(0)
            make?.size.mas_equalTo()(CGSizeMake(21, 20))
            make?.left.offset()(edgeOffset)
        }
        fileCountLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(fileCountImageView.my_mas_centerY)
            make?.left.equalTo()(fileCountImageView.my_mas_right)?.offset()(edgeOffset)
            make?.right.offset()(edgeOffset*(-1.0))
        }
        timeImageView.my_mas_makeConstraints { make in
            make?.top.equalTo()(fileCountImageView.my_mas_bottom)?.offset()(10)
            make?.size.mas_equalTo()(CGSizeMake(21, 20))
            make?.left.offset()(edgeOffset)
        }
        timeLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(timeImageView.my_mas_centerY)
            make?.left.equalTo()(timeImageView.my_mas_right)?.offset()(edgeOffset)
            make?.right.offset()(edgeOffset*(-1.0))
        }
        progressIndicator.my_mas_makeConstraints { make in
            make?.top.equalTo()(timeImageView.my_mas_bottom)?.offset()(40)
            make?.left.offset()(edgeOffset)
            make?.right.offset()(edgeOffset*(-1.0))
            make?.height.mas_equalTo()(7.0)
        }
        progressLable.my_mas_makeConstraints { make in
            make?.top.equalTo()(progressIndicator.my_mas_bottom)?.offset()(15)
            make?.right.equalTo()(progressIndicator)
        }
    }

    public func update(repairInfo: LauncherRepairCheckInfo) {
        fileCountLabel.stringValue = String(format: LauncherLocalization.localizedString(forKey: "checking_files"), repairInfo.checkedFileCount, repairInfo.totalFileCount)
        timeLabel.stringValue = String(format: LauncherLocalization.localizedString(forKey: "estimated_remaining_time"), repairInfo.estimatedTime)
        progressLable.stringValue = repairInfo.progressPercent
        progressIndicator.doubleValue = repairInfo.progress
    }
}

class LauncherProgressIndicator: NSView {
    var backgroundColor: NSColor = .lightGray // 背景颜色
    var progressColor: NSColor = .blue       // 进度条颜色
    var minValue: Double = 0.0               // 最小值
    var maxValue: Double = 100.0             // 最大值
    private var _doubleValue: Double = 0.0
    var doubleValue: Double {
        get {
            return _doubleValue
        }
        set {
            _doubleValue = max(minValue, min(newValue, maxValue))
            needsDisplay = true
        }
    }
    
    override func draw(_ dirtyRect: NSRect) {
        super.draw(dirtyRect)
        let rect = self.bounds
        // 绘制背景
        backgroundColor.setFill()
        rect.fill()
        
        // 计算进度条的宽度
        let progressWidth = doubleValue * rect.width
        
        // 绘制进度条
        let progressRect = NSRect(x: 0, y: 0, width: progressWidth, height: rect.height)
        progressColor.setFill()
        progressRect.fill()
    }
}
