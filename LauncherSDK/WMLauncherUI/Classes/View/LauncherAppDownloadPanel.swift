//
//  LauncherAppDownloadPanel.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/7.
//

import Foundation
import WMMasonry

class LauncherAppDownloadPanel: NSView {
    // 定义事件回调类型
    enum ActionType {
        case doing
        case waitPause
        case pause
        case waitResume
        case resume
        case waitCancel
        case cancel
        case resProcess
    }
    
    // 点击按钮事件响应回调
    var actionCallback: ((ActionType) -> Void)?
    private let continueDownloadLabel: NSTextField = createLabel(withText: "")
    private let downloadMainBgView = NSView()
    private let downloadStaticLabel: NSTextField = createLabel(withText: LauncherLocalization.localizedString(forKey: "downloading"))
    private let speedStaticLabel: NSTextField = createLabel(withText: LauncherLocalization.localizedString(forKey: "download_speed"))
    private let sizeStaticLabel: NSTextField = createLabel(withText: LauncherLocalization.localizedString(forKey: "download_size"))
    private let timeStaticLabel: NSTextField = createLabel(withText: LauncherLocalization.localizedString(forKey: "remaining_time"))
    private let versionStaticLabel: NSTextField = createLabel(withText: LauncherLocalization.localizedString(forKey: "current_version"))

    private let downloadLabel: NSTextField = createLabel(withText: "--")
    private let speedLabel: NSTextField = createLabel(withText: "--")
    private let sizeLabel: NSTextField = createLabel(withText: "--")
    private let timeLabel: NSTextField = createLabel(withText: "--:--:--")
    private let versionLabel: NSTextField = createLabel(withText: "--")
    
    // 操作按钮
    private let pauseButton: LauncherCustomButton = createButton(withTitle: LauncherLocalization.localizedString(forKey: "pause"))
    private let cancelButton: LauncherCustomButton = createButton(withTitle: LauncherLocalization.localizedString(forKey: "cancel"))
    private var downloadStatus:ActionType = .doing //下载、暂停状态
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupUI()
        setupActions()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
        setupActions()
    }
    
    private func setupUI() {
        // 添加所有子视图
        continueDownloadLabel.lineBreakMode = .byWordWrapping
        continueDownloadLabel.alignment = .left
        continueDownloadLabel.maximumNumberOfLines = 0
        addSubview(continueDownloadLabel)
        
        downloadStaticLabel.font = NSFont.systemFont(ofSize: 14)
        downloadLabel.font = NSFont.systemFont(ofSize: 14)
        downloadLabel.cell?.lineBreakMode = .byTruncatingHead
        
        downloadMainBgView.wantsLayer = true
        downloadMainBgView.layer?.backgroundColor = NSColor.clear.cgColor
        
        addSubview(downloadMainBgView)
        downloadMainBgView.addSubview(downloadStaticLabel)
        downloadMainBgView.addSubview(speedStaticLabel)
        downloadMainBgView.addSubview(sizeStaticLabel)
        downloadMainBgView.addSubview(timeStaticLabel)
        downloadMainBgView.addSubview(versionStaticLabel)
        
        downloadMainBgView.addSubview(downloadLabel)
        downloadMainBgView.addSubview(speedLabel)
        downloadMainBgView.addSubview(sizeLabel)
        downloadMainBgView.addSubview(timeLabel)
        downloadMainBgView.addSubview(versionLabel)
        
        addSubview(pauseButton)
        addSubview(cancelButton)
        
        // 设置约束
        continueDownloadLabel.my_mas_makeConstraints { make in
            make?.top.offset()(10)
            make?.left.offset()(22)
            make?.right.offset()(-15)
        }
        
        downloadMainBgView.my_mas_makeConstraints { make in
            make?.top.offset()(35)
            make?.left.offset()(0)
            make?.right.offset()(0)
            make?.bottom.offset()(-50)
        }
        
        downloadStaticLabel.my_mas_makeConstraints { make in
            make?.top.offset()(0)
            make?.left.offset()(22)
            make?.width.mas_equalTo()(68)
        }
        
        downloadLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(downloadStaticLabel)
            make?.left.equalTo()(downloadStaticLabel.my_mas_right)?.offset()(3)
            make?.right.offset()(-15)
        }
        
        speedStaticLabel.my_mas_makeConstraints { make in
            make?.top.equalTo()(downloadStaticLabel.my_mas_bottom)?.offset()(15)
            make?.left.equalTo()(downloadStaticLabel)
            make?.right.equalTo()(downloadStaticLabel)
        }
        
        speedLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(speedStaticLabel)
            make?.left.equalTo()(downloadLabel)
            make?.right.equalTo()(downloadLabel)
        }
        
        sizeStaticLabel.my_mas_makeConstraints { make in
            make?.top.equalTo()(speedStaticLabel.my_mas_bottom)?.offset()(5)
            make?.left.equalTo()(downloadStaticLabel)
            make?.right.equalTo()(downloadStaticLabel)
        }
        
        sizeLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(sizeStaticLabel)
            make?.left.equalTo()(downloadLabel)
            make?.right.equalTo()(downloadLabel)
        }
        
        timeStaticLabel.my_mas_makeConstraints { make in
            make?.top.equalTo()(sizeStaticLabel.my_mas_bottom)?.offset()(5)
            make?.left.equalTo()(downloadStaticLabel)
            make?.right.equalTo()(downloadStaticLabel)
        }
        
        timeLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(timeStaticLabel)
            make?.left.equalTo()(downloadLabel)
            make?.right.equalTo()(downloadLabel)
        }
        
        versionStaticLabel.my_mas_makeConstraints { make in
            make?.top.equalTo()(timeLabel.my_mas_bottom)?.offset()(5)
            make?.left.equalTo()(downloadStaticLabel)
            make?.right.equalTo()(downloadStaticLabel)
        }
        
        versionLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(versionStaticLabel)
            make?.left.equalTo()(downloadLabel)
            make?.right.equalTo()(downloadLabel)
        }
        
        cancelButton.my_mas_makeConstraints { make in
            make?.bottom.offset()(-16)
            make?.left.equalTo()(downloadStaticLabel)
            make?.size.mas_equalTo()(NSMakeSize(120, 30))
        }
        
        pauseButton.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(cancelButton)
            make?.right.equalTo()(downloadLabel)
            make?.size.equalTo()(cancelButton)
        }
    }
    
    // MARK: - 设置按钮动作
    private func setupActions() {
        pauseButton.target = self
        pauseButton.action = #selector(handlePauseButton)
        
        cancelButton.target = self
        cancelButton.action = #selector(handleCancelButton)
    }
    
    // 暂停按钮事件
    @objc private func handlePauseButton() {
        pauseButton.isEnabled = false
        cancelButton.isEnabled = false
        if downloadStatus == .doing {
            downloadStatus = .waitPause
            actionCallback?(.pause)
        } else {
            downloadStatus = .waitResume
            continueDownloadLabel.stringValue = LauncherLocalization.localizedString(forKey: "preparing_download")
            actionCallback?(.resume)
        }
    }
    
    public func changePauseButtonState(state : ActionType, downloadInfo: LauncherAppDownloadInfo? = nil) {
        downloadStatus = state
        pauseButton.isEnabled = true
        cancelButton.isEnabled = true
        if downloadStatus == .pause {
            downloadMainBgView.isHidden = false
            continueDownloadLabel.stringValue = LauncherLocalization.localizedString(forKey: "click_resume")
            speedLabel.stringValue = "--"
            timeLabel.stringValue = "--"
            pauseButton.title = LauncherLocalization.localizedString(forKey: "resume")
        } else if downloadStatus == .resProcess {
            if let latestInfo = downloadInfo, let fixResTotalSize = latestInfo.fixResTotalSize, let fixResFixedSize = latestInfo.fixResFixedSize {
                let percentage = Int64((Double(fixResFixedSize) / Double(fixResTotalSize) * 100).rounded())
                var fixResFileName = "--"
                if let fileName = latestInfo.fixResFile, !fileName.isEmpty {
                    fixResFileName = fileName
                }
                let message = String(format: LauncherLocalization.localizedString(forKey: "download_finish_waitting_fixRes"), percentage, fixResFileName)
                
                
                let attributedString = NSMutableAttributedString(string: message)
                
                let paragraphStyle = NSMutableParagraphStyle()
                let lineSpacing: CGFloat = 5
                paragraphStyle.lineSpacing = lineSpacing
            
                attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: attributedString.length))
                attributedString.addAttribute(.font, value: NSFont.systemFont(ofSize: 15), range: NSRange(location: 0, length: attributedString.length))
                attributedString.addAttribute(.foregroundColor, value: NSColor.white, range: NSRange(location: 0, length: attributedString.length))
                
                continueDownloadLabel.stringValue = ""
                continueDownloadLabel.attributedStringValue = attributedString
                
                downloadMainBgView.isHidden = true
            } else {
                downloadMainBgView.isHidden = false
                continueDownloadLabel.attributedStringValue = NSAttributedString(string: "")
                continueDownloadLabel.stringValue = LauncherLocalization.localizedString(forKey: "download_finish_waitting")
            }
            pauseButton.title = LauncherLocalization.localizedString(forKey: "pause")
            pauseButton.isEnabled = false
            cancelButton.isEnabled = false
        } else  {
            downloadMainBgView.isHidden = false
            continueDownloadLabel.stringValue = ""
            pauseButton.title = LauncherLocalization.localizedString(forKey: "pause")
        }

    }
    
    // 取消按钮事件
    @objc private func handleCancelButton() {
        pauseButton.isEnabled = false
        cancelButton.isEnabled = false
        continueDownloadLabel.stringValue = ""
        downloadStatus = .waitCancel
        actionCallback?(.cancel)
    }
    
    // MARK: - 更新接口
    func updateProgress(_ downloadInfo: LauncherAppDownloadInfo?, status: LauncherAppStatus) {
        if let info = downloadInfo {
            updateDownloadTips(info.appVersionName)
            updateSpeed(info.speed)
            let downloadProgress = "\(info.downloadedSize)/\(info.totalSize)  \(info.progress)"
            updateSize(downloadProgress)
            updateTime(info.remainingTime)
            updateVersion(info.localVersion)
        }
        
        if status == .pauseDownloading {
            changePauseButtonState(state : .pause)
        } else if status == .resProcessing {
            changePauseButtonState(state : .resProcess, downloadInfo: downloadInfo)
        } else if status != .pauseDownloading && downloadStatus != .waitPause {
            changePauseButtonState(state : .doing)
        }
    }
    
    private func updateDownloadTips(_ tips: String) {
        self.downloadLabel.stringValue = tips
    }
    
    private func updateSpeed(_ speed: String) {
        speedLabel.stringValue = speed
    }
    
    private func updateSize(_ size: String) {
        sizeLabel.stringValue = size
    }
    
    private func updateTime(_ time: String) {
        timeLabel.stringValue = time
    }
    
    private func updateVersion(_ version: String?) {
        if let tempVersion = version, !tempVersion.isEmpty {
            versionStaticLabel.isHidden = false
            versionLabel.isHidden = false
            versionLabel.stringValue = tempVersion
        } else {
            versionStaticLabel.isHidden = true
            versionLabel.isHidden = true
        }
    }
    
    // MARK: - 工具方法
    private static func createLabel(withText text: String) -> NSTextField {
        let label = NSTextField(labelWithString: text)
        label.font = NSFont.systemFont(ofSize: 13)
        label.textColor = .white
        return label
    }
    
    private static func createButton(withTitle title: String) -> LauncherCustomButton {
        let button = LauncherCustomButton()
        button.target = self
        button.bezelColor = NSColor.clear
        button.wantsLayer = true
        button.layer?.backgroundColor = NSColor.clear.cgColor
        button.font = NSFont.systemFont(ofSize: 15)
        
        let normalImage = NSImage.load(inLauncherBundle: "dark_btn_bg_normal")!
        let hoverImage = NSImage.load(inLauncherBundle: "dark_btn_bg_hover")!
        let pressedImage = NSImage.load(inLauncherBundle: "dark_btn_bg_pressed")!
        
        button.updateStateImage(normal: normalImage, hover: hoverImage, pressed: pressedImage)
        
        let normalColor = NSColor.wmxkit_color(withHexString: "#d3d3d3")
        let hoverColor = NSColor.wmxkit_color(withHexString: "#cdcdcd")
        let pressedColor = NSColor.wmxkit_color(withHexString: "#d3d3d3")
        button.updateTitleColor(normal: normalColor, hover: hoverColor, pressed: pressedColor)
        
        button.title = title
        return button
    }
}
