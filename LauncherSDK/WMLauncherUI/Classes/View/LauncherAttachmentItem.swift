//
//  LauncherAttachmentItem.swift
//  Launcher
//
//  Created by 代云兴 on 2023/11/6.
//

import Cocoa

protocol LauncherAttachmentItemDelegate: NSObjectProtocol {
    func deleteAttachment(_ attachment: LauncherAttachment!)
}

class LauncherAttachmentItem: NSCollectionViewItem {
    weak var delegate: LauncherAttachmentItemDelegate?
    override func loadView() {
        self.view = NSView()
    }

    private lazy var iconView: NSImageView = {
        let view = NSImageView(frame: CGRect(x: 0.0, y: 0.0, width: 14.0, height: 14.0))
        view.layer?.backgroundColor = NSColor.clear.cgColor
        return view
    }()
    
    private lazy var nameLabel: NSTextField = {
        let view = NSTextField()
        view.backgroundColor = .clear
        view.textColor = NSColor.wmxkit_color(withHexString: "#FFFFFF")
        view.font = NSFont.systemFont(ofSize: 18.0)
        view.alignment = .left
        view.lineBreakMode = .byTruncatingTail
        view.isEditable = false
        view.isBordered = false
        return view
    }()
    
    private lazy var deleteButton: NSButton = {
        let view = NSButton(image: NSImage.load(inLauncherBundle: "attachment_delete")!,
                            target: self,
                            action: #selector(deleteButtonDidTap(_:)))
        view.isBordered = false
        view.layer?.backgroundColor = NSColor.clear.cgColor
        return view
    }()
    
    var attachment: LauncherAttachment!
    
    override init(nibName nibNameOrNil: NSNib.Name?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.commonInit()
    }
    
    func configItemWithAttachment(_ attachment: LauncherAttachment!) {
        self.attachment = attachment
        
        switch attachment.type {
        case .image:
            self.iconView.image = NSImage.load(inLauncherBundle: "attachment_picture")
        case .video:
            self.iconView.image = NSImage.load(inLauncherBundle: "attachment_video")
        case .zip:
            self.iconView.image = NSImage.load(inLauncherBundle: "attachment_zip")
        default:
            break
        }
        
        if let url = attachment.url,let nsurl = url as NSURL? {
            self.nameLabel.stringValue = nsurl.lastPathComponent ?? ""
        }
    }
    // MARK: -
    @objc func deleteButtonDidTap(_ sender: AnyObject!) {
        delegate?.deleteAttachment(self.attachment)
    }
    // MARK: -
    func commonInit() {
        self.view.wantsLayer = true
        self.view.layer?.backgroundColor = NSColor.wmxkit_color(withHexString: "#2B2F38").cgColor
        self.view.layer?.cornerRadius = 16.0
        
        self.view.addSubview(self.iconView)
        self.view.addSubview(self.nameLabel)
        self.view.addSubview(self.deleteButton)
        
        self.iconView.my_mas_makeConstraints { make in
            make?.left.offset()(20)
            make?.centerY.offset()(0)
            make?.width.equalTo()(16)
            make?.height.equalTo()(16)
        }
        
        self.nameLabel.my_mas_makeConstraints { make in
            make?.left.equalTo()(iconView.my_mas_right)?.offset()(10)
            make?.centerY.offset()(0)
            make?.right.lessThanOrEqualTo()(deleteButton.my_mas_left)?.offset()(-20)
        }
        
        self.deleteButton.my_mas_makeConstraints { make in
            make?.right.offset()(-20)
            make?.centerY.offset()(0)
            make?.width.equalTo()(16)
            make?.height.equalTo()(16)
        }
    }
}
