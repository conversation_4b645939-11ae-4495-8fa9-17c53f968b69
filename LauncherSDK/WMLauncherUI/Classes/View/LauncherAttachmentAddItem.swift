//
//  LauncherAttachmentAddItem.swift
//  Launcher
//
//  Created by 代云兴 on 2023/11/6.
//

import Cocoa
import AppKit
import WMMasonry

@objc protocol LauncherAttachmentAddItemDelegate: NSObjectProtocol {
    func addAttachment()
}

class LauncherAttachmentAddItem: NSCollectionViewItem {
    @objc weak var delegate: LauncherAttachmentAddItemDelegate?
    override func loadView() {
        self.view = NSView()
    }
    
    @objc var enable: Bool = true {
        didSet {
            DispatchQueue.main.async {
                if self.enable {
                    self.view.layer?.backgroundColor = NSColor.wmxkit_color(withHexString: "#C3CAD6", alpha: 0.04).cgColor
                    self.view.layer?.cornerRadius = 16.0
                    self.view.layer?.borderWidth = 1.0
                    self.view.layer?.borderColor = NSColor.wmxkit_color(withHexString: "#989CA5", alpha: 0.22).cgColor
                    
                    let image: NSImage! = NSImage.load(inLauncherBundle: "attachment_add_normal")
                    
                    self.iconView.image = image
                    self.nameLabel.textColor = NSColor.wmxkit_color(withHexString: "#FFFFFF")
                } else {
                    self.view.layer?.backgroundColor = NSColor.wmxkit_color(withHexString: "#2D3139").cgColor
                    self.view.layer?.cornerRadius = 16.0
                    self.view.layer?.borderWidth = 0.0
                    
                    let image: NSImage! = NSImage.load(inLauncherBundle: "attachment_add_disable")
                    
                    self.iconView.image = image
                    self.nameLabel.textColor = NSColor.wmxkit_color(withHexString: "#969EAF", alpha: 0.2)
                }
                
                self.view.displayIfNeeded()
            }
        }
    }
    
    private lazy var leftView: NSView = {
        let view = NSView()
        view.layer?.backgroundColor = .clear
        return view
    }()
    
    private lazy var rightView: NSView = {
        let view = NSView()
        view.layer?.backgroundColor = .clear
        return view
    }()
    
    
    private lazy var iconView: NSImageView = {
        return NSImageView()
    }()
    
    private lazy var nameLabel: NSTextField = {
        let view = NSTextField()
        view.font = NSFont.systemFont(ofSize: 18.0)
        view.stringValue = LauncherLocalization.localizedString(forKey: "add_attachment")
        view.isBordered = false
        view.drawsBackground = false
        view.isEditable = false
        
        return view
    }()
    
    override init(nibName nibNameOrNil: NSNib.Name?, bundle nibBundleOrNil: Bundle?) {
        super.init(nibName: nibNameOrNil, bundle: nibBundleOrNil)
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        self.enable = true
        
        view.addSubview(leftView)
        view.addSubview(rightView)
        view.addSubview(iconView)
        view.addSubview(nameLabel)
        
        let gesture = NSClickGestureRecognizer(target: self, action: #selector(addButtonDidTap(_:)))
        self.view.addGestureRecognizer(gesture)
        
        leftView.my_mas_makeConstraints { make in
            make?.left.offset()(0)
            make?.height.equalTo()(1)
            make?.bottom.offset()(0)
            make?.width.equalTo()(rightView)
        }
        
        rightView.my_mas_makeConstraints { make in
            make?.right.offset()(0)
            make?.height.equalTo()(1)
            make?.bottom.offset()(0)
            make?.width.equalTo()(leftView)
        }
        
        iconView.my_mas_makeConstraints { make in
            make?.left.equalTo()(leftView.my_mas_right)
            make?.centerY.offset()(0)
            make?.width.equalTo()(16)
            make?.height.equalTo()(16)
        }
        
        nameLabel.my_mas_makeConstraints { make in
            make?.left.equalTo()(iconView.my_mas_right)?.offset()(2)
            make?.right.equalTo()(rightView.my_mas_left)
            make?.width.equalTo()(100)
            make?.centerY.offset()(0)
        }
    }
    
    override func viewWillLayout() {
        super.viewWillLayout()
    }
    
    @objc func addButtonDidTap(_ sender: AnyObject!) {
        if self.enable {
            delegate?.addAttachment()
        }
    }
}
