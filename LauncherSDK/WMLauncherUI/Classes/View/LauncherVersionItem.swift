//
//  LauncherVersionItem.swift
//  Launcher
//
//  Created by 代云兴 on 2023/11/3.
//

import Foundation
import WMMasonry

class VersionCustomTextField: NSTextField {
    public var becomeFirstResponderHandler: (() -> Void)?
    
    override func becomeFirstResponder() -> Bool {
        let didBecomeFirstResponder = super.becomeFirstResponder()
        if didBecomeFirstResponder {
            becomeFirstResponderHandler?()
        }
        return didBecomeFirstResponder
    }
}

class LauncherVersionItem: NSCollectionViewItem {
    public var inputHandler: (() -> Void)?
    
    override func loadView() {
        self.view = NSView()
    }

    private lazy var selecteView: NSImageView = {
        let selecteView = NSImageView()
        selecteView.layer?.backgroundColor = NSColor.clear.cgColor
        selecteView.image = NSImage.load(inLauncherBundle: "single_choice_normal")
        return selecteView
    }()
    
    
    public lazy var versionLabel: VersionCustomTextField = {
        let versionLabel = VersionCustomTextField()
        versionLabel.isEditable = false
        versionLabel.isBordered = false
        versionLabel.textColor = NSColor.white
        versionLabel.font = NSFont.systemFont(ofSize: 20.0)
        versionLabel.backgroundColor = NSColor.clear
        versionLabel.becomeFirstResponderHandler = {[weak self] in
            self?.inputHandler?()
            self?.isSelected = true
        }
        return versionLabel
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        commonInit()
    }
    
    @objc func configVersion(_ version: String!, selected isSelected: Bool, eitable: Bool) {
        self.isSelected = isSelected
        versionLabel.isEditable = eitable
        if eitable {
            versionLabel.stringValue = ""
            versionLabel.isBordered = true
            versionLabel.placeholderString = LauncherLocalization.localizedString(forKey: "enter_version_number")
            versionLabel.delegate = self
        } else {
            versionLabel.stringValue = version
            versionLabel.isBordered = false
            versionLabel.placeholderString = ""
            versionLabel.delegate = nil
        }
    }
    
    override var isSelected: Bool {
        didSet {
            if isSelected {
                selecteView.image = NSImage.load(inLauncherBundle: "single_choice_selected")
            } else {
                selecteView.image = NSImage.load(inLauncherBundle: "single_choice_normal")
            }
        }
    }
    
    func commonInit() {
        view.addSubview(selecteView)
        view.addSubview(versionLabel)
        
        selecteView.my_mas_makeConstraints { make in
            make?.left.offset()(30)
            make?.centerY.offset()(0)
            make?.width.mas_equalTo()(48.0)
            make?.height.mas_equalTo()(48.0)
        }
        
        versionLabel.my_mas_makeConstraints { make in
            make?.centerY.offset()(0)
            make?.left.equalTo()(selecteView.my_mas_right)?.offset()(6)
            make?.right.offset()(-30)
        }
    }
}

extension LauncherVersionItem: NSTextFieldDelegate {
    // 结束编辑
    func controlTextDidEndEditing(_ obj: Notification) {
        if let textField = obj.object as? NSTextField, textField == versionLabel {
            self.isSelected = false
        }
    }
}
