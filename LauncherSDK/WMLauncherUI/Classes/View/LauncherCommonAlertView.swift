//
//  LauncherCommonAlertView.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/29.
//

import Foundation
import WMMasonry
import WMXAppKit

// MARK: 设置各控件约束
struct LauncherConstraint {
    var top: CGFloat?
    var left: CGFloat?
    var bottom: CGFloat?
    var right: CGFloat?
    var width: CGFloat?
    var height: CGFloat?
    
    init(top: CGFloat? = nil, left: CGFloat? = nil, bottom: CGFloat? = nil, right: CGFloat? = nil, width: CGFloat? = nil, height: CGFloat? = nil) {
        self.top = top
        self.left = left
        self.bottom = bottom
        self.right = right
        self.width = width
        self.height = height
    }
}
// MARK: 自定义视图协议类
class LauncherAlertCustom {
    var mainView: NSView?

    var constraint: LauncherConstraint?

    func setup(mainView: NSView? = nil) {
        self.mainView = mainView
    }
}
    
// MARK: 弹框标题
class LauncherAlertTitleView: NSView {
    
    private lazy var titleBgImageView: NSImageView = {
        let bgImageView = NSImageView()
        bgImageView.image = NSImage.load(inLauncherBundle: "alert_title_bg_img")
        return bgImageView
    }()
    
    public lazy var titleLabel: NSTextField = {
        let view = NSTextField()
        view.font = NSFont.systemFont(ofSize: 28.0)
        view.isBordered = false
        view.drawsBackground = false
        view.isEditable = false
        view.textColor = NSColor.white
        return view
    }()
    
    override init(frame: NSRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    private func setupUI() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.clear.cgColor
        
        addSubview(titleBgImageView)
        addSubview(titleLabel)
        
        titleBgImageView.my_mas_makeConstraints { make in
            make?.center.offset()(0)
            make?.size.equalTo()(CGSizeMake(518, 8))
        }
        
        titleLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(titleBgImageView)
            make?.left.offset()(22)
        }
    }
}

// MARK: 弹框按钮类
class LauncherAlertAction: LauncherAlertCustom {
    private var actionButton: LauncherCustomButton?
    private var title: String?
    private var handler: ((LauncherAlertAction) -> Void)?

    static func actionWithTitle(_ title: String?, handler: ((LauncherAlertAction) -> Void)? = nil) -> LauncherAlertAction {
        let action = LauncherAlertAction()
        action.title = title
        action.handler = handler
        action.setupActionButton()
        return action
    }

    private func setupActionButton() {
        let button = LauncherCustomButton()
        let normalImage = NSImage.load(inLauncherBundle: "alert_btn_normal_img")!
        let normalColor = NSColor.wmxkit_color(withHexString: "#3C3E40")
        button.updateStateImage(normal: normalImage)
        button.updateTitleColor(normal: normalColor)
        button.isBordered = false
        button.imageScaling = .scaleProportionallyUpOrDown
        button.target = self
        button.action = #selector(buttonTapped(_:))
        button.title = title ?? ""
        button.font = NSFont.systemFont(ofSize: 18)
        
        setup(mainView: button)
        constraint = LauncherConstraint(width: 170, height: 49)
    }

    @objc private func buttonTapped(_ sender: LauncherCustomButton) {
        if sender.superview is LauncherCommonAlertView {
            (sender.superview as! LauncherCommonAlertView).dismiss()
        }
        
        if let handler = handler {
            handler(self)
        }
    }
}

// MARK: 弹框主界面
class LauncherCommonAlertView: NSView {
    
    public var alertViewWidth: CGFloat = 558
    // 可自定义title约束
    public var titleConstraint: LauncherConstraint = LauncherConstraint(top: 30, left: 26, width: 518, height: 30)
    // 可自定义message约束
    public var messageConstraint: LauncherConstraint = LauncherConstraint(top: 30, left: 26, right: -26)
    // 外部可自实现约束
    public var customSubViewConstraints: ((_ title: LauncherAlertTitleView?, _ messageView: NSTextView?, _ subViews: [LauncherAlertCustom]) -> Void)?
    
    private var titleView: LauncherAlertTitleView?
    private var messageView: NSTextView?
    private var closeButton: LauncherCustomButton?
    private var subItemViewArray: [LauncherAlertCustom] = []
    private var actionButtonCount = 0
    private var containerView: WMXOverlayContainerView!
    private var closeHandler: (() -> Void)?
    
    override init(frame: NSRect) {
        super.init(frame: frame)
        
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    private lazy var alertBgImageView: NSImageView = {
        let bgImageView = NSImageView()
        bgImageView.imageScaling = .scaleAxesIndependently
        bgImageView.wantsLayer = true
        return bgImageView
    }()
    
//    private lazy var messageScrollView: NSScrollView = {
//        let textView = NSTextView()
//        textView.isEditable = false
//        textView.backgroundColor = .clear
//        textView.textContainer?.lineFragmentPadding = 0
//        textView.textContainerInset = NSZeroSize
//        messageView = textView
//        
//        let scrollView = NSScrollView()
//        scrollView.documentView = messageView
//        scrollView.scrollerStyle = .overlay
//        scrollView.hasVerticalScroller = false
//        scrollView.contentInsets = NSEdgeInsetsZero
//        scrollView.drawsBackground = false
//        scrollView.backgroundColor = NSColor.clear
//        
//        return scrollView
//    }()
    
    static func setup(title: String? = nil, message: String? = nil, bgImage: String = "alert_bg_img") -> LauncherCommonAlertView {

        var attributedMessage : NSAttributedString?
        if let messageText = message {
            attributedMessage = defaultAttributed(message: messageText)
        }
        return setup(title: title, attributedMessage: attributedMessage, bgImage: bgImage)
    }
    
    static func setup(title: String? = nil, attributedMessage: NSAttributedString? = nil, bgImage: String = "alert_bg_img")  -> LauncherCommonAlertView  {
        let alertView  = LauncherCommonAlertView()
        alertView.setup(title: title,attributedMessage: attributedMessage,bgImage: bgImage)
        return alertView
    }
    
    private func setup(title: String? = nil, attributedMessage: NSAttributedString? = nil, bgImage: String = "alert_bg_img")  {
        
        actionButtonCount = 0
        subItemViewArray.removeAll()
        
        if let originalImage = NSImage.load(inLauncherBundle: bgImage) {
            let capInsets = NSEdgeInsets(top: 40, left: 40, bottom: 40, right: 40)// 设置可拉伸区域
            let resizableImage = originalImage.resizableImage(capInsets: capInsets)
            alertBgImageView.image = resizableImage
            addSubview(alertBgImageView)
        }
            
        if let titleText = title {
            titleView = LauncherAlertTitleView()
            titleView?.titleLabel.stringValue = titleText
            addSubview(titleView!)
        }
        
        if let attributedMsg = attributedMessage {
            let textView = NSTextView()
            textView.isEditable = false
            textView.backgroundColor = .clear
            textView.textContainer?.lineFragmentPadding = 0
            textView.textContainerInset = NSZeroSize
            messageView = textView
            addSubview(messageView!)
            messageView!.textStorage?.setAttributedString(attributedMsg)
        }
    }
    
    static func defaultAttributed(message: String, colorHex: String = "#878787", fontSize: CGFloat = 18, alignment:NSTextAlignment = .left) -> NSAttributedString {
        let attributedString = NSMutableAttributedString(string: message)

        let paragraphStyle = NSMutableParagraphStyle()
        let lineSpacing: CGFloat = 5
        paragraphStyle.lineSpacing = lineSpacing
        paragraphStyle.alignment = alignment
        
        let textColor = NSColor.wmxkit_color(withHexString: colorHex)
    
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: attributedString.length))
        attributedString.addAttribute(.font, value: NSFont.systemFont(ofSize: fontSize), range: NSRange(location: 0, length: attributedString.length))
        attributedString.addAttribute(.foregroundColor, value: textColor!, range: NSRange(location: 0, length: attributedString.length))
        
        return attributedString
    }
    
    //MARK: 添加关闭按钮
    public func addDismissButton(handler: (() -> Void)? = nil) {
        let button = LauncherCustomButton()
        let normalImage = NSImage.load(inLauncherBundle: "alert_close_btn_light_img")!
        button.updateStateImage(normal: normalImage, hover: nil, pressed: nil, disabled: nil)
        button.isBordered = false
        button.imageScaling = .scaleProportionallyUpOrDown
        button.target = self
        button.action = #selector(closeButtonTapped(_:))
        addSubview(button)
        closeButton = button
        closeHandler = handler
        button.my_mas_makeConstraints { make in
            make?.top.offset()(28)
            make?.right.offset()(-28)
            make?.size.mas_equalTo()(CGSize(width: 28, height: 28))
        }
    }
    
    @objc private func closeButtonTapped(_ sender: LauncherCustomButton) {
        if let tempCloseHandler = closeHandler {
            tempCloseHandler()
        } else {
            dismiss()
        }
    }
    
    // 当前布局，仅支持两个AlertAction
    public func add(action: LauncherAlertAction) {
        subItemViewArray.append(action)
        actionButtonCount = actionButtonCount + 1
    }
    
    public func add(cutsomView: LauncherAlertCustom) {
        subItemViewArray.append(cutsomView)
    }
    
    //MARK: 显示隐藏方法
    public func show(inView: NSView?) {
        if let customConstraints = customSubViewConstraints {
            customConstraints(titleView,messageView,subItemViewArray)
        } else {
            updateControlConstraints()
        }
        
        var alertSuperView = inView
        if alertSuperView == nil {
            let showWindow = NSWindow.wmxkit_applicationKey()
            alertSuperView = showWindow.contentView
        }
        
        containerView = WMXOverlayContainerView.init(frame: alertSuperView!.frame)
        containerView.backgroundColor = NSColor.init(calibratedWhite: 0, alpha: 0.4)
        containerView.addSubview(self)
        self.my_mas_makeConstraints { make in
            make?.width.mas_equalTo()(self.alertViewWidth)
            make?.center.offset()(0)
        }
        alertSuperView?.addSubview(containerView)
        containerView.my_mas_makeConstraints { make in
            make?.edges.offset()(0)
        }
        //成为第一响应者，使其相应键盘事件
        window?.makeFirstResponder(self)
    }
    
    public func dismiss() {
        self.removeFromSuperview()
        actionButtonCount = 0
        containerView.removeFromSuperview()
        if let tempTitleView = titleView {
            tempTitleView.removeFromSuperview()
        }
        
        if let tempMessageView = messageView {
            tempMessageView.removeFromSuperview()
        }
        
        for subItemView in subItemViewArray {
            subItemView.mainView?.removeFromSuperview()
        }
        subItemViewArray.removeAll()
        closeHandler = nil
    }
    
    //MARK: 更新约束
    private func updateControlConstraints() {
        var previousView:NSView?
        alertBgImageView.my_mas_makeConstraints { make in
            make?.edges.offset()(0)
        }
        
        if let tempTitleView = titleView {
            tempTitleView.my_mas_makeConstraints { make in
                make?.top.offset()(titleConstraint.top!)
                make?.left.offset()(titleConstraint.left!)
                make?.width.equalTo()(titleConstraint.width!)
                make?.height.equalTo()(titleConstraint.height!)
            }
            previousView = tempTitleView
        }
        
        if let tempMessageView = messageView {
            tempMessageView.frame = CGRect(x: 0,y: 0, width: alertViewWidth - abs(messageConstraint.left!) - abs(messageConstraint.right!), height: 0
            )
 
            guard let layoutManager = tempMessageView.layoutManager,
                  let textContainer = tempMessageView.textContainer else { return }
            layoutManager.ensureLayout(for: textContainer)

            // 计算用于显示文本的矩形区域
            let usedRect = layoutManager.usedRect(for: textContainer)
            var contentHeightValue = usedRect.size.height
            
            // 文本最小高度
            if contentHeightValue < 65 {
                contentHeightValue = 65
            }
            tempMessageView.my_mas_makeConstraints { make in
                make?.left.offset()(messageConstraint.left!)
                make?.right.offset()(messageConstraint.right!)
                if let tempPreviousView = previousView {
                    make?.top.equalTo()(tempPreviousView.my_mas_bottom)?.offset()(messageConstraint.top!)
                } else {
                    make?.top.offset()(messageConstraint.top!)
                }
                make?.height.mas_equalTo()(contentHeightValue)?.priorityLow()
            }
            previousView = tempMessageView
        }
        
        var previousCustomMainView : NSView? = nil
        var addActionCount = 0
        
        for alertCustomItem in subItemViewArray {
            if alertCustomItem is LauncherAlertAction, let itemMainView = alertCustomItem.mainView {
                addSubview(itemMainView)
                if actionButtonCount == 1 {
                    itemMainView.my_mas_makeConstraints({ make in
                        make?.width.equalTo()(alertCustomItem.constraint?.width)
                        make?.height.equalTo()(alertCustomItem.constraint?.height)
                        make?.top.equalTo()(previousView!.my_mas_bottom)?.offset()(26)
                        make?.centerX.offset()(0)
                    })
                } else {
                    if addActionCount == 0 {
                        itemMainView.my_mas_makeConstraints({ make in
                            make?.width.equalTo()(alertCustomItem.constraint?.width)
                            make?.height.equalTo()(alertCustomItem.constraint?.height)
                            make?.top.equalTo()(previousView!.my_mas_bottom)?.offset()(26)
                            make?.right.equalTo()(self.my_mas_centerX)?.offset()(-15)
                        })
                        addActionCount = 1
                        previousCustomMainView = itemMainView
                    } else if addActionCount == 1 {
                        itemMainView.my_mas_makeConstraints({ make in
                            make?.width.equalTo()(alertCustomItem.constraint?.width)
                            make?.height.equalTo()(alertCustomItem.constraint?.height)
                            make?.centerY.equalTo()(previousCustomMainView)
                            make?.left.equalTo()(self.my_mas_centerX)?.offset()(15)
                        })
                        addActionCount = 2
                    }
                }
                previousView = itemMainView
            } else {
                if let itemMainView = alertCustomItem.mainView {
                    addSubview(itemMainView)
                    itemMainView.my_mas_makeConstraints({ make in
                        make?.left.offset()(0)
                        make?.right.offset()(0)
                        make?.height.equalTo()(alertCustomItem.constraint?.height)
                        if let tempPreviousView = previousView {
                            make?.top.equalTo()(tempPreviousView.my_mas_bottom)?.offset()(alertCustomItem.constraint?.top! ?? 0)
                        } else {
                            make?.top.offset()(alertCustomItem.constraint?.top! ?? 0)
                        }
                    })
                    previousView = itemMainView
                }
            }
        }
        
        if let tempPreviousView = previousView {
            tempPreviousView.my_mas_updateConstraints { make in
                make?.bottom.offset()(-30)
            }
        }
    }
}
