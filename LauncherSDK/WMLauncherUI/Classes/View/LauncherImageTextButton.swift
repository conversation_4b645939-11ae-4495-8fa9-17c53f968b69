//
//  LauncherImageTextButton.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/13.
//

class LauncherImageTextButton: NSButton {
    private var normalImage: NSImage?
    private var normalHoverImage: NSImage?
    private var selectedImage: NSImage?
    private var selectedHoverImage: NSImage?
    private var isSelected: Bool = false
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupButton()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupButton()
    }
    
    private func setupButton() {
        self.isBordered = false
        self.imagePosition = .imageLeft
        
        let trackingArea = NSTrackingArea(rect: self.bounds, options: [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect], owner: self, userInfo: nil)
        self.addTrackingArea(trackingArea)
    }
    
    func setImages(normal: NSImage, normalHover: NSImage, selected: NSImage, selectedHover: NSImage) {
        self.normalImage = normal
        self.normalHoverImage = normalHover
        self.selectedImage = selected
        self.selectedHoverImage = selectedHover
        self.image = normal
    }
    
    func setCustomTitle(_ title: String, font: NSFont, color: NSColor) {
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: color
        ]
        self.attributedTitle = NSAttributedString(string: title, attributes: attributes)
    }
    
    func setSelected(_ selected: Bool) {
        isSelected = selected
        self.image = isSelected ? selectedImage : normalImage
    }
    
    func isSelectedState() -> Bool {
        return isSelected
    }
    
    override func mouseEntered(with event: NSEvent) {
        if !isSelected {
            self.image = normalHoverImage
        } else {
            self.image = selectedHoverImage
        }
    }
    
    override func mouseExited(with event: NSEvent) {
        if !isSelected {
            self.image = normalImage
        } else {
            self.image = selectedImage
        }
    }
}

