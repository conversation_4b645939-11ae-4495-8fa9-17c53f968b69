//
//  LauncherResRepairView.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/2.
//

import Foundation
import WMXAppKit


enum LauncherResRepairType {
    case reinstall  //重新安装
    case all        //修复所有
    case breakdown  //修复损坏
}


struct LauncherResRepairInfo {
    var estimatedTime: String = ""
    var type: LauncherResRepairType!
}

extension LauncherResRepairView {
    static func show(repairInfos: [LauncherResRepairInfo],cancelHandler: (() -> Void)? = nil, confirHandler: ((LauncherResRepairType) -> Void)? = nil) -> LauncherResRepairView {
        let resRepairView = LauncherResRepairView()
        let viewHeight = CGFloat(repairInfos.count) * LauncherResRepairView.collectionItemHeight + CGFloat(repairInfos.count-1)*LauncherResRepairView.lineSpacing
        resRepairView.dataArray = repairInfos
        resRepairView.updategResRepairEstimatedTime()
        let alertCustomView : LauncherAlertCustom = LauncherAlertCustom.init()
        alertCustomView.setup(mainView: resRepairView)
        alertCustomView.constraint =  LauncherConstraint(top: 32, left: 0, right: 0, height: viewHeight)
        
        let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "resource_repair"), message: LauncherLocalization.localizedString(forKey: "repair_tips"))
        alertView.alertViewWidth = LauncherResRepairView.viewWidth
        
        alertView.add(cutsomView: alertCustomView)
        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "cancel"), handler: { action in
            if let handler = cancelHandler {
                handler()
            }
        }))
        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "start_repair"), handler: { action in
            let selectedIndex = resRepairView.selectedIndex
            let repairType = repairInfos[selectedIndex].type
            if let handler = confirHandler {
                handler(repairType!)
            }
        }))
        alertView.show(inView: nil)
        
        return resRepairView
    }
}

class LauncherResRepairView: NSView {
    private var collectionView: NSCollectionView?
    public var dataArray : [LauncherResRepairInfo]!
    public var selectedIndex = 0
    
    static var collectionItemHeight : CGFloat = 120.0
    static var lineSpacing : CGFloat = 13
    static var viewWidth : CGFloat = 648.0
    
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupCollectionView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupCollectionView()
    }

    private func setupCollectionView() {
        let offset = 22.0
        let layout = NSCollectionViewFlowLayout()
        layout.itemSize = NSSize(width: LauncherResRepairView.viewWidth-2*offset, height: LauncherResRepairView.collectionItemHeight)
        layout.minimumLineSpacing = LauncherResRepairView.lineSpacing
        
        collectionView = NSCollectionView()
        collectionView!.collectionViewLayout = layout
        collectionView!.delegate = self
        collectionView!.dataSource = self
        collectionView!.isSelectable = true
        collectionView!.allowsMultipleSelection = false
        collectionView!.backgroundColors = [.clear]
        collectionView!.wantsLayer = true
        collectionView!.allowsEmptySelection = false
        collectionView!.layer?.backgroundColor = NSColor.clear.cgColor
        collectionView!.register(LauncherResRepairCollectiontem.self, forItemWithIdentifier: NSUserInterfaceItemIdentifier("LauncherResRepairCollectiontem"))
        addSubview(collectionView!)
        collectionView!.my_mas_makeConstraints { make in
            make?.top.offset()(0)
            make?.bottom.offset()(0)
            make?.left.offset()(offset)
            make?.right.offset()(-1*offset)
        }
    }
    
    public func updategResRepairEstimatedTime() {
        if !dataArray.isEmpty {
            let typesArray: [LauncherResRepairType] = dataArray.compactMap { $0.type }
            LauncherPatcherManager.shared.getResRepairEstimatedTimes(types: typesArray) {[weak self] restallTime, repairAllTime, breakdownTime in
                if let nonNilDataArray = self?.dataArray {
                    let updatedDataArray = nonNilDataArray.map { (item) -> LauncherResRepairInfo in
                        var newItem = item
                        if newItem.type == .reinstall {
                            newItem.estimatedTime = restallTime
                        } else if item.type == .all {
                            newItem.estimatedTime = repairAllTime
                        } else if item.type == .breakdown {
                            newItem.estimatedTime = breakdownTime
                        }
                        return newItem
                    }
                    self?.dataArray = updatedDataArray
                    self?.collectionView?.reloadData()
                }
            }
        }
    }
}

extension LauncherResRepairView: NSCollectionViewDataSource, NSCollectionViewDelegate {
    func numberOfSections(in collectionView: NSCollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: NSCollectionView, numberOfItemsInSection section: Int) -> Int {
        return dataArray.count
    }
    
    func collectionView(_ collectionView: NSCollectionView, itemForRepresentedObjectAt indexPath: IndexPath) -> NSCollectionViewItem {
        let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("LauncherResRepairCollectiontem"), for: indexPath) as! LauncherResRepairCollectiontem
        item.configure(with: dataArray[indexPath.item], selectedState: (selectedIndex == indexPath.item))
        return item
    }
    

    func collectionView(_ collectionView: NSCollectionView, didSelectItemsAt indexPaths: Set<IndexPath>) {
        
        if let item = collectionView.item(at: selectedIndex) as? LauncherResRepairCollectiontem {
            item.isSelected = false
        }
        
        for indexPath in indexPaths {
            let item = collectionView.item(at: indexPath) as? LauncherResRepairCollectiontem
            item?.isSelected = true
            selectedIndex = indexPath.item
        }
    }
}

// MARK: NSCollectionViewItem
class LauncherResRepairCollectiontem: NSCollectionViewItem {
    let iconImageView = NSImageView()
    let titleLabel = NSTextField(labelWithString: "")
    let estimatedTimeLabel = NSTextField(labelWithString: "")
    let descriptionTextView = LauncherClickThroughTextView()
    let backgroundView = NSImageView()

    override func loadView() {
        self.view = NSView()
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 背景图片
        backgroundView.wantsLayer = true
        backgroundView.layer?.backgroundColor = NSColor.clear.cgColor
        backgroundView.imageScaling = .scaleAxesIndependently
        view.addSubview(backgroundView)
        
        // 图标
        view.addSubview(iconImageView)
        // 标题
        titleLabel.font = NSFont.boldSystemFont(ofSize: 22)
        titleLabel.textColor = NSColor.wmxkit_color(withHexString: "#DFE1E2")
        view.addSubview(titleLabel)
        
        // 预计时间
        estimatedTimeLabel.font = NSFont.systemFont(ofSize: 16)
        estimatedTimeLabel.textColor = NSColor.wmxkit_color(withHexString: "#878787")
        view.addSubview(estimatedTimeLabel)
        
        // 描述
        descriptionTextView.isEditable = false
        descriptionTextView.isSelectable = false
        descriptionTextView.backgroundColor = .clear
        descriptionTextView.textContainer?.lineFragmentPadding = 0
        descriptionTextView.textContainerInset = NSZeroSize
        view.addSubview(descriptionTextView)
        
        // 设置布局
        setupConstraints()
    }
    
    private func setupConstraints() {
        backgroundView.my_mas_makeConstraints { make in
            make?.edges.offset()(0)
        }
        
        iconImageView.my_mas_makeConstraints { make in
            make?.size.mas_equalTo()(CGSizeMake(20, 20))
            make?.top.offset()(24)
            make?.left.offset()(24)
        }
        
        titleLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(iconImageView)
            make?.left.equalTo()(iconImageView.my_mas_right)?.offset()(16)
        }
        
        estimatedTimeLabel.my_mas_makeConstraints { make in
            make?.centerY.equalTo()(iconImageView)
            make?.right.offset()(-12)
        }
        
        descriptionTextView.my_mas_makeConstraints { make in
            make?.left.equalTo()(titleLabel)
            make?.right.equalTo()(estimatedTimeLabel)
            make?.bottom.offset()(-20)?.priorityLow()
            make?.top.equalTo()(titleLabel.my_mas_bottom)?.offset()(13)
        }
        
    }
    
    override var isSelected: Bool {
        didSet {
            var originalBgImage : NSImage!
            if isSelected {
                originalBgImage = NSImage.load(inLauncherBundle: "repair_item_bg_selected")
            } else {
                originalBgImage = NSImage.load(inLauncherBundle: "repair_item_bg_normal")
            }
            let capInsets = NSEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)// 设置可拉伸区域
            let resizableImage = originalBgImage.resizableImage(capInsets: capInsets)
            backgroundView.image = resizableImage
        }
    }
    
    func configure(with model: LauncherResRepairInfo, selectedState : Bool) {
        var iconImageName = ""
        var title = ""
        var desc = ""
        switch model.type {
        case .reinstall:
            iconImageName = "repair_reinstall_icon"
            title = LauncherLocalization.localizedString(forKey: "reinstall")
            desc = LauncherLocalization.localizedString(forKey: "reinstall_tips")
        case .all:
            iconImageName = "repair_all_icon"
            title = LauncherLocalization.localizedString(forKey: "complete_repair")
            desc = LauncherLocalization.localizedString(forKey: "complete_repair_tips")
        case .breakdown:
            iconImageName = "repair_warning_icon"
            title = LauncherLocalization.localizedString(forKey: "repair_only_damaged")
            desc = LauncherLocalization.localizedString(forKey: "repair_only_damaged_tips")
        default:
            break
        }
        iconImageView.image = NSImage.load(inLauncherBundle: iconImageName)
        titleLabel.stringValue = title
        estimatedTimeLabel.stringValue = model.estimatedTime.isEmpty ? LauncherLocalization.localizedString(forKey: "estimating") : String(format: LauncherLocalization.localizedString(forKey: "estimated_time"), model.estimatedTime)
        descriptionTextView.textStorage?.setAttributedString(defaultAttributed(message: desc))
        self.isSelected = selectedState
        
    }
    
    func defaultAttributed(message: String, colorHex: String = "#878787", fontSize: CGFloat = 16) -> NSAttributedString {
        let attributedString = NSMutableAttributedString(string: message)

        let paragraphStyle = NSMutableParagraphStyle()
        let lineSpacing: CGFloat = 5
        paragraphStyle.lineSpacing = lineSpacing
        
        let textColor = NSColor.wmxkit_color(withHexString: colorHex)
    
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: attributedString.length))
        attributedString.addAttribute(.font, value: NSFont.systemFont(ofSize: fontSize), range: NSRange(location: 0, length: attributedString.length))
        attributedString.addAttribute(.foregroundColor, value: textColor!, range: NSRange(location: 0, length: attributedString.length))
        
        return attributedString
    }
}

class LauncherClickThroughTextView: NSTextView {
    override func mouseDown(with event: NSEvent) {
        // 将鼠标事件传递给下一个响应者
        self.nextResponder?.mouseDown(with: event)
        super.mouseDown(with: event) // 保持默认行为（可选）
    }
}
