//
//  LauncherAppStatusPanel.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/7.
//

import Foundation
import WMMasonry

class LauncherAppStatusPanel: NSView {

    public var statusButton: LauncherCustomButton!
    private var versionTextField: NSTextField!
    private var switchVersionButton: NSButton!
    private var versionTextCenterConstraint:WMMASConstraint!
    
    private var currentStatus: LauncherAppStatus = .unknown {
        didSet {
            updateUIForStatus(currentStatus)
        }
    }
    
    private var currentVersion: String = "" {
        didSet {
            versionTextField.stringValue = currentVersion
        }
    }
    
    var statusButtonCallback: ((LauncherAppStatus) -> Void)?
    var switchVersionButtonCallback: (() -> Void)?

    // 初始化界面
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // 设置 UI 元素
    private func setupUI() {
        statusButton = LauncherCustomButton()
        statusButton.target = self
        statusButton.action = #selector(statusButtonClicked(_:))
        statusButton.bezelColor = NSColor.clear
        statusButton.wantsLayer = true
        statusButton.layer?.backgroundColor = NSColor.clear.cgColor
        statusButton.font = NSFont.systemFont(ofSize: 20)
        
        let normalImage = NSImage.load(inLauncherBundle: "start_btn_normal")!
        let hoverImage = NSImage.load(inLauncherBundle: "start_btn_hover")!
        let pressedImage = NSImage.load(inLauncherBundle: "start_btn_pressed")!
        let disabledImage = NSImage.load(inLauncherBundle: "start_btn_disabled")!
        statusButton.updateStateImage(normal: normalImage, hover: hoverImage, pressed: pressedImage, disabled: disabledImage)
        
        let normalColor = NSColor.wmxkit_color(withHexString: "#000000")
        let hoverColor = NSColor.wmxkit_color(withHexString: "#323232")
        let pressedColor = NSColor.wmxkit_color(withHexString: "#000000")
        let disabledColor = NSColor.wmxkit_color(withHexString: "#000000")
        statusButton.updateTitleColor(normal: normalColor, hover: hoverColor, pressed: pressedColor, disabled: disabledColor)
        addSubview(statusButton)
        
        versionTextField = NSTextField()
        versionTextField.isEditable = false
        versionTextField.isBordered = false
        versionTextField.drawsBackground = false
        versionTextField.stringValue = currentVersion
        versionTextField.textColor = NSColor.white
        versionTextField.font = NSFont.systemFont(ofSize: 15)
        versionTextField.lineBreakMode = .byWordWrapping
        versionTextField.alignment = .center
        versionTextField.maximumNumberOfLines = 2
        
        addSubview(versionTextField)
        
        switchVersionButton = NSButton()
        switchVersionButton.title = ""
        switchVersionButton.target = self
        switchVersionButton.action = #selector(switchVersionButtonClicked)
        switchVersionButton.cell?.isBordered = false
        switchVersionButton.cell?.isBezeled = false
        switchVersionButton.image = NSImage.load(inLauncherBundle: "switch_version")!
        switchVersionButton.isHidden = true
        
        addSubview(switchVersionButton)
        
        // 设置约束
        statusButton.my_mas_makeConstraints { make in
            make?.top.offset()(0)
            make?.centerX.offset()(0)
            make?.size.mas_equalTo()(NSMakeSize(252, 56))
        }
        
        switchVersionButton.my_mas_makeConstraints { make in
            make?.top.equalTo()(statusButton.my_mas_bottom)?.offset()(12)
            make?.right.equalTo()(versionTextField.my_mas_left)?.offset()(-2)
            make?.size.mas_equalTo()(NSMakeSize(16, 16))
        }
        
        versionTextField.my_mas_makeConstraints { make in
            make?.left.greaterThanOrEqualTo()(0)
            make?.right.lessThanOrEqualTo()(0)
            make?.centerY.equalTo()(switchVersionButton)
            self.versionTextCenterConstraint = make?.centerX.offset()(0)
        }
        
        // 初始更新 UI
        updateUIForStatus(currentStatus)
        didShowSitchButton(didShow:false) //默认不显示切换按钮
    }
    
    private func updateUIForStatus(_ status: LauncherAppStatus) {
        switch status {
        case .unknown:
            statusButton.title = LauncherLocalization.localizedString(forKey: "version_verification")
            statusButton.isEnabled = true
        case .checking:
            statusButton.title = LauncherLocalization.localizedString(forKey: "checking")
            statusButton.isEnabled = false
            didShowSitchButton(didShow:false)
            currentVersion = ""
        case .checkFail:
            statusButton.title = LauncherLocalization.localizedString(forKey: "version_verification")
            statusButton.isEnabled = true
        case .downloadingFail:
            statusButton.title = LauncherLocalization.localizedString(forKey: "continue_install")
            statusButton.isEnabled = true
        case .startDownloading:
            statusButton.title = LauncherLocalization.localizedString(forKey: "install_game")
            statusButton.isEnabled = true
        case .needUpdating:
            statusButton.title = LauncherLocalization.localizedString(forKey: "start_update")
            statusButton.isEnabled = true
        case .switchVersionUpdate:
            statusButton.title = LauncherLocalization.localizedString(forKey: "switch_version")
            statusButton.isEnabled = true
        case .startGame:
#if LAUNCHER_TYPE_BENCHMARK
            statusButton.title = LauncherLocalization.localizedString(forKey: "start_test")
#else
            statusButton.title = LauncherLocalization.localizedString(forKey: "start_game")
#endif
            if LauncherPatcherManager.shared.haveRunningApplication() {
                statusButton.isEnabled = false
            } else {
                statusButton.isEnabled = true
            }
            
        case .gameInProgress:
#if LAUNCHER_TYPE_BENCHMARK
            statusButton.title = LauncherLocalization.localizedString(forKey: "testing")
#else
            statusButton.title = LauncherLocalization.localizedString(forKey: "game_in_progress")
#endif
            statusButton.isEnabled = false
            didShowSitchButton(didShow:false)
        default: break
        }
    }
    
    // 对外接口：设置状态
    func setStatus(_ status: LauncherAppStatus) {
        if status != .operationDone {
            currentStatus = status
        } else {
            statusButton.isEnabled = true
        }
    }
    
    // 对外接口：获取当前状态
    func getStatus() -> LauncherAppStatus {
        return currentStatus
    }
    
    // 对外接口：设置版本号
    func setVersion(appName: String, currentVer: String, latestVer: String) {
        if !currentVer.isEmpty, currentVer != latestVer {
            currentVersion = String(format: LauncherLocalization.localizedString(forKey: "current_version_latest_version"), appName, currentVer, latestVer)
        } else {
            currentVersion = "\(appName)：\(latestVer)"
        }
    }
    
    func didShowSitchButton(didShow: Bool, enable: Bool = false) {
#if LAUNCHER_ENV_DEVELOP || LAUNCHER_ENV_TEST//只开发和测试环境包含这个功能
        if didShow {
            switchVersionButton.isHidden = false
            switchVersionButton.isEnabled = enable
            versionTextCenterConstraint.offset()(18)
        } else {
            switchVersionButton.isHidden = true
            versionTextCenterConstraint.offset()(0)
        }
        layoutSubtreeIfNeeded()
#endif
    }
    
    public func retryStatusButtonClicked() {
        statusButtonClicked(statusButton)
    }
    
    @objc private func statusButtonClicked(_ sender: NSButton) {
        sender.isEnabled = false
        statusButtonCallback?(currentStatus)
    }
    
    @objc private func switchVersionButtonClicked() {
        switchVersionButtonCallback?()
    }
}
