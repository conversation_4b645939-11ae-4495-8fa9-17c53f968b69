//
//  LauncherTipView.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON>jia on 2024/11/20.
//

import Foundation
import WMXAppKit

enum LauncherTipType: Int {
    case success
    case error
    case warning
}

class LauncherTipView: NSView {

    private let iconView = NSImageView()
    private let tipLabel = NSTextField()
    private var type: LauncherTipType = .success
    private var tipString: String = ""

    private static let kMaxWidth: CGFloat = 280

    // MARK: - Initialization

    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        commonInit()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }

    private func commonInit() {
        wantsLayer = true
        layer?.backgroundColor = NSColor.white.cgColor
        layer?.cornerRadius = 4.0

        iconView.wantsLayer = true
        iconView.layer?.backgroundColor = NSColor.clear.cgColor

        tipLabel.isBordered = false
        tipLabel.backgroundColor = .clear
        tipLabel.isEditable = false
        tipLabel.textColor = NSColor.wmxkit_color(withHexString: "#202020")
        tipLabel.lineBreakMode = .byWordWrapping
        tipLabel.maximumNumberOfLines = 3

        addSubview(iconView)
        addSubview(tipLabel)
    }

    // MARK: - Public Methods

    static func tipView(type: LauncherTipType, withTip tipString: String) -> LauncherTipView {
        let tipView = LauncherTipView()
        tipView.type = type
        tipView.tipString = tipString
        tipView.updateView()
        return tipView
    }

    static func show(in view: NSView?, type: LauncherTipType, tip: String) -> LauncherTipView {
        let tipView = LauncherTipView.tipView(type: type, withTip: tip)
        if let inView = view {
            inView.addSubview(tipView)
        } else {
            NSWindow.wmxkit_applicationKey().contentView?.addSubview(tipView)
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
            tipView.removeFromSuperview()
        }
        return tipView
    }

    // MARK: - Layout

    override func layout() {
        super.layout()

        let imageFrame = CGRect(x: 18.0, y: 0.0, width: 14.0, height: 14.0)
        let titleWidth = LauncherTipView.kMaxWidth - 18.0 * 2 -  10
        let size = tipLabel.sizeThatFits(NSSize(width: titleWidth, height: .greatestFiniteMagnitude))

        let tipFrame = CGRect(x: imageFrame.maxX + 10, y: 0.0, width: size.width, height: size.height)

        self.frame = CGRect(x: 0.0, y: 0.0,
                            width: size.width + 18.0 * 3.0 + 10,
                            height: size.height + 11.0 * 2.0)

        iconView.frame = imageFrame.offsetBy(dx: 0.0, dy: (bounds.midY - imageFrame.height / 2.0))
        tipLabel.frame = tipFrame.offsetBy(dx: 0.0, dy: (bounds.midY - tipFrame.height / 2.0))

        if let superview = superview {
            let center = CGPoint(x: superview.bounds.midX, y: superview.bounds.midY)
            self.frame.origin = CGPoint(x: center.x - bounds.width / 2.0,
                                        y: center.y + bounds.height / 2.0)
        }
    }

    // MARK: - Private Methods

    private func updateView() {
        switch type {
        case .success:
            iconView.image = NSImage.load(inLauncherBundle: "tips_success")
        case .error:
            iconView.image = NSImage.load(inLauncherBundle: "tips_error")
        case .warning:
            iconView.image = NSImage.load(inLauncherBundle: "tips_warning")
        }

        tipLabel.stringValue = tipString
        needsLayout = true
        layoutSubtreeIfNeeded()
    }
}
