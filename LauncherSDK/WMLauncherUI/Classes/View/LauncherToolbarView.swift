//
//  LauncherToolbarView.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/13.
//

import Cocoa
import WMMasonry

// 定义按钮类型枚举
enum LauncherToolbarType: Int {
    case logo = 0
    case sound
    case settings
    case close
    case minimize
}

class LauncherToolbarView: NSView {
    private let latestCacheSoundOffStateKey = "LauncherCacheSoundOffState"
    private var initialMouseLocation: NSPoint = .zero
    
    private lazy var stackView: NSStackView = {
        let stackView = NSStackView()
        stackView.orientation = .horizontal
        stackView.spacing = 6
        stackView.alignment = .centerY
        return stackView
    }()
    
    // 按钮点击回调
    var buttonClickHandler: ((LauncherToolbarType) -> Void)?

    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        addSubview(stackView)
        stackView.my_mas_makeConstraints { make in
            make?.top.offset()(6)
            make?.right.offset()(-13)
        }
        
        let (sound_normal,sound_hover,sound_pressed) = getSoundIcon(off: latestCacheSoundOffState)
//        addButton(ofType: .logo, normalImage: "toolbar_logo_normal",hoverImage: "toolbar_logo_hover", pressedImage: "toolbar_logo_pressed")
        addButton(ofType: .settings,  normalImage: "toolbar_set_normal",hoverImage: "toolbar_set_hover", pressedImage: "toolbar_set_pressed")
        addButton(ofType: .sound, normalImage: sound_normal,hoverImage: sound_hover, pressedImage: sound_pressed)
//        addVerticalLine()
//        addButton(ofType: .minimize,  normalImage: "toolbar_minimize_normal",hoverImage: "toolbar_minimize_hover", pressedImage: "toolbar_minimize_pressed")
//        addButton(ofType: .close,  normalImage: "toolbar_close_normal",hoverImage: "toolbar_close_hover", pressedImage: "toolbar_close_pressed")
        
#if LAUNCHER_TYPE_BENCHMARK
        stackView.isHidden = true
#endif
    }
    
    public var latestCacheSoundOffState: Bool {
        get {
            let state = UserDefaults.standard.bool(forKey: latestCacheSoundOffStateKey)
            return state
        }
        set {
            UserDefaults.standard.setValue(newValue, forKey: latestCacheSoundOffStateKey)
            UserDefaults.standard.synchronize()
        }
    }
    
    private func addButton(ofType type: LauncherToolbarType, normalImage: String, hoverImage: String, pressedImage: String) {
        // 创建 NSButton 并配置
        let button = LauncherCustomButton()
        let normalImage = NSImage.load(inLauncherBundle: normalImage)!
        let hoverImage = NSImage.load(inLauncherBundle: hoverImage)!
        let pressedImage = NSImage.load(inLauncherBundle: pressedImage)!
        button.updateStateImage(normal: normalImage, hover: hoverImage, pressed: pressedImage, disabled: nil)
        button.isBordered = false
        button.imageScaling = .scaleProportionallyUpOrDown
        button.target = self
        button.action = #selector(buttonClicked(_:))
        button.tag = type.rawValue
        stackView.addArrangedSubview(button)
        button.my_mas_makeConstraints { make in
            make?.size.mas_equalTo()(CGSizeMake(24, 24))
        }
    }
    
    private func addVerticalLine() {
        let lineBgView = NSView()
        lineBgView.wantsLayer = true
        lineBgView.layer?.backgroundColor = NSColor.clear.cgColor
        stackView.addArrangedSubview(lineBgView)
        lineBgView.my_mas_makeConstraints { make in
            make?.size.mas_equalTo()(CGSizeMake(18, 18))
        }
        
        let verticalLine = NSView()
        verticalLine.wantsLayer = true
        verticalLine.layer?.backgroundColor = NSColor.init(white: 1.0, alpha: 0.2).cgColor
        lineBgView.addSubview(verticalLine)
        verticalLine.my_mas_makeConstraints { make in
            make?.size.mas_equalTo()(CGSizeMake(2, 16))
            make?.center.offset()(0)
        }
    }
    
    public func changeButtonIsHidden(ofType type: LauncherToolbarType, isHidden: Bool) {
        let targetTag = type.rawValue
        if let foundView = stackView.arrangedSubviews.first(where: { $0.tag == targetTag }) {
            if let button = foundView as? LauncherCustomButton {
                button.isHidden = isHidden
            }
        }
    }
    
    // 按钮点击事件
    @objc private func buttonClicked(_ sender: LauncherCustomButton) {
        // 使用 tag 来确定按钮类型
        if let type = LauncherToolbarType(rawValue: sender.tag) {
            if type == .sound {
                self.latestCacheSoundOffState = !latestCacheSoundOffState
                
                let (sound_normal,sound_hover,sound_pressed) = getSoundIcon(off: latestCacheSoundOffState)
                let normalImage = NSImage.load(inLauncherBundle: sound_normal)!
                let hoverImage = NSImage.load(inLauncherBundle: sound_hover)!
                let pressedImage = NSImage.load(inLauncherBundle: sound_pressed)!
                sender.updateStateImage(normal: normalImage, hover: hoverImage, pressed: pressedImage, disabled: nil)
            }
            buttonClickHandler?(type)
        }
    }
    
    private func getSoundIcon(off: Bool) -> (String, String, String) {
        if off {
            return ("toolbar_sound_off_normal","toolbar_sound_off_hover","toolbar_sound_off_pressed")
        } else {
            return ("toolbar_sound_on_normal","toolbar_sound_on_hover","toolbar_sound_on_pressed")
        }
    }
    
    // 增加长按，可拖拽移动功能
    override func mouseDown(with event: NSEvent) {
        initialMouseLocation = event.locationInWindow
    }

    override func mouseDragged(with event: NSEvent) {
        guard let window = self.window else { return }
        let currentMouseLocation = event.locationInWindow
        let offset = NSPoint(x: currentMouseLocation.x - initialMouseLocation.x,
                             y: currentMouseLocation.y - initialMouseLocation.y)

        // 更新窗口位置
        var windowOrigin = window.frame.origin
        windowOrigin.x += offset.x
        windowOrigin.y += offset.y
        window.setFrameOrigin(windowOrigin)
    }
}
