//
//  LauncherAnimatedArrowView.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/3/6.
//

import Cocoa

class LauncherAnimatedArrowView: NSView  {
    private let arrowLayer = CAShapeLayer()
    private var isPalying = false
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    private func setupView() {
        let circleSize: CGFloat = min(bounds.width, bounds.height)
        layer = CALayer()
        layer?.cornerRadius = circleSize/2
        layer?.backgroundColor = NSColor.wmxkit_color(withHexString: "#937A44").cgColor
        layer?.masksToBounds = true

        setupArrow()
    }

    private func setupArrow() {
        let circleFrame = self.bounds
        let arrowWidth: CGFloat = circleFrame.width * 0.5 // 箭头的总宽度
        let arrowHeight: CGFloat = circleFrame.height * 0.6 // 箭头的总高度
        let stemHeight: CGFloat = arrowHeight * 0.55 // 竖直线段的高度
        let stemWidth: CGFloat = arrowWidth * 0.4 // 竖线的宽度
        let triangleHeight: CGFloat = arrowHeight * 0.45 // 三角箭头的高度

        let arrowPath = NSBezierPath()

        // 1️⃣ 画箭头的竖直矩形部分（箭头主体）
        let stemRect = CGRect(x: (arrowWidth - stemWidth) / 2, y: triangleHeight, width: stemWidth, height: stemHeight)
        arrowPath.appendRect(stemRect)

        // 2️⃣ 画箭头底部的三角形部分
        arrowPath.move(to: CGPoint(x: 0, y: triangleHeight)) // 左下角
        arrowPath.line(to: CGPoint(x: arrowWidth / 2, y: 0)) // 顶点（底部尖角）
        arrowPath.line(to: CGPoint(x: arrowWidth, y: triangleHeight)) // 右下角
        arrowPath.close()

        arrowLayer.path = arrowPath.cgPath
        arrowLayer.fillColor = NSColor.white.cgColor
        arrowLayer.frame = CGRect(x: circleFrame.midX - arrowWidth / 2, y: circleFrame.maxY, width: arrowWidth, height: arrowHeight)

        layer?.addSublayer(arrowLayer)
    }

    func startAnimation() {
        if isPalying {
            return
        }
        if !isPalying {
            arrowLayer.frame = CGRect(x: arrowLayer.frame.origin.x, y: self.bounds.maxY, width: arrowLayer.frame.size.width, height: arrowLayer.frame.size.height)
        }
        
        isPalying = true
        
        let moveAnimation = CABasicAnimation(keyPath: "position.y")
        moveAnimation.fromValue = arrowLayer.position.y
        moveAnimation.toValue = arrowLayer.position.y - self.bounds.height - arrowLayer.bounds.height
        moveAnimation.duration = 1.0
        moveAnimation.repeatCount = .infinity
        moveAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)

        arrowLayer.add(moveAnimation, forKey: "moveDown")
    }
    
    /// 停止箭头动画
    func stopAnimation() {
        arrowLayer.removeAnimation(forKey: "moveDown")
        let triangleHeight: CGFloat = arrowLayer.frame.size.height
        arrowLayer.frame = CGRect(x: arrowLayer.frame.origin.x, y: self.bounds.midY - triangleHeight/2, width: arrowLayer.frame.size.width, height: triangleHeight)
        isPalying = false
    }
}

// ✅ 扩展 NSBezierPath 以兼容 macOS 12+
extension NSBezierPath {
    var cgPath: CGPath {
        let path = CGMutablePath()
        var points = [NSPoint](repeating: .zero, count: 3)

        for i in 0..<elementCount {
            let type = element(at: i, associatedPoints: &points)
            switch type {
            case .moveTo:
                path.move(to: CGPoint(x: points[0].x, y: points[0].y))
            case .lineTo:
                path.addLine(to: CGPoint(x: points[0].x, y: points[0].y))
            case .curveTo:
                path.addCurve(to: CGPoint(x: points[2].x, y: points[2].y),
                              control1: CGPoint(x: points[0].x, y: points[0].y),
                              control2: CGPoint(x: points[1].x, y: points[1].y))
            case .closePath:
                path.closeSubpath()
            case .cubicCurveTo:
                break
            case .quadraticCurveTo:
                break
            @unknown default:
                break
            }
        }
        return path
    }
}
