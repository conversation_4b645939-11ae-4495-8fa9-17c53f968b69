//
//  LauncherCustomButton.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON>jia on 2024/11/5.
//

import Cocoa
// 自定义枚举，用于表示按钮的状态
enum ButtonState {
    case normal     //普通
    case hover      //悬停
    case pressed    //按下
    case disabled   //不可点
}

class LauncherCustomButton: NSButton {

    private var images: [ButtonState: NSImage] = [:]
    private var backgroundColors: [ButtonState: NSColor] = [:]
    private var titleColors: [ButtonState: NSColor] = [:]
    private var trackingArea: NSTrackingArea?
    // 自定义初始化方法，接受不同状态的图片
    override init(frame frameRect: NSRect) {
        
        super.init(frame: frameRect)
        configureButton()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        configureButton()
    }
    
    override func updateTrackingAreas() {
        super.updateTrackingAreas()
        addTrackingArea()
    }
    
    func updateStateImage(normal: NSImage?,
                          hover: NSImage? = nil,
                          pressed: NSImage? = nil,
                          disabled: NSImage? = nil) {
        // 设置各个状态的图片
        images[.normal] = normal
        images[.hover] = hover ?? normal
        images[.pressed] = pressed ?? normal
        images[.disabled] = disabled ?? normal
        
        self.image = images[.normal]
    }
    
    func updateStateBackgroundColor(normal: NSColor?,
                                    hover: NSColor? = nil,
                                    pressed: NSColor? = nil,
                                    disabled: NSColor? = nil) {
        // 设置各个状态的背景色
        backgroundColors[.normal] = normal
        backgroundColors[.hover] = hover ?? normal
        backgroundColors[.pressed] = pressed ?? normal
        backgroundColors[.disabled] = disabled ?? normal
        
        layer?.backgroundColor = backgroundColors[.normal]?.cgColor
    }
    
    func updateTitleColor(normal: NSColor?  = NSColor.black,
                          hover: NSColor? = nil,
                          pressed: NSColor? = nil,
                          disabled: NSColor? = nil) {
        // 设置各个状态的图片
        titleColors[.normal] = normal
        titleColors[.hover] = hover ?? normal
        titleColors[.pressed] = pressed ?? normal
        titleColors[.disabled] = disabled ?? normal
        
        self.contentTintColor = titleColors[.normal]
    }
    
    private func configureButton() {
        self.isBordered = false
        self.wantsLayer = true
        self.layer?.contentsGravity = .resizeAspectFill
    }
    
    private func addTrackingArea() {
        if let trackingArea = self.trackingArea {
            self.removeTrackingArea(trackingArea)
        }
        
        let options: NSTrackingArea.Options = [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect]
        let newTrackingArea = NSTrackingArea(rect: .zero, options: options, owner: self, userInfo: nil)
        self.addTrackingArea(newTrackingArea)
        self.trackingArea = newTrackingArea
    }

        
    // 鼠标进入按钮区域，切换为悬停状态图片
    override func mouseEntered(with event: NSEvent) {
        if isEnabled {
            if !images.isEmpty {
                self.image = images[.hover] ?? images[.normal]
            } else {
                self.image = nil
                layer?.backgroundColor = backgroundColors[.hover]?.cgColor ?? backgroundColors[.normal]?.cgColor
            }
            
            self.contentTintColor = titleColors[.hover] ?? titleColors[.normal]
        }
    }

    // 鼠标离开按钮区域，切换为正常状态图片
    override func mouseExited(with event: NSEvent) {
        if isEnabled {
            if !images.isEmpty {
                self.image = images[.normal]
            } else {
                self.image = nil
                layer?.backgroundColor = backgroundColors[.normal]?.cgColor
            }
            self.contentTintColor = titleColors[.normal]
        }
    }

    // 按下按钮，切换为按下状态图片
    override func mouseDown(with event: NSEvent) {
        if isEnabled {
            if !images.isEmpty {
                self.image = images[.pressed] ?? images[.normal]
            } else {
                self.image = nil
                layer?.backgroundColor = backgroundColors[.pressed]?.cgColor ?? backgroundColors[.normal]?.cgColor
            }
            self.contentTintColor = titleColors[.pressed] ?? titleColors[.normal]
        }
        super.mouseDown(with: event)
    }

    // 松开按钮后恢复到悬停或正常状态图片
    override func mouseUp(with event: NSEvent) {
        if isEnabled {
            if !images.isEmpty {
                self.image = images[.hover] ?? images[.normal]
            } else {
                self.image = nil
                layer?.backgroundColor = backgroundColors[.hover]?.cgColor ?? backgroundColors[.normal]?.cgColor
            }
            self.contentTintColor = titleColors[.hover] ?? titleColors[.normal]
        }
        super.mouseUp(with: event)
    }

    // 设置按钮禁用状态的背景图片
    override var isEnabled: Bool {
        didSet {
            if !images.isEmpty {
                self.image = isEnabled ? images[.normal] : images[.disabled] ?? images[.normal]
            } else {
                self.image = nil
                layer?.backgroundColor = isEnabled ? backgroundColors[.normal]?.cgColor : (backgroundColors[.disabled]?.cgColor ??  backgroundColors[.normal]?.cgColor)
            }
            
            self.contentTintColor = isEnabled ? titleColors[.normal]  : titleColors[.disabled] ?? titleColors[.normal]
        }
    }
}

