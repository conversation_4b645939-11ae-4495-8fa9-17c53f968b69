//
//  LauncherLocalization.swift
//  ZhuXianWorldLanucher
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/1/23.
//

import Foundation
// 定义一个支持的语言枚举类型，每个类型对应一个字符串
enum SupportedLanguage: String {
    case zhHans = "zh-<PERSON>" // 简体中文
    case zhHant = "zh-Hant" // 繁体中文
    func interceptKey() -> String {
        switch self {
        case .zhHans:
            return "zh-cn"
        case .zhHant:
            return "zh-tw"
        }
    }
}

class LauncherLocalization {
    
    // 当前语言
    private static var currentLanguage: String = SupportedLanguage.zhHans.rawValue
    private static var languageBundle: Bundle? = bundleForLanguage(currentLanguage)
    private static let defaultLanguage: String = SupportedLanguage.zhHans.rawValue
    
    // 获取指定语言的 Bundle
    private static func bundleForLanguage(_ language: String) -> Bundle? {
        let bundle = Bundle.launcherMainBundle()
        guard let path = bundle.path(forResource: language, ofType: "lproj"),
              let languageBundle = Bundle(path: path) else {
            return bundle
        }
        return languageBundle
    }
    
    // 设置当前语言
    static func setLanguage(_ language: String) {
        currentLanguage = language
        languageBundle = bundleForLanguage(language) ?? bundleForLanguage(defaultLanguage)
    }
    
    // 获取本地化字符串
    static func localizedString(forKey key: String, comment: String = "") -> String {
        guard let bundle = languageBundle else {
            return key
        }
        return NSLocalizedString(key, bundle: bundle, comment: comment)
    }
    
    static func getCuttentLanguage() -> SupportedLanguage {
        if let language = SupportedLanguage(rawValue: currentLanguage) {
            return language
        }
        return .zhHans
    }
}
