//
//  AppDelegate.swift
//  ZhuXianWorldLanucher
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/4.
//

import Cocoa
import Sparkle
class LauncherAppDelegate: NSObject, NSApplicationDelegate {
    
    var window: NSWindow!
    var updaterController: SPUStandardUpdaterController!
    var windowDidMoved : Bool = false
    var forcedUpdateAlertView : LauncherCommonAlertView? //强制更新弹框
    var exitChoiceType : LauncherExitChoiceType = .unknown
    var shortcutKeyType : LauncherShortcutKeyType = .exit
    
    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // Insert code here to initialize your application
        // 初始化依赖的SDK(统计、crash)
        addAppVersionInfoLog()
        
        LauncherPatcherManager.shared.setupDependencySDK()
        LauncherEventManager.enterLauncher()
        
        // 确认语言环境
        if let regionLanguage = Bundle.main.object(forInfoDictionaryKey: "CFBundleDevelopmentRegion") as? String {
            LauncherLocalization.setLanguage(regionLanguage)
        }
        // 初始化WindowScaleleFactor
        LaunchUIConstant.MainWindowScaleleFactor = LaunchUIManager.shared.loadMainWindowScalingFactor()
        
        NSApp.appearance = NSAppearance(named: .vibrantDark)
        addMainMenu()
        let contentRect = NSRect(x: 0, y: 0, width: LaunchUIConstant.MainWindowWidth, height: LaunchUIConstant.MainWindowHeight)
        window = NSWindow(
            contentRect: contentRect,
            styleMask: [.titled, .fullSizeContentView], //.fullSizeContentView
            backing: .buffered,
            defer: false
        )
        window.makeKeyAndOrderFront(nil)
        window.title = Bundle.main.object(forInfoDictionaryKey: kCFBundleNameKey as String) as? String ?? ""
        let viewController = LauncherMainViewController()
        window.contentViewController = viewController
        window.titleVisibility = .hidden
        window.titlebarAppearsTransparent = true
        window.styleMask.insert(.miniaturizable)
        window.styleMask.insert(.closable)
        window.center()
        window.delegate = self
        
        // 设置Sparkle更新检测功能
        updaterController = SPUStandardUpdaterController.init(updaterDelegate: self, userDriverDelegate: self)
        LauncherSettingsManager.shared.updaterController = updaterController
        
        // 添加窗口移动事件监听，处理在不同分辨率显示器间切换时，窗口大小的变化
        addMonitorForLeftMouseUpEvents()
        
        // 启动后自动后台检查更新
        updaterController.updater.checkForUpdatesInBackground()
    }
    
    func applicationDidBecomeActive(_ notification: Notification) {
        // 用户点击了 Dock 图标时重新显示窗口
        if let window = window, !window.isVisible {
            window.makeKeyAndOrderFront(nil)
        }
    }
    
    func applicationShouldHandleReopen(_ sender: NSApplication, hasVisibleWindows flag: Bool) -> Bool {
        if !flag {
            // 当前无可见窗口 → 恢复主窗口
            window.makeKeyAndOrderFront(nil)
        }
        return true
    }
    
    func applicationWillTerminate(_ aNotification: Notification) {
        // Insert code here to tear down your application
        LauncherPatcherManager.shared.uninitPatcher()
        
        LauncherEventManager.closeLauncher()
    }
    
    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        if shortcutKeyType != .exit  {
            shortcutKeyType = .exit //还原shortcutKeyType状态
            return false
        }
        return true
    }
    
    
    func addMainMenu() {
        
        var mainMenu = NSApplication.shared.mainMenu
        if mainMenu == nil {
            mainMenu = NSMenu()
            NSApplication.shared.mainMenu = mainMenu
            let appMenuItem = NSMenuItem()
            mainMenu?.addItem(appMenuItem)
        }
        
        if let appMenuItem = mainMenu?.item(at: 0) {
            appMenuItem.title = Bundle.main.object(forInfoDictionaryKey: kCFBundleNameKey as String) as? String ?? ""
            
            let appSubmenu = NSMenu()
            appMenuItem.submenu = appSubmenu
            appSubmenu.addItem(withTitle: LauncherLocalization.localizedString(forKey: "about"), action: #selector(menuAboutAction(_:)), keyEquivalent: "")
            appSubmenu.addItem(withTitle: LauncherLocalization.localizedString(forKey: "feedback"), action: #selector(menuFeedbackAction(_:)), keyEquivalent: "")
            appSubmenu.addItem(withTitle: LauncherLocalization.localizedString(forKey: "check_update"), action: #selector(checkForUpdates(_:)), keyEquivalent: "")
            appSubmenu.addItem(withTitle: AppOperateType.openAppDir.description(), action: #selector(openAppDirAction(_:)), keyEquivalent: "")
//            appSubmenu.addItem(withTitle: AppOperateType.cleanCache.description(), action: #selector(cleanAppCacheAction(_:)), keyEquivalent: "")
            
            if LauncherPatcherManager.shared.isSupportMultiAppSwitch() == false {
                appSubmenu.addItem(withTitle: AppOperateType.resRepair.description(), action: #selector(repairAppAction(_:)), keyEquivalent: "")
                appSubmenu.addItem(withTitle: AppOperateType.removeApp.description(), action: #selector(removeAppAction(_:)), keyEquivalent: "")
            }
            
            appSubmenu.addItem(
                withTitle: LauncherLocalization.localizedString(forKey: "minimize"), action: #selector(miniaturizeAppAction(_:)), keyEquivalent: "m")
            appSubmenu.addItem(
                withTitle: LauncherLocalization.localizedString(forKey: "hide"), action: #selector(hideAppAction(_:)), keyEquivalent: "h")
            appSubmenu.addItem(
                withTitle: LauncherLocalization.localizedString(forKey: "close"), action: #selector(closeAppAction(_:)), keyEquivalent: "w")
            appSubmenu.addItem(
                withTitle: LauncherLocalization.localizedString(forKey: "exit"), action: #selector(exitAppAction(_:)), keyEquivalent: "q")
        }
        
        // 为支持NSTextView中键盘的复制、粘贴事件
        let editMenu = NSMenuItem()
        mainMenu?.addItem(editMenu)
        let editSubmenu = NSMenu(title: "Edit")
        editMenu.submenu = editSubmenu
        editSubmenu.addItem(withTitle: "Cut", action: #selector(NSText.cut(_:)), keyEquivalent: "x")
        editSubmenu.addItem(withTitle: "Copy", action: #selector(NSText.copy(_:)), keyEquivalent: "c")
        editSubmenu.addItem(withTitle: "Paste", action: #selector(NSText.paste(_:)), keyEquivalent: "v")
        editSubmenu.addItem(withTitle: "Select All", action: #selector(NSText.selectAll(_:)), keyEquivalent: "a")
        editMenu.isHidden = true
    }
    
    @objc func menuAboutAction(_ sender: NSMenuItem) {
        let options: [NSApplication.AboutPanelOptionKey: Any] = [
            .applicationName: Bundle.main.object(forInfoDictionaryKey: kCFBundleNameKey as String) as? String ?? "" 
        ]
        NSApplication.shared.orderFrontStandardAboutPanel(options: options)
    }
    
    @objc func menuFeedbackAction(_ sender: NSMenuItem) {
        LauncherSettingsManager.openFeedbackAction()
    }
    
    @objc func checkForUpdates(_ sender: NSMenuItem) {
        LauncherSettingsManager.checkForUpdates()
    }
    
    @objc func openAppDirAction(_ sender: NSMenuItem) {
        LauncherSettingsManager.openAppDirAction()
    }
    
    @objc func removeAppAction(_ sender: NSMenuItem) {
        LauncherSettingsManager.removeAppAction()
    }
    
    @objc func repairAppAction(_ sender: NSMenuItem) {
        let _ = LauncherSettingsManager.resRepairAction()
    }
           
    @objc func cleanAppCacheAction(_ sender: NSMenuItem) {
        LauncherSettingsManager.cleanAppCacheAction()
    }
    @objc func miniaturizeAppAction(_ sender: NSMenuItem) {
        window.performMiniaturize(nil)
    }
    
    @objc func hideAppAction(_ sender: NSMenuItem) {
        shortcutKeyType = .hide
        NSApp.hide(nil)
        
    }
    
    @objc func closeAppAction(_ sender: NSMenuItem) {
        shortcutKeyType = .close
        window.orderOut(nil)
        
    }
    
    @objc func exitAppAction(_ sender: NSMenuItem) {
        shortcutKeyType = .exit
        window.performClose(nil)
    }
}

// MARK: - NSWindowDelegate
extension LauncherAppDelegate:NSWindowDelegate {
    func windowShouldClose(_ sender: NSWindow) -> Bool {
        let confirmExit = confirmTerminateInDownloadTask()
        if confirmExit {
            // 退出时，关闭所有已打开的窗口
            for window in NSApp.windows {
                window.close()
            }
        }
        return confirmExit
    }
    
    func applicationShouldTerminate(_ sender: NSApplication) -> NSApplication.TerminateReply {
        if exitChoiceType == .unknown {
            window.deminiaturize(nil)
            if #available(macOS 14.0, *) {
                NSApp.activate()
            } else {
                NSApp.activate(ignoringOtherApps: true)
            }
        }
        
        let confirmExit = confirmTerminateInDownloadTask()
        if confirmExit {
            return .terminateNow
        } else {
            return .terminateCancel
        }
    }
    
    func confirmTerminateInDownloadTask() -> Bool {
        if exitChoiceType == .cancel {
            return false
        } else if exitChoiceType == .confirm {
            return true
        }
        
        if LauncherPatcherManager.shared.isDownloadingTask()  {
            // 弹出确认关闭的警告框
            let alert = NSAlert()
            alert.messageText = ""
            alert.informativeText = LauncherLocalization.localizedString(forKey: "downloading_with_exit")
            alert.alertStyle = .warning
            alert.addButton(withTitle: LauncherLocalization.localizedString(forKey: "cancel"))
            alert.addButton(withTitle: LauncherLocalization.localizedString(forKey: "confirm"))
            
            let response = alert.runModal()
            let isConfirm = (response == .alertSecondButtonReturn)
            if isConfirm {
                LauncherPatcherManager.shared.pauseDownload()
                LauncherEventManager.gameDownloadingCloseConfirm()
                exitChoiceType = .confirm
            } else {
                LauncherEventManager.gameDownloadingCloseCancle()
                exitChoiceType = .cancel
                DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                    self.exitChoiceType = .unknown  // 过一段时间后允许再次取消
                }
            }
            return isConfirm
        } else {
            exitChoiceType = .confirm
            return true
        }
    }
    
}

// MARK: - SPUUpdaterDelegate
extension LauncherAppDelegate:SPUUpdaterDelegate {
    // debug版本允许查看beta渠道的更新包，非debug版本只能查看默认渠道的更新包
    func allowedChannels(for updater: SPUUpdater) -> Set<String> {
#if DEBUG
        return Set(["beta"])
#else
        return Set([])
#endif
    }
    
    func updater(_ updater: SPUUpdater, didFinishLoading appcast: SUAppcast) {
#if DEBUG
        for item in appcast.items {
            //打印itemver中版本号
            log.debug("[\(LogModule.checkUpdates)] appVersion=\(item.title ?? ""),buildVersion=\(item.versionString),channel=\(item.channel ?? "")")
        }
#endif
    }
    
    func updaterWillRelaunchApplication(_ updater: SPUUpdater) {
        self.forcedUpdateAlertView?.dismiss()
    }
    
    // 自更新检查
    func updater(_ updater: SPUUpdater, didFinishUpdateCycleFor updateCheck: SPUUpdateCheck, error: (any Error)?) {
        if let updaterError = error as? NSError, updaterError.code != SUError.noUpdateError.rawValue {
            log.emergency("[\(LogModule.checkUpdates)] didFinishUpdateCycleFor error=\(updaterError)")
        }
    }
}

extension LauncherAppDelegate:SPUStandardUserDriverDelegate {

    /* 强制更新逻辑，通过弹出Alert屏蔽操作和重复弹出Sparkle更新下载框实现
    func standardUserDriverShouldHandleShowingScheduledUpdate(_ update: SUAppcastItem, andInImmediateFocus immediateFocus: Bool) -> Bool {
        print("title=\(update.title ?? ""),versionString=\(update.versionString),channel=\(update.channel ?? ""),isCriticalUpdate=\(update.isCriticalUpdate)")
        if update.isCriticalUpdate {
            return false
        }
        return true
    }
    
    func standardUserDriverWillHandleShowingUpdate(_ handleShowingUpdate: Bool, forUpdate update: SUAppcastItem, state: SPUUserUpdateState) {
        if handleShowingUpdate {
            print("Sparkle will handle showing the update.")
        } else {
            print("Custom handling for update: \(update.versionString)")
            
            let updateAlertView = LauncherCommonAlertView.setup(title: "重要更新", message: "发现重要更新版本，点击立即更新")
           
            updateAlertView.add(action: LauncherAlertAction.actionWithTitle("更新", handler: {[weak self] action in
                self?.updaterController.updater.checkForUpdates()
                self?.showWaittingforInstallSession()
            }))
            updateAlertView.show(inView: nil)
            
        }
    }
    
    func standardUserDriverWillFinishUpdateSession() {
        // 触发时机：关闭、跳过更新、发生更新错误后，强制更新时，可再次重新检查
        let updateAlertView = LauncherCommonAlertView.setup(title: "重要更新", message: "发现重要更新版本，点击立即更新")
       
        updateAlertView.add(action: LauncherAlertAction.actionWithTitle("更新", handler: {[weak self] action in
            self?.updaterController.updater.checkForUpdates()
        }))
        updateAlertView.show(inView: nil)
    }
    
    func showWaittingforInstallSession() {
        let updateAlertView = LauncherCommonAlertView.setup(title: "提示", message: "发现重要更新版本，正在等待更新")
        updateAlertView.show(inView: nil)
        self.forcedUpdateAlertView = updateAlertView
    }
    */
}

// 处理在不同分辨率屏幕间拖拽后，主窗口大小自适应
extension LauncherAppDelegate {
    func addMonitorForLeftMouseUpEvents() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(windowDidMoveNotification),
            name: NSWindow.didMoveNotification,
            object: nil
        )
        
        let _ = NSEvent.addLocalMonitorForEvents(matching: .leftMouseUp) { event in
            if self.windowDidMoved {
                let scale = LaunchUIManager.shared.loadMainWindowScalingFactor()
                if scale != LaunchUIConstant.MainWindowScaleleFactor {
                    LaunchUIConstant.MainWindowScaleleFactor = scale
                    NotificationCenter.default.post(name: .LauncherMainWindowScreenDidNotification, object: nil)
                }
                
                self.windowDidMoved = false
            }
            return event
        }
    }
    
    @objc func windowDidMoveNotification() {
        windowDidMoved = true
    }
}


// 打印版本信息日志
extension LauncherAppDelegate {
    func addAppVersionInfoLog() {
        let launcherAppName = Bundle.main.object(forInfoDictionaryKey: kCFBundleNameKey as String) as? String ?? "Launcher"
        var envValue = "[\(LogModule.base)] \(launcherAppName) env="
        
#if LAUNCHER_ENV_DEVELOP
        envValue += "dev"
#elseif LAUNCHER_ENV_TEST
        envValue += "test"
#else
        envValue += "ob"
#endif
        
#if LAUNCHER_TYPE_BENCHMARK
        envValue += ", isBenchmark"
#endif
    
        log.info(envValue)
    }
}
