//
//  LauncherEventManager.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/12.
//

import Foundation
import WMLauncherCore

class LauncherEventManager {
    static let shared = LauncherEventManager()
    
    //切换游戏后，修改打点中ostype
    public func changeEvent(ostype: String?) {
        WMMacLauncherMonitor.sharedInstance().changeEventOSType(ostype)
    }
    
    //通用打点接口
    static func trackEvent(_ name: String, info: [String: Any]?) {
        WMMacLauncherMonitor.event(name, attributes: info)
    }
    
    //打开启动器
    static func enterLauncher() {
        LauncherEventManager.trackEvent("enterLauncher", info: nil)
        log.debug("[\(LogModule.base)] enterLauncher")
    }
    
    //关闭启动器
    static func closeLauncher() {
        LauncherEventManager.trackEvent("closeLauncher", info: nil)
        log.debug("[\(LogModule.base)] closeLauncher")
    }
    
    //暂停下载
    static func gameAssetDownloadPause() {
        LauncherEventManager.trackEvent("gameAssetDownloadPause", info: nil)
    }
    
    //继续下载
    static func continuegameAssetDownloadContinue() {
        LauncherEventManager.trackEvent("continuegameAssetDownloadContinue", info: nil)
    }
    
    //资源下载成功
    static func gameAssetDownloadSuccesss() {
        LauncherEventManager.trackEvent("gameAssetDownloadSuccesss", info: nil)
    }
    
    //开始游戏
    static func launcherStartGame() {
        LauncherEventManager.trackEvent("launcherStartGame", info: nil)
    }
    
    //点击修复
    static func clickRepair() {
        LauncherEventManager.trackEvent("clickRepair", info: nil)
    }
    
    //取消修复
    static func cancleRepair() {
        LauncherEventManager.trackEvent("cancleRepair", info: nil)
    }
    
    //游戏正在下载中关闭确认
    static func gameDownloadingCloseConfirm() {
        LauncherEventManager.trackEvent("gameDownloadingCloseConfirm", info: nil)
    }
    
    //游戏正在下载中关闭取消
    static func gameDownloadingCloseCancle() {
        LauncherEventManager.trackEvent("gameDownloadingCloseCancle", info: nil)
    }
    
    //多版本切换打点
    static func clickChooseGameEditionButton(abbr: String?) {
        var versionInfo : [String: Any] = [:]
        if let abbrVal = abbr {
            versionInfo["abbr"] = abbrVal
        }
        LauncherEventManager.trackEvent("clickChooseGameEditionButton", info:versionInfo)
    }
    

}
