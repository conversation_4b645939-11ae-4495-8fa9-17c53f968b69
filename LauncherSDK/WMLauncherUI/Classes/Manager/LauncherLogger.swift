//
//  LauncherLogger.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/4/3.
//
import WMXCGLogger

// 根据Launcher功能模块定义的日志tag
struct LogModule {
    static let base = "base"
    static let checkUpdates = "checkUpdates"
    static let ui = "ui"
    static let core = "core"
    static let resfix = "resfix"
    static let setting = "setting"
}

let log: XCGLogger = {
    // Setup XCGLogger (Advanced/Recommended Usage)
    // Create a logger object with no destinations
    let log = XCGLogger(identifier: "advancedLogger", includeDefaultDestinations: false)
#if DEBUG
    // Create a destination for the system console log (via NSLog)
    let systemDestination = AppleSystemLogDestination(identifier: "advancedLogger.appleSystemLogDestination")

    // Optionally set some configuration options
    systemDestination.outputLevel = .debug
    systemDestination.showLogIdentifier = false
    systemDestination.showThreadName = true
    systemDestination.showLevel = true

    systemDestination.showFunctionName = true
    systemDestination.showFileName = true
    systemDestination.showLineNumber = true

    // Add the destination to the logger
    log.add(destination: systemDestination)
#endif
    
    // Create a file log destination
    let fileManager = FileManager.default
    // 检查路径是否存在
    if !fileManager.fileExists(atPath: LauncherAppDataManager.shared.launcherLogDirPath) {
        try? fileManager.createDirectory(atPath: LauncherAppDataManager.shared.launcherLogDirPath, withIntermediateDirectories: true, attributes: nil)
    }
    
    let logPath: String = LauncherAppDataManager.shared.launcherLogFullPath
    let fileURL = NSURL(fileURLWithPath: logPath)
    let autoRotatingFileDestination = AutoRotatingFileDestination(writeToFile: fileURL,
                                                                  identifier: "advancedLogger.fileDestination",
                                                                  shouldAppend: true,
                                                                  appendMarker: "-- ** ** ** ** ** ** ** ** ** ** ** ** ** ** **  --",
                                                                  maxFileSize: 1024 * 1024 * 2, // 文件最大size, 2M
                                                                  maxTimeInterval: 60 * 60 * 24, // 日志间最大时间间隔， 24小时
                                                                  targetMaxLogFiles: 5) // 保存的最大日志个数

    // Optionally set some configuration options
    autoRotatingFileDestination.outputLevel = .debug
    autoRotatingFileDestination.showLogIdentifier = false
    autoRotatingFileDestination.showThreadName = true
    autoRotatingFileDestination.showLevel = true
#if !DEBUG
    autoRotatingFileDestination.showFunctionName = false
    autoRotatingFileDestination.showFileName = false
    autoRotatingFileDestination.showLineNumber = false
#endif
    autoRotatingFileDestination.showDate = true

    // Process this destination in the background
    autoRotatingFileDestination.logQueue = XCGLogger.logQueue

    // Add the destination to the logger
    log.add(destination: autoRotatingFileDestination)
    return log
}()
