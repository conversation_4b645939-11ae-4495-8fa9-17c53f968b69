//
//  LauncherSettingsManager.swift
//  WMLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/27.
//

import Foundation
import Sparkle

class LauncherSettingsManager {
    static let shared = LauncherSettingsManager()
    public weak var updaterController: SPUStandardUpdaterController?
    private let latestCacheAppIdentifyKey = "LauncherSelectAppIdentify"
    private let targetClientEnvKey = "targetClientEnvKey"
    private let autoUpdateClientKey = "autoUpdateClientKey"
    private let selectedAppVersionKey = "selectedAppVersionKey"
    public var isOpenFeedbackView = false
    
    // 保存最后操作的App标识
    public var latestSelectAppIdentify: String? {
        get {
            return UserDefaults.standard.string(forKey: latestCacheAppIdentifyKey)
        }
        set {
            if let newValue = newValue {
                UserDefaults.standard.set(newValue, forKey: latestCacheAppIdentifyKey)
                UserDefaults.standard.synchronize()
            }
        }
    }
    
    // 侧边栏展开、收起状态
    public var sidebarCloseStatus: Bool {
        get {
            return UserDefaults.standard.bool(forKey: "LauncherSidebarCollapsedIdentify")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "LauncherSidebarCollapsedIdentify")
            UserDefaults.standard.synchronize()
        }
    }
    
    // Dev版本中，记录打开的目标.app环境 (shipping、test、dev)
    public var targetClientEnv: LauncherTargetClientEnv! {
        get {
            let targetClinetAppIdentify = appendIdentifyForKey(targetClientEnvKey)
            if UserDefaults.standard.object(forKey: targetClinetAppIdentify) != nil {
                return LauncherTargetClientEnv(rawValue: UserDefaults.standard.integer(forKey: targetClinetAppIdentify)) ?? .shipping
            } else {
                return .shipping
            }
        }
        set {
            if let newValue = newValue {
                let targetClinetAppIdentify = appendIdentifyForKey(targetClientEnvKey)
                UserDefaults.standard.set(newValue.rawValue, forKey: targetClinetAppIdentify)
                UserDefaults.standard.synchronize()
            }
        }
    }
    
    // Dev版本中，记录手动更新 or 自动更新
    public var autoUpdateClient: Bool! {
        get {
            let autoUpdatetAppIdentify = appendIdentifyForKey(autoUpdateClientKey)
            if UserDefaults.standard.object(forKey: autoUpdatetAppIdentify) != nil {
                return UserDefaults.standard.bool(forKey: autoUpdatetAppIdentify)
            } else {
                return true
            }
        }
        set {
            if let newValue = newValue {
                let autoUpdatetAppIdentify = appendIdentifyForKey(autoUpdateClientKey)
                UserDefaults.standard.set(newValue, forKey: autoUpdatetAppIdentify)
                UserDefaults.standard.synchronize()
            }
        }
    }
    
    // Dev版本中，记录最后选择的版本号
    public var selectedAppVersion: String? {
        get {
            let selectedAppIdentify = appendIdentifyForKey(selectedAppVersionKey)
            return UserDefaults.standard.string(forKey: selectedAppIdentify)
        }
        set {
            let selectedAppIdentify = appendIdentifyForKey(selectedAppVersionKey)
            if let newValue = newValue {
                UserDefaults.standard.set(newValue, forKey: selectedAppIdentify)
                UserDefaults.standard.synchronize()
            } else {
                UserDefaults.standard.removeObject(forKey: selectedAppIdentify)
            }
        }
    }
    
    // 设置界面中，播放背景视频
    public var playBgVideo: Bool {
        get {
            return UserDefaults.standard.object(forKey: "playBgVideoKey") == nil ? true : UserDefaults.standard.bool(forKey: "playBgVideoKey")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "playBgVideoKey")
            UserDefaults.standard.synchronize()
            NotificationCenter.default.post(name: .LauncherPlayerVideoSwitchDidNotification, object: nil)
        }
    }
    
    // 游戏是否自动更新
    public var gameAutoUpdate: Bool {
        get {
            return UserDefaults.standard.bool(forKey: "gameAutoUpdateKey")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "gameAutoUpdateKey")
            UserDefaults.standard.synchronize()
        }
    }
    
    // 下载速度设置
    public var downloadSpeedLimit: String? {
        get {
            return UserDefaults.standard.string(forKey: "downloadSpeedLimitKey")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "downloadSpeedLimitKey")
            UserDefaults.standard.synchronize()
        }
    }
    
    // 下载线路，记录的"1","2","3", "0"或nil为默认
    public var downloadRoute: String? {
        get {
            return UserDefaults.standard.string(forKey: "downloadRouteKey")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "downloadRouteKey")
            UserDefaults.standard.synchronize()
        }
    }
    
    // 获取线路gameResurl
    public func getDownloadResUrls() -> [String] {
        if let resURLs = LauncherPatcherManager.shared.getCurrentPatchrResUrls() {
            return resURLs
        } else {
            return [""]
        }
    }
    
    private func appendIdentifyForKey(_ key: String) -> String {
        return key + "_" + (latestSelectAppIdentify ?? "")
    }
    
    public static func openFeedbackAction() {
        if LauncherSettingsManager.shared.isOpenFeedbackView {
            // 已经有意见反馈显示了，不再重复显示
            return
        }
        LauncherSettingsManager.shared.isOpenFeedbackView = true
        
        let feedbackVC = FeedbackViewController()
        feedbackVC.showViewController()
    }
    
    public static func checkForUpdates() {
        LauncherSettingsManager.shared.updaterController?.updater.checkForUpdates()
    }
    
    public static func openAppDirAction() {
        let selectedIndex = LauncherPatcherManager.shared.currentSelectedAppIndex
        LauncherPatcherManager.shared.siderBarItemAppOperateAction(type: .openAppDir, index:selectedIndex )
    }
    
    public static func removeAppAction() {
        let selectedIndex = LauncherPatcherManager.shared.currentSelectedAppIndex
        LauncherPatcherManager.shared.siderBarItemAppOperateAction(type: .removeApp, index: selectedIndex)
    }
          
    
    public static func resRepairAction() -> Bool {
        let selectedIndex = LauncherPatcherManager.shared.currentSelectedAppIndex
        let canRepair = LauncherPatcherManager.shared.canBeginRepair(index: selectedIndex)
        if !canRepair {
            LauncherPatcherManager.shared.showCommonAlertView(message: LauncherLocalization.localizedString(forKey: "unsupport_repair"))
            return false
        }
        LauncherPatcherManager.shared.siderBarItemAppOperateAction(type: .resRepair, index: selectedIndex)
        return true
    }
    
    public static func cleanAppCacheAction() {
        let selectedIndex = LauncherPatcherManager.shared.currentSelectedAppIndex
        LauncherPatcherManager.shared.siderBarItemAppOperateAction(type: .cleanCache, index: selectedIndex)
    }
    
    public static func openUserAgreementTermsAction() {
        if let protocolListUrl = LaunchUIConstant.LauncherConfig?.config.protocolList {
            let url = URL(string: protocolListUrl, relativeTo: nil)
            NSWorkspace.shared.open(url!)
        }
    }
    
    public static func openExternalBrowser(url : String!) {
        let url = URL(string: url, relativeTo: nil)
        NSWorkspace.shared.open(url!)
    }
}
