//
//  LauncherInterceptManager.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/4/24.
//

import WMAFNetworking

struct LauncherInterceptModel: Codable {
    let install: InstallInterceptModel?
    let game: GameInterceptModel?
}

struct InstallInterceptModel: Codable {
    let canStartInstall: Bool?
    let message: [String: String]?
}

struct GameInterceptModel: Codable {
    let canStartGame: Bool?
    let message: [String: String]?
}

// 拦截类型
enum LauncherInterceptType {
    case install
    case startGame
}

import Foundation
public class LauncherInterceptManager {
    public static let shared = LauncherInterceptManager()
    private var interceptUrlIndex = 0
    
    private func getInterceptUrl() -> String? {
        if let urls = LaunchUIConstant.LauncherConfig?.config.interceptUrl {
            if interceptUrlIndex >= urls.count || interceptUrlIndex < 0 {
                interceptUrlIndex = 0
            }
            let interceptUrl = urls[interceptUrlIndex]
            // 针对requestUrl后缀增加时间戳，防止缓存
            let timestamp = Date().timeIntervalSince1970
            let separator = interceptUrl.contains("?") ? "&" : "?"
            let urlWithTimestamp = "\(interceptUrl)\(separator)ts=\(timestamp)"
            return urlWithTimestamp
        }
        return nil
    }
    
    func canStartInstall(handler:((Bool) -> Void)? = nil) {
        canStart(type: .install, interceptUrl: getInterceptUrl(), handler: handler) {[weak self] in
            self?.canStartInstall(handler: handler)
        }
    }
    
    
    func canStartGame(handler:((Bool) -> Void)? = nil, retryHandler:(() -> Void)? = nil){
        canStart(type: .startGame, interceptUrl: getInterceptUrl(), handler: handler) {[weak self] in
            self?.canStartGame(handler: handler)
        }
    }
    
    func canStart(type:LauncherInterceptType, interceptUrl:String?, handler:((Bool) -> Void)? = nil, retryHandler:(() -> Void)? = nil) {
        if let requestUrl = interceptUrl {
            requestInterceptModel(interceptUrl: requestUrl, type: type) { status, isRequestSuccess, msg in
                if isRequestSuccess {
                    //确保是接口请求成功
                    if status {
                        //能下载or开始
                        handler?(true)
                    } else {
                        // 给出提示框
                        DispatchQueue.main.async {
                            // 不能下载or开始
                            handler?(false)
                            let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "blank_tips"), message: msg)
                            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "confirm"), handler: nil))
                            alertView.show(inView: nil)
                        }
                    }
                } else {
                    // 接口请求失败，给出提示框
                    DispatchQueue.main.async {
                        // 不能下载or开始
                        handler?(false)
                        let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "blank_tips"), message: msg)
                        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "retry"), handler: { action in
                            retryHandler?()
                        }))
                        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "cancel"), handler: nil))
                        alertView.show(inView: nil)
                    }
                }
            }
        } else {
            handler?(true)
        }
    }
    
    
    // 请求网络拦截配置
    func requestInterceptModel(interceptUrl:String, type:LauncherInterceptType, handler:((Bool, Bool, String?) -> Void)? = nil) {
        let manager = WMAFHTTPSessionManager()
        manager.responseSerializer = WMAFJSONResponseSerializer()
        manager.responseSerializer.acceptableContentTypes = Set(["text/plain", "application/json", "text/html", "text/json", "text/javascript"])
        manager.requestSerializer.timeoutInterval = 10
        
        manager.get(interceptUrl,
                    parameters: nil,
                    progress: nil,
                    success: { (task, responseObject) in
            if let json = responseObject as? [String: Any] {
                if let data = try? JSONSerialization.data(withJSONObject: json, options: .prettyPrinted) {
                    let model = try? JSONDecoder().decode(LauncherInterceptModel.self, from: data)
                    let (status,message) = parseInterceptModel(model, type: type)
                    handler?(status, true, message)
                    return
                }
            }
            log.emergency("[\(LogModule.core)] Error: Could not parse intercept data")
            // model解析失败，返回默认值
            let errorMessage = String(format: LauncherLocalization.localizedString(forKey: "network_error"), LauncherErrorCode.parsingFailed.rawValue)
            handler?(false, false, errorMessage)
        },
                    failure: { [weak self] (task, error) in
            // 失败，切换url index
            self?.interceptUrlIndex += 1
            
            var errorCode = LauncherErrorCode.networkError.rawValue
            if let nsError = error as NSError?  {
                errorCode = nsError.code
            }
            log.emergency("[\(LogModule.core)] Error: request intercept data failed, code:\(errorCode), message: \(error.localizedDescription)")
            let errorMessage = String(format: LauncherLocalization.localizedString(forKey: "network_error"), errorCode)
            handler?(false, false, errorMessage)
        })

        func parseInterceptModel(_ model:LauncherInterceptModel?, type:LauncherInterceptType) -> (Bool, String?) {
            // 解析的
            switch type {
            case .install:
                if let installModel = model?.install {
                    return (installModel.canStartInstall ?? true, interceptShowMessage(installModel.message))
                }
            case.startGame: 
                if let gameModel = model?.game {
                    return (gameModel.canStartGame ?? true, interceptShowMessage(gameModel.message))
                }
            }
            return (true, nil)
        }
        
        func interceptShowMessage(_ infoDic: [String: String]?) -> String {
            let interceptMsgKey = LauncherLocalization.getCuttentLanguage().interceptKey()
            if let interceptInfo = infoDic {
                if let areaMsg = interceptInfo[interceptMsgKey] {
                    return areaMsg
                }
                
                // 没找到对应地区的提示，返回英文的
                if let areaMsg = interceptInfo["en"] {
                    return areaMsg
                }
                
                // 没有英文，返回首个
                // 判断interceptInfo中key的个数，如果大于1个，就返回首个
                if interceptInfo.count > 0 {
                    return interceptInfo.values.first ?? "Not open yet, please try again"
                }
            }
            return "Not open yet, please try again"
        }
    }
}
