//
//  LauncherMainViewModel.swift
//  WMMacLauncherUI
//
//  Created by z<PERSON>jia on 2024/11/8.
//

import Foundation
import WMLauncherCore
import Combine
import WMXAppKit
import WMDevice

extension String {
    func appendingPathComponent(_ component: String) -> String {
        let nsString = self as NSString
        return nsString.appendingPathComponent(component)
    }
}

class LauncherPatcherManager {
    static let shared = LauncherPatcherManager()
    private var launcherName:String! = ""
    
    private var launcherCoreManager:WMMacLauncherCore!
    private var patcherDataManager:LauncherAppDataManager!
    
    private var appClientExeName: String!
    private var patcherInitState = false
    public var currentSelectedAppIndex = 0
    @Published var currentInstallingAppIndex = -1
    @Published var updateStatus: PatcherDownloadStatus = .idle
    
    private var allAppStatusDict: [String: LauncherAppAllInfo] = [:]
    
    private var latestDownloadSpeed: Int64? //最后一次网速测试下载速度，B/s
    private var speedTestRetryCount: Int = 0 //自动测速重试次数，最大三次
    private var cachedDiskReadSpeedMB: Int? //缓存磁盘读写速度值
    private var runningAppUniqueId: String? //正在运行中的游戏标识
    
    var statePublisher = PassthroughSubject<(LauncherAppStatus,LauncherAppBranchVersionInfo?,LauncherAppDownloadInfo?,Int?), Never>()
    
    // patcher处理的App发生变化
    public var selectedAppStatusDidChage: ((Int?) -> Void)?
    public var selectedAppDidChage: ((MultiConfig?) -> Void)?
    // 调用patcher 取消下载后，任务取消完成回调
    public var patcherUpdateCancelledHandler: (() -> Void)?
    
    // MARK: - 初始化、各状态值获取
    private init() {
        launcherName = LaunchUIConstant.LauncherConfig?.config.launcherName
        launcherCoreManager = WMMacLauncherCore()
        patcherDataManager = LauncherAppDataManager.shared
        
        regiserPatcherCallback()
        
        // 判断当前所选游戏是否正在运行
        if let (uniqueId, bundleId) = patcherDataManager.runningAppUniqueInfo {
            addSubApplicationRunningObserve(bundleId, uniqueId:uniqueId)
        }
        
        // 设置Userdata工作目录
        launcherCoreManager.setUserdataWorkDirInfo(patcherDataManager.allBranchWorkPaths);
    }
    
    
    // 返回最后操作的App在multiConfig中的Index
    public var latestSelectAppIndex: Int {
        guard let launcherConfig = LaunchUIConstant.LauncherConfig else {
            return 0
        }
        
        guard let multiConfig = launcherConfig.config.multiConfig, let appIdentify = LauncherSettingsManager.shared.latestSelectAppIdentify else {
            return 0
        }
        if let index = multiConfig.firstIndex(where: { $0.uniqueId == appIdentify }) {
            return index
        } else {
            return 0
        }
    }
    
    public var currentAppAvailableVersions: [String] {
        let branchInfo = getSelectedAppBranchVersionInfo(index: currentSelectedAppIndex)
        return branchInfo?.avaliableVersionStrings ?? []
    }
    
    // 根据配置文件，判断是否支持多个App间切换
    public func isSupportMultiAppSwitch() -> Bool {
        if let launcherConfig = LaunchUIConstant.LauncherConfig,
           let multiConfig = launcherConfig.config.multiConfig,
           multiConfig.count > 1 {
            return true
        } else {
            return false
        }
    }
    public func multiAppConfig(index: Int?) -> MultiConfig? {
        var targetIndex = 0
        if let tempIndex = index {
            targetIndex = tempIndex
        } else {
            targetIndex = currentSelectedAppIndex
        }
        
        if let launcherConfig = LaunchUIConstant.LauncherConfig,
           let multiConfig = launcherConfig.config.multiConfig,
           targetIndex < multiConfig.count{
            return multiConfig[targetIndex]
        } else {
            return nil
        }
    }
    
    func sendStatePublisherToSubject(_ appStatus: LauncherAppStatus, _ versionInfo: LauncherAppBranchVersionInfo?, _ downloadInfo: LauncherAppDownloadInfo?, _ errorCode: Int? = 0) {
        statePublisher.send((appStatus,versionInfo,downloadInfo,errorCode))
    }
    
    // MARK: - 启动下载、更新，Patcher的初始化、反初始化
    public func beginPatcherDidSeletedApp(index: Int, forced: Bool, autoDownloadType: LauncherGameAutoDownloadType = .none) {
        currentSelectedAppIndex = index
        // 给出回调
        selectedAppStatusDidChage?(index)
        if let appConfig = multiAppConfig(index: index) {
            selectedAppDidChage?(appConfig)
        }
        
        // 缓存最后选中的App标识
        let baseInfo = getSelectedAppBaseInfo(index: currentSelectedAppIndex)
        LauncherSettingsManager.shared.latestSelectAppIdentify = baseInfo!.uniqueId
        
        // 判断当前所选游戏是否正在运行
        if let uniqueId = baseInfo?.uniqueId, isApplicationRunning(uniqueId: uniqueId) {
            let branchInfo = getSelectedAppBranchVersionInfo(index: nil)
            sendStatePublisherToSubject(.gameInProgress,branchInfo,nil)
            log.debug("[\(LogModule.core)] selected game in gameInProgress status")
        } else if !forced && currentInstallingAppIndex >= 0 {
            // patcher有正在下载
            let versionStatus = getSelectedAppBranchVersionStatus(index: nil)
            let branchInfo = getSelectedAppBranchVersionInfo(index: nil)
            let downloadInfo = getSelectedAppDownloadInfo(index: nil)
            sendStatePublisherToSubject(versionStatus,branchInfo,downloadInfo)
            log.debug("[\(LogModule.core)] switch game while in downloading(\(currentInstallingAppIndex)) status")
        } else {
            continueDidCancelDownloadingTask(operateType: .switchApp) { [weak self] bContinue in
                if bContinue {
                    // 发布检查状态
                    self?.sendStatePublisherToSubject(.checking,nil,nil)
                    self?.updateAppVersionCheckStatus(index: nil, status: .checking)
                    // 检查是否安装了游戏，及已安装的版本信息
                    self?.beginCheckLocalVersion(autoDownloadType:autoDownloadType)
                }
            }
        }
    }
    
    private func changePatcherTask(index: Int) {
        // 先反初始化，重置状态
        uninitPatcher()
        // 初始化patcher
        setupPatcher(index: index)
        
#if LAUNCHER_ENV_DEVELOP || LAUNCHER_ENV_TEST//只开发和测试环境包含这个功能
        if let seletedAppVersion = LauncherSettingsManager.shared.selectedAppVersion, LauncherSettingsManager.shared.autoUpdateClient == false {
            self.launcherCoreManager.changeSelectedVersion(seletedAppVersion)
        } else {
            self.launcherCoreManager.changeSelectedVersion("")
        }
#endif
    }
    
    
    // 初始化patcherSDK
    private func setupPatcher(index: Int) {
        guard let launcherAppConfig = getSelectedAppBaseInfo(index: index) else {
            return
        }
        
        let patcherPatchInfo = WMPatcherPathInfo()
        patcherPatchInfo.cacheBasePath = launcherAppConfig.cacheBasePath
        patcherPatchInfo.resPath = launcherAppConfig.resPath
        patcherPatchInfo.patcherPath = launcherAppConfig.patcherPath
        patcherPatchInfo.logPath = launcherAppConfig.logPath
        
        let result = launcherCoreManager.setupPatcher(withConfigPath: launcherAppConfig.patcherConfig, pathInfo: patcherPatchInfo, deviceId: "", mediaId: "")
        if result == 0 {
            patcherInitState = true
        } else {
            log.error("[\(LogModule.core)] Patcher init failed: \(result)")
        }
    }
    
    // Patcher反初始化
    func uninitPatcher() {
        if patcherInitState {
            launcherCoreManager.uninitPatcher()
            patcherInitState = false
        }
        
    }
    // 获取所有不同分支版本的版本信息
    public func loadAllBranchAppVersionInfo() {
        if !isSupportMultiAppSwitch() {
            return
        }
        
        Task {
            await getMutableBranchAppVersionInfo()
        }
    }
    
    // 启动时，获取所有不同分支游戏的版本信息(仅启动时获取一次)
    private func getMutableBranchAppVersionInfo() async {
        guard let launcherConfig = LaunchUIConstant.LauncherConfig else {
            return
        }
        
        // 遍历launcherConfig.config.multiConfig
        for appItemConfig in launcherConfig.config.multiConfig {
            let uniqueId = appItemConfig.uniqueId
            if let appPatchConfig = patcherDataManager.getBaseInfo(for: appItemConfig.uniqueId) {
                // 同步获取每个分支的版本信息
                if var appInfo = await self.requestBranchVersionInfo(configPath: appPatchConfig.patcherConfig,
                                                                     configContent: nil,
                                                                     patcherPath: appPatchConfig.patcherPath) {
                    if patcherDataManager.getVersionCheckStatus(for: uniqueId) == .unknown {
                        appInfo.appVersionName = appPatchConfig.displayAppName
                        patcherDataManager.updateVersionInfo(for: uniqueId, versionInfo: appInfo)
                        
                        var checkStatus : LauncherAppStatus = .unknown
                        if appInfo.localVersion.isEmpty {
                            // 本地还没版本，返回开始安装
                            checkStatus = .startDownloading
                        } else {
                            // 本地已有版本，判断版本是否一致
                            if appInfo.localVersion == appInfo.remoteVersion {
                                // 本地和远端版本一致
                                checkStatus = .startGame
                            } else {
                                //本地和远端版本不一致
                                checkStatus = .needUpdating
                            }
                        }
                        patcherDataManager.updateVersionCheckStatus(for: uniqueId, status: checkStatus)
                    }
                    log.debug("[\(LogModule.core)] branch version info = \(appInfo)")
                }
            }
        }
    }
    
    private func requestBranchVersionInfo(configPath: String?, configContent: String?, patcherPath: String?) async -> LauncherAppBranchVersionInfo? {
        return await withCheckedContinuation { continuation in
            self.launcherCoreManager.requestBranchVersionInfo(true, configPath: configPath, configContent: configContent, patcherPath: patcherPath) { [weak self] info, error in
                if let tempInfo = info {
                    let result = self?.parseEnvironmentInfo(info: tempInfo)
                    continuation.resume(returning: result)
                } else {
                    continuation.resume(returning: nil)
                }
            }
        }
    }
    
    // 开始下载
    func doUpdate(showAlert: Bool = false) {
        // 先检查本地是否有足够存储空间
        checkBaseResourceWithDiskSpace {[weak self] status, needSpace, freeSpace, totalBytes in
            if status {
                if showAlert {
                    guard let weakSelf = self else {
                        return
                    }
                    DispatchQueue.main.async {
                        // 手动触发下载，给出占用磁盘大小提示
                        let totalsizeFormat = WMMacPatcher.byte(toCapacity: UInt64(totalBytes))
                        let diskSpaceUsedMessage = String(format: LauncherLocalization.localizedString(forKey: "download_disk_space_tips"), weakSelf.launcherName,weakSelf.extractShowBranchName(weakSelf.getSelectedAppBaseInfo(index: nil)?.displayAppName), totalsizeFormat)
                        let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "blank_tips"), message:diskSpaceUsedMessage)
                        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "confirm"), handler: {[weak self] action in
                            self?.startUpdate()
                        }))
                        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "cancel"), handler: { action in
                            self?.sendStatePublisherToSubject(.operationDone,nil,nil)
                        }))
                        alertView.show(inView: nil)
                    }
                } else {
                    // 非首次触发下载（继续下载、更新等),不需要提示，直接下载
                    self?.startUpdate()
                }
            } else {
                let needSpaceString = WMMacPatcher.byte(toCapacity: UInt64(needSpace))
                let freeSpaceString = WMMacPatcher.byte(toCapacity: UInt64(freeSpace))
                self?.showCommonAlertView(message: String(format: LauncherLocalization.localizedString(forKey: "disk_space_insufficient"), needSpaceString,freeSpaceString))
                self?.sendStatePublisherToSubject(.operationDone,nil,nil)
            }
        }
    }
    
    func startUpdateAfterChangeSetting(_ autoUpdate: Bool) {
        if !autoUpdate {
            return
        }
        
        if haveDownloadingTask() {
            return
        }
        
        let appVersionStatus = getSelectedAppBranchVersionStatus(index: nil)
        if appVersionStatus == .needUpdating {
            startUpdate()
        }
    }
    
    func startUpdate() {
        if currentInstallingAppIndex == -1 {
            currentInstallingAppIndex = currentSelectedAppIndex
        }
        setPatcherDownload(speed: true, cdn: true)
        launcherCoreManager.updateResource("", clearResource: false, downloadIndex: currentInstallingAppIndex)
        log.debug("[\(LogModule.core)] start update task(\(currentInstallingAppIndex)) ")
    }
    
    func setPatcherDownload(speed: Bool, cdn: Bool) {
        if !haveDownloadingTask() {
            //没有游戏下载时不设置
            return
        }
        
        let settingManager = LauncherSettingsManager.shared
        if speed {
            if let speedValue = settingManager.downloadSpeedLimit, let speedNumber = Int(speedValue) {
                launcherCoreManager.setMaxSpeed(Int64(speedNumber))
            } else {
                launcherCoreManager.setMaxSpeed(0)
            }
        }
        
        if cdn {
            if let routeValue = settingManager.downloadRoute, let routeIndex = Int(routeValue),
               let cdnList = getCurrentPatchrResUrls(), routeIndex - 1 < cdnList.count {
                let cdnUrl = cdnList[routeIndex - 1]
                launcherCoreManager.setCDN(cdnUrl)
            }
        }
    }
    
    // 暂停下载
    func pauseDownload() {
        LauncherEventManager.gameAssetDownloadPause()
        cancelPatcherUpdate(reason:.pause)
        log.debug("[\(LogModule.core)] pasue update task(\(currentInstallingAppIndex))")
    }
    
    // 继续下载
    func continueDownload() {
        LauncherEventManager.continuegameAssetDownloadContinue()
        doUpdate()
    }
    
    // 取消下载
    func cancelDownload() {
        cancelPatcherUpdate(reason: .cancel)
        log.debug("[\(LogModule.core)] cancel update task(\(currentInstallingAppIndex))")
    }
    
    func cancelPatcherUpdate(reason:WMStopDownloadType, handler: (() -> Void)? = nil) {
        patcherUpdateCancelledHandler = handler
        launcherCoreManager.stopUpdate(reason)
    }
    
    
    // 是否有正在下载的任务，无需关注下载中或暂停状态
    func haveDownloadingTask() -> Bool {
        if currentInstallingAppIndex >= 0 {
            return true
        } else {
            return false
        }
    }
    
    // 是否有正在下载的任务，并处于下载中
    func isDownloadingTask() -> Bool {
        if haveDownloadingTask() && updateStatus == .doing {
            return true
        } else {
            return false
        }
    }
    
    
    func continueDidCancelDownloadingTask(operateType: AppOperateType, handler: ((Bool) -> Void)?) {
        if currentInstallingAppIndex >= 0 {
            let branchInfo = getSelectedAppBranchVersionInfo(index: currentInstallingAppIndex)
            let isInstalled = branchInfo?.localVersion.isEmpty ?? true
            
            // 正在下载时，切换游戏提示
            var operateLocalizedKey = ""
            switch operateType {
            case .download:
                operateLocalizedKey = "download"
            case .update:
                operateLocalizedKey = "update"
            case .launcher:
                operateLocalizedKey = "launcher"
            case .resRepair:
                operateLocalizedKey = "repair"
            default:
                operateLocalizedKey = "switch"
            }
            let multiAppName = " 《\(launcherName!)》\(extractShowBranchName(branchInfo?.appVersionName))"
            
            let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "blank_tips"), message: String(format: LauncherLocalization.localizedString(forKey: "download_in_progress"), isInstalled ? LauncherLocalization.localizedString(forKey: "download") : LauncherLocalization.localizedString(forKey: "update"),  multiAppName, LauncherLocalization.localizedString(forKey: operateLocalizedKey)))
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "confirm"), handler: {[weak self] action in
                if let weakSelf = self {
                    log.debug("[\(LogModule.core)] cancel current task(\(weakSelf.currentInstallingAppIndex)) and start new(\(weakSelf.currentSelectedAppIndex))")
                    weakSelf.cancelPatcherUpdate(reason: .cancel) {
                        weakSelf.changePatcherTask(index: weakSelf.currentSelectedAppIndex)
                        handler?(true)
                    }
                }
            }))
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "cancel"), handler: { action in
                handler?(false)
            }))
            alertView.show(inView: nil)
        } else {
            self.changePatcherTask(index: currentSelectedAppIndex)
            log.debug("[\(LogModule.core)] switch to game(\(currentSelectedAppIndex)) ")
            handler?(true)
        }
    }
}

// MARK: - get/set
extension LauncherPatcherManager {
    // 根据UniqueId获取index
    private func getConfigIndex(for uniqueId: String?) -> Int {
        guard let appIdentify = uniqueId else {
            return -1
        }
        
        guard let launcherConfig = LaunchUIConstant.LauncherConfig else {
            return -1
        }
        
        guard let multiConfig = launcherConfig.config.multiConfig else {
            return -1
        }
        if let index = multiConfig.firstIndex(where: { $0.uniqueId == appIdentify }) {
            return index
        } else {
            return -1
        }
    }
    
    // 根据index获取UniqueId
    private func getSelectedAppUniqueId(index: Int?) -> String? {
        guard let launcherConfig = LaunchUIConstant.LauncherConfig else {
            return nil
        }
        
        var appIndex = currentSelectedAppIndex
        if let tempIndex = index {
            appIndex = tempIndex
        }
        guard appIndex < launcherConfig.config.multiConfig.count else {
            return nil
        }
        
        let appItemConfig = launcherConfig.config.multiConfig[appIndex]
        return appItemConfig.uniqueId
    }
    
    // 获取所选Branch版本的基础信息
    private func getSelectedAppBaseInfo(index: Int?) -> LauncherAppConfig? {
        guard let launcherConfig = LaunchUIConstant.LauncherConfig else {
            return nil
        }
        var appIndex = currentSelectedAppIndex
        if let tempIndex = index {
            appIndex = tempIndex
        }
        
        guard appIndex < launcherConfig.config.multiConfig.count else {
            return nil
        }
        
        let appItemConfig = launcherConfig.config.multiConfig[appIndex]
        var appPatchConfig = patcherDataManager.getBaseInfo(for: appItemConfig.uniqueId)
        
        let launcherClientEnv = LauncherSettingsManager.shared.targetClientEnv
        var clientExeName = appItemConfig.client
        var cmdLineArgs = appItemConfig.clientCmdLineArgs
        
        if launcherClientEnv == .dev {
            clientExeName = appItemConfig.clientDev ?? appItemConfig.client
            cmdLineArgs = appItemConfig.clientDevCmdLineArgs
        } else if launcherClientEnv == .test {
            clientExeName = appItemConfig.clientTest ?? appItemConfig.client
            cmdLineArgs = appItemConfig.clientTestCmdLineArgs
        }

        let targetAppFullPath = appPatchConfig!.resPath.appendingPathComponent(clientExeName)
        appPatchConfig!.clientPath = targetAppFullPath
        appPatchConfig!.cmdLineArgs = cmdLineArgs
        return appPatchConfig
    }
    
    // 获取所选Branch版本的检查状态
    private func getSelectedAppBranchVersionStatus(index: Int?) -> LauncherAppStatus {
        guard let uniqueId = getSelectedAppUniqueId(index: index) else {
            return .unknown
        }
        let checkStatus = patcherDataManager.getVersionCheckStatus(for: uniqueId)
        return checkStatus
    }
    
    // 获取所选Branch版本的版本信息
    private func getSelectedAppBranchVersionInfo(index: Int?) -> LauncherAppBranchVersionInfo? {
        guard let uniqueId = getSelectedAppUniqueId(index: index) else {
            return nil
        }
        let branchVersionInfo = patcherDataManager.getVersionInfo(for: uniqueId)
        return branchVersionInfo
    }
    
    // 获取所选Branch版本的下载信息
    private func getSelectedAppDownloadInfo(index: Int?) -> LauncherAppDownloadInfo? {
        guard let uniqueId = getSelectedAppUniqueId(index: index) else {
            return nil
        }
        let downloadInfo = patcherDataManager.getDownloadInfo(for: uniqueId)
        return downloadInfo
    }
    
    private func updateAppVersionCheckStatus(index: Int?,  status: LauncherAppStatus)  {
        guard let uniqueId = getSelectedAppUniqueId(index: index) else {
            return
        }
        patcherDataManager.updateVersionCheckStatus(for: uniqueId, status: status)
    }
    
    private func updateAppBranchVersionInfo(index: Int?, versionInfo: LauncherAppBranchVersionInfo?)  {
        guard let uniqueId = getSelectedAppUniqueId(index: index) else {
            return
        }
        patcherDataManager.updateVersionInfo(for: uniqueId, versionInfo: versionInfo)
    }
    
    private func updateAppDownloadInfo(index: Int?, downloadInfo: LauncherAppDownloadInfo?)  {
        guard let uniqueId = getSelectedAppUniqueId(index: index) else {
            return
        }
        patcherDataManager.updateDownloadInfo(for: uniqueId, downloadInfo: downloadInfo)
    }
    
    // 获取当前patcher对应配置文件中的gameResUrl
    public func getCurrentPatchrResUrls() -> [String]? {
        return launcherCoreManager.getCDNList()
    }
}

// MARK: - Patcher版本检查、下载回调
extension LauncherPatcherManager {
    // 下载安装时，判断本地磁盘空间是否够用
    func checkBaseResourceWithDiskSpace(handler: ((Bool, Int64, Int64, Int64) -> Void)? = nil) {
        launcherCoreManager.checkResource(nil) { totalFileCount, checkedFileCount, totalBytes, checkedBytes, currentResName, currentFileSize, currentCheckedSize in
        } resultCallback: {   code, message, resStatus, totalBytes, needDownloadBytes, needSpace in
            let freeSpace = WMMacPatcher.getDeviceFreespace()
            if needSpace >= freeSpace {
                log.debug("[\(LogModule.core)] insufficient disk space, \(needSpace) >= \(freeSpace)")
                handler?(false,needSpace,freeSpace,totalBytes)
            } else {
               handler?(true,needSpace,freeSpace,totalBytes)
            }
        }
    }
    
    // 检查本地是否已安装、已安装版本号检查
    func beginCheckLocalVersion(autoDownloadType: LauncherGameAutoDownloadType = .none) {
        // 获取本地安装或即将安装的版本信息
        launcherCoreManager.requestEnvironmentInfo {[weak self] info, error in
            guard let mySelf = self else {
                return
            }
            if error != nil {
                //获取失败未知
                let unknown = LauncherLocalization.localizedString(forKey: "unknown")
                var appVersionInfo = LauncherAppBranchVersionInfo(environment: .unknown, localVersion: unknown, remoteVersion: unknown, preRelease: "")
                if let appName = mySelf.getSelectedAppBaseInfo(index: nil)?.displayAppName {
                    appVersionInfo.appVersionName = appName
                }
                
                mySelf.updateAppBranchVersionInfo(index: nil, versionInfo: appVersionInfo)
                mySelf.availableVersionCheck(completion: { [weak mySelf] in
                    let branchInfo = mySelf?.getSelectedAppBranchVersionInfo(index: nil)
                    var errorCode = LauncherErrorCode.unknown.rawValue
                    if let nsError = error as NSError?  {
                        errorCode = nsError.code
                    }
                    mySelf?.sendStatePublisherToSubject(.checkFail,branchInfo, nil, errorCode)
                    mySelf?.updateAppVersionCheckStatus(index: nil, status: .unknown)
                })
                log.debug("[\(LogModule.core)] request environment info error: \(error!)")
            } else {
                // 本地已有版本，检查当前版本是否要更新
                let appInstallInfo = mySelf.parseEnvironmentInfo(info: info!)
                mySelf.updateAppBranchVersionInfo(index: nil, versionInfo: appInstallInfo)
                // 获取App可用的所有版本号信息
                mySelf.availableVersionCheck(completion: {[weak mySelf] in
                    let branchInfo = mySelf?.getSelectedAppBranchVersionInfo(index: nil)
                    mySelf?.checkBaseResource(info: branchInfo!, autoDownloadType: autoDownloadType)
                })
            }
        }
    }
    
    // 选择或切换App版本后，检查资源是否完整，是否需要更新
    func checkBaseResource(info : LauncherAppBranchVersionInfo, autoDownloadType: LauncherGameAutoDownloadType = .none) {
        launcherCoreManager.checkResource(nil) { totalFileCount, checkedFileCount, totalBytes, checkedBytes, currentResName, currentFileSize, currentCheckedSize in
            
        } resultCallback: {  [weak self]code, message, resStatus, totalBytes, needDownloadBytes, needSpace in
            guard let mySelf = self else {
                return
            }
            var branchInfo = mySelf.getSelectedAppBranchVersionInfo(index: nil)
            if resStatus == .needUpdate {
                if info.localVersion.isEmpty {
                    // 本地还没版本，返回开始安装
                    mySelf.updateAppVersionCheckStatus(index: nil, status: .startDownloading)
                    mySelf.sendStatePublisherToSubject(.startDownloading, branchInfo, nil)
                    if autoDownloadType == .install {
                        mySelf.statusPanelOperateAction(status: .startDownloading)
                    }
                } else {
                    if info.localVersion == info.remoteVersion {
                        // 本地和远端版本一致，但资源不一样，需要更新，手动添加.0和.1后缀
                        branchInfo?.localVersion = "\( info.localVersion).0"
                        branchInfo?.remoteVersion = "\( info.localVersion).1"
                        mySelf.updateAppVersionCheckStatus(index: nil, status: .needUpdating)
                        mySelf.updateAppBranchVersionInfo(index: nil, versionInfo: branchInfo)
                        mySelf.sendStatePublisherToSubject(.needUpdating, branchInfo, nil)
                        
                        if autoDownloadType == .update {
                            mySelf.statusPanelOperateAction(status: .needUpdating)
                        }
                    } else {
                        // 本地和远端版本不一致
                        mySelf.updateAppVersionCheckStatus(index: nil, status: .needUpdating)
                        mySelf.sendStatePublisherToSubject(.needUpdating, branchInfo, nil)
                        if autoDownloadType == .update {
                            mySelf.statusPanelOperateAction(status: .needUpdating)
                        }
                    }
                }
                
            } else if resStatus == .completed {
                mySelf.updateAppVersionCheckStatus(index: nil, status: .startGame)
                mySelf.sendStatePublisherToSubject(.startGame, branchInfo, nil)
            } else {
                mySelf.sendStatePublisherToSubject(.checkFail, branchInfo, nil, LauncherErrorCode.unknown.rawValue)
            }
            log.debug("[\(LogModule.core)] request environment info, localversion: \(info.localVersion) remoteversion: \(info.remoteVersion), resStatus:\(resStatus)")
        }
    }
    
    // 解析当前版本信息
    func parseEnvironmentInfo(info: WMPatcherEnvironmentInfo) -> LauncherAppBranchVersionInfo {
        let environment = info.environment
        let localVer = info.local
        let remote = info.remote
        let preRelease = info.preRelease
        
        // 环境判断
        var envCode: LauncherAppEnv = .unknown
        if environment == "beta" {
            envCode = .beta
        } else if environment == "beta test" {
            envCode = .betaTest
        } else if environment == "release" {
            envCode = .release
        } else if environment == "release test" {
            envCode = .releaseTest
        }
        
        var appInstallInfo = LauncherAppBranchVersionInfo(environment: envCode, localVersion: localVer, remoteVersion: remote, preRelease: preRelease)
        if let appName = getSelectedAppBaseInfo(index: nil)?.displayAppName {
            appInstallInfo.appVersionName = appName
        }
        return appInstallInfo
    }
    
    // 打开游戏前，再次检查资源是否正确
    func checkBaseResourceWithLauncherApp(handler: ((Bool) -> Void)? = nil) {
#if LAUNCHER_ENV_DEVELOP || LAUNCHER_ENV_TEST//只开发和测试环境包含这个功能
        if let seletedAppVersion = LauncherSettingsManager.shared.selectedAppVersion, LauncherSettingsManager.shared.autoUpdateClient == false {
            self.launcherCoreManager.changeSelectedVersion(seletedAppVersion)
        } else {
            self.launcherCoreManager.changeSelectedVersion("")
        }
#endif
        launcherCoreManager.requestEnvironmentInfo {[weak self] info, error in
            guard let mySelf = self else {
                DispatchQueue.main.async {
                    handler?(false)
                }
                return
            }
            let currentVersionInfo = mySelf.getSelectedAppBranchVersionInfo(index: nil)
            var appInstallInfo = currentVersionInfo
            if let tempInfo = info {
                appInstallInfo = mySelf.parseEnvironmentInfo(info: tempInfo)
                appInstallInfo?.avaliableVersions = currentVersionInfo?.avaliableVersions //保留可用版本
                mySelf.updateAppBranchVersionInfo(index: nil, versionInfo: appInstallInfo)
            }
            
            mySelf.launcherCoreManager.checkResource(nil) { totalFileCount, checkedFileCount, totalBytes, checkedBytes, currentResName, currentFileSize, currentCheckedSize in
            } resultCallback: {  [weak self]code, message, resStatus, totalBytes, needDownloadBytes, needSpace in
                if resStatus == .needUpdate {
                    if let appInfo = appInstallInfo,appInfo.localVersion.isEmpty {
                        // 本地还没版本，返回开始安装
                        mySelf.updateAppBranchVersionInfo(index: nil, versionInfo: appInfo)
                        mySelf.updateAppVersionCheckStatus(index: nil, status: .startDownloading)
                        self?.sendStatePublisherToSubject(.startDownloading,appInfo,nil)
                    } else {
                        if  var appInfo = appInstallInfo, appInfo.localVersion == appInfo.remoteVersion {
                            // 本地和远端版本一致，但资源不一样，需要更新，手动添加.0和.1后缀
                            appInfo.localVersion = "\( appInfo.localVersion).0"
                            appInfo.remoteVersion = "\( appInfo.localVersion).1"
                            mySelf.updateAppVersionCheckStatus(index: nil, status: .needUpdating)
                            mySelf.updateAppBranchVersionInfo(index: nil, versionInfo: appInfo)
                            self?.sendStatePublisherToSubject(.needUpdating, appInfo, nil)
                        } else {
                            // 本地和远端版本不一致
                            mySelf.updateAppBranchVersionInfo(index: nil, versionInfo: appInstallInfo)
                            mySelf.updateAppVersionCheckStatus(index: nil, status: .needUpdating)
                            self?.sendStatePublisherToSubject(.needUpdating, appInstallInfo, nil)
                        }
                    }
                    DispatchQueue.main.async {
                        handler?(false)
                    }
                } else if resStatus == .completed {
                    DispatchQueue.main.async {
                        handler?(true)
                    }
                } else {
                    self?.sendStatePublisherToSubject(.checkFail, appInstallInfo, nil, LauncherErrorCode.unknown.rawValue)
                    DispatchQueue.main.async {
                        handler?(false)
                    }
                }
                log.debug("[\(LogModule.core)] request environment info resStatus:\(resStatus) before launcher game")
                
            }
        }
    }
    
    // 获取可用版本信息
    func availableVersionCheck(completion: (() -> Void)? = nil) {
#if LAUNCHER_ENV_DEVELOP || LAUNCHER_ENV_TEST//只开发和测试环境包含这个功能
        guard patcherInitState else { return }
        // 检查可用版本
        launcherCoreManager.availableVersionCheck { [weak self] versionInfos, error in
            if let infos = versionInfos {
                var newVersions: [LauncherAppAavaliableVersion] = []
                for versionInfo in infos {
                    let branchInfo = LauncherAppAavaliableVersion(branchName: versionInfo.branchName, version: versionInfo.version)
                    newVersions.append(branchInfo)
                }
                guard let mySelf = self else {
                    return
                }
                
                var branchInfo = mySelf.getSelectedAppBranchVersionInfo(index: nil)
                branchInfo?.avaliableVersions = newVersions
                mySelf.updateAppBranchVersionInfo(index: nil, versionInfo: branchInfo)
            }
            completion?()
        }
#else
        completion?()
#endif
    }
    
    // 添加patcher资源检测、下载回调
    func regiserPatcherCallback() {
        // 状态改变回调，下载中触发
        launcherCoreManager.patcherStateCallback = { [weak self] state, isStart, downloadIndex in
            guard let mySelf = self else {
                return
            }
            
            if state == .resFix && isStart && downloadIndex >= 0 {
                self?.updateAppVersionCheckStatus(index: downloadIndex, status: .resProcessing)
                if (downloadIndex == mySelf.currentSelectedAppIndex) {
                    let downloadInfo = self?.getSelectedAppDownloadInfo(index: downloadIndex)
                    self?.sendStatePublisherToSubject(.resProcessing, nil, downloadInfo)
                }
            } else if state == .resDownload && !isStart && downloadIndex >= 0 {
                // 资源下载完成，打点gameAssetDownloadSuccesss
                LauncherEventManager.gameAssetDownloadSuccesss()
            }
            
            log.debug("[\(LogModule.core)] patcher state callback: \(state) isStart: \(isStart)")
        }
        
        launcherCoreManager.patcherFixResourceFileStartCallback = { [weak self] totalResouceByteSize, totalFixedResourceSize, CurrentResourceTotalSize, file_oper, current_file, uniqueIndex in
            guard let mySelf = self else {
                return
            }
            
            var downloadInfo = self?.getSelectedAppDownloadInfo(index: uniqueIndex)
            downloadInfo?.fixResTotalSize = totalResouceByteSize
            downloadInfo?.fixResFixedSize = totalFixedResourceSize
            if !current_file.isEmpty {
                let fileName = URL(fileURLWithPath: current_file).lastPathComponent
                downloadInfo?.fixResFile = fileName
            }
            mySelf.updateAppDownloadInfo(index: uniqueIndex, downloadInfo: downloadInfo)
            mySelf.updateAppVersionCheckStatus(index: uniqueIndex, status: .resProcessing)
            
            if (uniqueIndex == mySelf.currentSelectedAppIndex) {
                mySelf.sendStatePublisherToSubject(.resProcessing, nil, downloadInfo)
            }
        }
        
        // 版本检查回调，下载前触发
        launcherCoreManager.patcherVersionCallback = { [weak self] result, versionInfo in
            if result == .resUpdate {
                // 更新正要下载的版本信息
                guard let mySelf = self else {
                    return
                }
                var branchInfo = mySelf.getSelectedAppBranchVersionInfo(index: nil)
                branchInfo?.beingInstallVersion = versionInfo.newestResVersion
                mySelf.updateAppBranchVersionInfo(index: nil, versionInfo: branchInfo)
            }
        }
        
        // 下载进度回调
        launcherCoreManager.patcherDownloadCallback = {[weak self] totalsize, progress, speed, index in
            guard let weakSelf = self else {
                return
            }
            
            DispatchQueue.main.async {
                weakSelf.currentInstallingAppIndex = index
                weakSelf.updateStatus = .doing
                let downloadSize = Double(totalsize) * progress //已下载的总大小
                let downloadSizeFormat = WMMacPatcher.byte(toCapacity: UInt64(downloadSize)) //格式化后的已下载文件大小
                
                let totalsizeFormat = WMMacPatcher.byte(toCapacity: totalsize) //格式化后的文件总大小
                let progressFormat = WMMacPatcher.progress(toString: progress)//下载进度
                
                var timeFormat = "--:--:--"
                var speedFormat = "--"
                if speed > 0 {
                    let remainTime = max(0, Double(totalsize) * (1.0 - progress) / Double(speed))
                    
                    let hours = Int(remainTime / 3600)
                    let minutes = Int((remainTime.truncatingRemainder(dividingBy: 3600)) / 60)
                    let seconds = Int(round(remainTime.truncatingRemainder(dividingBy: 60)))
                    
                    timeFormat = String(format: "%02d:%02d:%02d", hours, minutes, seconds)
                    speedFormat = WMMacPatcher.speed(toString: speed)
                }
                
                let branchInfo = self?.getSelectedAppBranchVersionInfo(index: index)
                var multiAppNameFormat = ""
                if let launcherAppName = self?.launcherName, let appName = self?.extractShowBranchName(self?.getSelectedAppBaseInfo(index: index)?.displayAppName)    {
                    multiAppNameFormat = "《\(launcherAppName)》" + appName
                }
                
                let client = LauncherLocalization.localizedString(forKey: "client_app")
                let versionName = multiAppNameFormat + client + "  \(branchInfo?.beingInstallVersion ?? "")"
                
                let downloadInfo = LauncherAppDownloadInfo(speed: speedFormat, downloadedSize: downloadSizeFormat, totalSize: totalsizeFormat, progress: progressFormat, remainingTime:timeFormat, appVersionName: versionName, localVersion: branchInfo?.localVersion)
                
                self?.updateAppDownloadInfo(index: index, downloadInfo: downloadInfo)
                self?.updateAppVersionCheckStatus(index: index, status: .downloading)
                
                if weakSelf.currentInstallingAppIndex != weakSelf.currentSelectedAppIndex {
                    // 正在下载的和所选的App不是一个
                    return
                }
                self?.sendStatePublisherToSubject(.downloading, nil, downloadInfo)
            }
        }
        
        // 所有流程完成回调，无论成功还是失败都会触发
        launcherCoreManager.patcherFinishCallback = { [weak self] result,index, reason in
            guard let weakSelf = self else {
                return
            }
            DispatchQueue.main.async {
                weakSelf.currentInstallingAppIndex = index
                log.debug("[\(LogModule.core)] patcher callback(\(index)) status:\(result)")
                
                let isDownloadingSelectedApp =  (index == weakSelf.currentSelectedAppIndex)
                var branchInfo = self?.getSelectedAppBranchVersionInfo(index: index)
                var installingStatus = LauncherAppStatus.startDownloading
                if let appInfo = branchInfo, !appInfo.localVersion.isEmpty {
                    installingStatus = .needUpdating
                }
                if result == .ok {
                    if let _ = branchInfo,
                       let localResVersion = self?.launcherCoreManager.getLocalResVersion() {
                        branchInfo?.localVersion = localResVersion
                    }
                    
                    self?.updateAppVersionCheckStatus(index: index, status: .startGame)
                    self?.updateAppBranchVersionInfo(index: index, versionInfo: branchInfo)
                    self?.resetPatcherDownloadStatus()
                    self?.selectedAppStatusDidChage?(nil) //刷新sidebar app安装状态
                    if isDownloadingSelectedApp {
                        self?.sendStatePublisherToSubject(.startGame, branchInfo, nil)
                    }
                } else if result == .cancel {
                    if reason == .pause {
                        self?.updateStatus = .pause
                        self?.updateAppVersionCheckStatus(index: index, status: .pauseDownloading)
                        if isDownloadingSelectedApp {
                            let downloadInfo = self?.getSelectedAppDownloadInfo(index: index)
                            self?.sendStatePublisherToSubject(.pauseDownloading, nil, downloadInfo)
                        } else {
                            self?.resetPatcherDownloadStatus()
                        }
                        self?.patcherUpdateCancelledHandler?()
                        self?.patcherUpdateCancelledHandler = nil
                    } else {
                        self?.updateStatus = .idle
                        self?.updateAppVersionCheckStatus(index: index, status: installingStatus)
                        self?.resetPatcherDownloadStatus()
                        self?.patcherUpdateCancelledHandler?()
                        self?.patcherUpdateCancelledHandler = nil
                        if isDownloadingSelectedApp {
                            self?.sendStatePublisherToSubject(.cancelDownloading, branchInfo, nil)
                        }
                    }
                } else if result == .insufficientSpace ||  result == .diskFull {
                    self?.showCommonAlertView(message: LauncherLocalization.localizedString(forKey: "download_error_disk_space_insufficient"))
                    self?.updateAppVersionCheckStatus(index: index, status: installingStatus)
                    self?.resetPatcherDownloadStatus()
                    if isDownloadingSelectedApp {
                        self?.sendStatePublisherToSubject(installingStatus, branchInfo, nil)
                    }
                } else {
                    self?.updateAppVersionCheckStatus(index: index, status: .downloadingFail)
                    self?.resetPatcherDownloadStatus()
                    if isDownloadingSelectedApp {
                        self?.sendStatePublisherToSubject(.downloadingFail, branchInfo, nil, result.rawValue)
                    } else {
                        let message = String(format: LauncherLocalization.localizedString(forKey: "download_error_tips"), self?.launcherName ?? "", self?.extractShowBranchName(branchInfo?.appVersionName) ?? "", result.rawValue)
                        self?.showCommonAlertView(message:message)
                    }
                }
            }
        }
    }
    
    func changeSelectedVersion(newVersion : String) {
#if LAUNCHER_ENV_DEVELOP || LAUNCHER_ENV_TEST//只开发和测试环境包含这个功能
        beginPatcherDidSeletedApp(index: currentSelectedAppIndex, forced: true)
#endif
    }
    
    func showCommonAlertView(message : String!) {
        DispatchQueue.main.async {
            let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "blank_tips"), message: message)
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "confirm"), handler: { action in
            }))
            alertView.show(inView: nil)
        }
    }
    
    func resetPatcherDownloadStatus() {
        currentInstallingAppIndex = -1
    }
    
    func extractShowBranchName(_ appVersionName:String?) -> String {
        if let tempName = appVersionName {
            // 防止配置文件中LauncherName 和 versionName有重叠，例如LauncherName=诛仙世界，versionName=诛仙世界标准版
            let branchFlag = tempName.replacingOccurrences(of: launcherName, with: "")
            return branchFlag
        }
        return ""
    }
}

// MARK: - 游戏启动、终止接口及回调
extension LauncherPatcherManager {
    func isInstalledApplication(index : Int) -> Bool {
        if let launcherAppConfig = getSelectedAppBaseInfo(index: index) {
            return FileManager.default.fileExists(atPath: launcherAppConfig.clientPath)
        }
        return false
    }
    
    func haveRunningApplication() -> Bool {
        return runningAppUniqueId != nil
    }
    
    func isApplicationRunning(uniqueId: String) -> Bool {
        if let runningUniqueId = runningAppUniqueId {
            return runningUniqueId == uniqueId
        }
        return false
    }
    
    func launcherSubApplication() {
        LauncherEventManager.launcherStartGame()
        checkBaseResourceWithLauncherApp { [weak self] canOpen  in
            guard let mySelf = self else {
                return
            }
            if canOpen {
                if let appConfig = mySelf.getSelectedAppBaseInfo(index: nil) {
                    // 先终止先前启动的游戏
                    mySelf.launcherCoreManager.removeAllTerminationHandlers()
                    mySelf.launcherCoreManager.terminateRunningApplication()
                    let appUrls = mySelf.launcherCoreManager.searchApplication(withBundleIdentifier: appConfig.bundleId, searchPath: appConfig.clientPath)
                    if appUrls.count > 0 {
                        self?.runningAppUniqueId = appConfig.uniqueId
                        self?.sendStatePublisherToSubject(.gameInProgress, nil, nil)
                        self?.updateAppVersionCheckStatus(index: nil, status: .gameInProgress)
                        mySelf.launcherCoreManager.launchApplication(with: appUrls.first!, bundleIdentifier: appConfig.bundleId, arguments: appConfig.cmdLineArgs) {[weak self] application, error in
                            if let error = error as NSError?  {
                                self?.runningAppUniqueId = nil
                                self?.sendStatePublisherToSubject(.startGame, nil, nil)
                                self?.updateAppVersionCheckStatus(index: nil, status: .startGame)
                                let launcherErrorMsg = String(format: LauncherLocalization.localizedString(forKey: "launcher_game_error"), appConfig.displayAppName ?? "", String(format: "(%@-%d)", error.localizedDescription, error.code))
                                self?.showCommonAlertView(message: launcherErrorMsg)
                                
                                log.notice("[\(LogModule.core)] launcher game error: \(error)")
                            } else {
                                if let window = NSApplication.shared.windows.first {
                                    window.performMiniaturize(nil)
                                }
                                log.debug("[\(LogModule.core)] launcher game: \(appConfig.uniqueId)")
                            }
                        } terminationHandler: {[weak self] application in
                            // 应用被终止
                            self?.appliecationDidTermination(uniqueId: appConfig.uniqueId)
                        }
                    } else {
                        self?.sendStatePublisherToSubject(.operationDone, nil, nil)
                        let launcherErrorMsg = String(format: LauncherLocalization.localizedString(forKey: "launcher_game_error"), appConfig.displayAppName ?? "","(bid error)")
                        self?.showCommonAlertView(message: launcherErrorMsg)
                        log.notice("[\(LogModule.core)] launcher game bid(\(appConfig.bundleId)) error")
                    }
                }
            } else {
                // 不需要处理，checkBaseResourceWithLauncherApp方法里会有对应回调
            }
        }
    }
    
    // 启动时，主动检查游戏是否在运行，正在运行时，添加游戏终止事件监听
    func addSubApplicationRunningObserve(_ bundleId: String, uniqueId: String) {
        runningAppUniqueId = uniqueId
        launcherCoreManager.addRunningApplication(bundleId) {[weak self] application in
            // 应用被终止,结束后重新触发资源检查
            guard let weakSelf = self else {
                return
            }
            weakSelf.appliecationDidTermination(uniqueId: uniqueId)
        }
    }
    
    func appliecationDidTermination(uniqueId: String) {
        // 应用被终止
        runningAppUniqueId = nil
        let applicationIndex = getConfigIndex(for: uniqueId)
        updateAppVersionCheckStatus(index: applicationIndex, status: .startGame)
        
        if applicationIndex == currentSelectedAppIndex {
            let branchInfo = getSelectedAppBranchVersionInfo(index: nil)
            sendStatePublisherToSubject(.startGame, branchInfo, nil)
            // 触发资源修复检查，检查是否有异常文件上报
            beginCheckExceptionFilesAlertView()
        } else {
            let versionStatus = getSelectedAppBranchVersionStatus(index: nil)
            if versionStatus == .startGame {
                // 当前选中的游戏，如何是开始游戏状态，需要置为可点
                let branchInfo = getSelectedAppBranchVersionInfo(index: nil)
                sendStatePublisherToSubject(.startGame, branchInfo, nil)
            }
        }
        
        log.debug("[\(LogModule.core)] game termination: \(uniqueId)")
    }
    
    // 主界面右下角面板中各操作事件响应
    func statusPanelOperateAction(status: LauncherAppStatus) {
        switch status {
        case .unknown,.checkFail:
            beginPatcherDidSeletedApp(index: currentSelectedAppIndex, forced: true)
        case .startGame:
            LauncherInterceptManager.shared.canStartGame(handler: { [weak self] enable in
                if enable {
                    self?.continueDidCancelDownloadingTask(operateType: .launcher) { [weak self] bContinue in
                        if bContinue {
                            self?.launcherSubApplication()
                        } else {
                            self?.sendStatePublisherToSubject(.operationDone,nil,nil)
                        }
                    }
                } else {
                    self?.sendStatePublisherToSubject(.operationDone,nil,nil)
                }
            })
            
            
        default:
            LauncherInterceptManager.shared.canStartInstall { [weak self] enable in
                if enable {
                    let branchInfo = self?.getSelectedAppBranchVersionInfo(index: self?.currentSelectedAppIndex)
                    let isInstalled = branchInfo?.localVersion.isEmpty ?? true
                    self?.continueDidCancelDownloadingTask(operateType: isInstalled ? .download : .update) { [weak self] bContinue in
                        if bContinue {
                            self?.doUpdate(showAlert: status == .startDownloading)
                        } else {
                            self?.sendStatePublisherToSubject(.operationDone,nil,nil)
                        }
                    }
                } else {
                    self?.sendStatePublisherToSubject(.operationDone,nil,nil)
                }
            }
        }
    }
    
    // 右侧侧边栏中点击事件响应
    func siderBarItemAppOperateAction(type: AppOperateType, index: Int)  {
        let appIndex = index
        switch type {
        case .openAppDir:
            if let launcherAppConfig = getSelectedAppBaseInfo(index: appIndex) {
                let fileManager = FileManager.default
                if fileManager.fileExists(atPath: launcherAppConfig.cacheBasePath) {
                    let url = URL(fileURLWithPath: launcherAppConfig.cacheBasePath)
                    NSWorkspace.shared.open(url)
                }
            }
        case .removeApp:
            if let selectedAppConfig = getSelectedAppBaseInfo(index: appIndex) {
                if isApplicationRunning(uniqueId: selectedAppConfig.uniqueId) {
                    // app正在运行
                    let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "blank_tips"), message: LauncherLocalization.localizedString(forKey: "game_running"))
                    alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "confirm"), handler: { action in
                    }))
                    alertView.show(inView: nil)
                    return
                }
                let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "blank_tips"), message: LauncherLocalization.localizedString(forKey: "delete_game_confirm"))
                alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "confirm"), handler: {[weak self] action in
                    WMXToast.showLoading(withTitle: nil, message: LauncherLocalization.localizedString(forKey: "deleting"))
                    self?.deleteItem(at: selectedAppConfig.cacheBasePath) {[weak self] result, error in
                        WMXToast.hideLoading()
                        self?.selectedAppStatusDidChage?(nil) //刷新sidebar app安装状态
                        if self?.getSelectedAppBaseInfo(index: nil)?.uniqueId == selectedAppConfig.uniqueId {
                            // 删除的是正在选择的游戏,重新检查游戏版本
                            self?.updateAppVersionCheckStatus(index: appIndex, status: .startDownloading)
                            self?.beginPatcherDidSeletedApp(index: appIndex, forced: false)
                        } else {
                            self?.updateAppVersionCheckStatus(index: appIndex, status: .startDownloading)
                        }
                    }
                }))
                alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "cancel"), handler: { action in
                }))
                alertView.show(inView: nil)
                
            }
        case .switchApp:
            self.switchBranchApp(index: appIndex)
        case .resRepair:
            let canRepair = canBeginRepair(index: appIndex)
            
            if !canRepair {
                showCommonAlertView(message: LauncherLocalization.localizedString(forKey: "unsupport_repair"))
                return
            }
            
            continueDidCancelDownloadingTask(operateType: .resRepair) { [weak self] bContinue in
                if bContinue {
                    self?.showResRepairAlertView(index: appIndex)
                }
            }
            break
        case .cleanCache:
            break
        case .changeAppVersion:
            let versionSelectorVC = LauncherVersionSelectorViewController()
            versionSelectorVC.onVersionSelected = { [weak self] selectedVersion in
                self?.changeSelectedVersion(newVersion: selectedVersion)
            }
            
            versionSelectorVC.showViewController()
            
        default:break
        }
    }
    
    func switchBranchApp(index: Int) {
        beginPatcherDidSeletedApp(index: index, forced: false)
        // 切换打点事件中ostype
        let appConfig = multiAppConfig(index: index)
        if  let ostype = appConfig?.ostype {
            LauncherEventManager.shared.changeEvent(ostype: ostype)
        } else {
            LauncherEventManager.shared.changeEvent(ostype: nil)
        }
        //多版本切换打点
        LauncherEventManager.clickChooseGameEditionButton(abbr: appConfig?.versionName)
    }
    
    func deleteItem(at path: String, completion: ((Bool, Error?) -> Void)? = nil) {
        DispatchQueue.global(qos: .background).async {
            let fileManager = FileManager.default
            let fileURL = URL(fileURLWithPath: path)
            
            do {
                if fileManager.fileExists(atPath: path) {
                    try fileManager.removeItem(at: fileURL)
                    DispatchQueue.main.async {
                        completion?(true, nil)
                    }
                } else {
                    DispatchQueue.main.async {
                        completion?(false, NSError(domain: "FileError", code: 404, userInfo: [NSLocalizedDescriptionKey: "File or directory does not exist at \(path)"]))
                    }
                }
            } catch let error {
                DispatchQueue.main.async {
                    log.notice("[\(LogModule.core)] delete item error: \(error)")
                    completion?(false, error)
                }
            }
        }
    }
}

// MARK: - Patcher资源修复
extension LauncherPatcherManager {
    func reinstallSelectedApp() {
        // 先保留当前已安装的版本
        let localResVersion = self.launcherCoreManager.getLocalResVersion()
        let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "reinstall"), message: LauncherLocalization.localizedString(forKey: "need_delete_files"))
        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "cancel"), handler: { action in
            
        }))
        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "continue"), handler: {[weak self] action in
            if let cacheBasePath = self?.getSelectedAppBaseInfo(index: nil)?.cacheBasePath {
                WMXToast.showLoading(withTitle: nil, message: LauncherLocalization.localizedString(forKey: "deleting"))
                self?.deleteItem(at: cacheBasePath) {[weak self] result, error in
                    WMXToast.hideLoading()
                    // 触发重新安装检测
                    if let mySelf = self {
                        mySelf.selectedAppStatusDidChage?(nil)
                        // 设置重新安装的版本号(和卸载前保持一致)
                        mySelf.launcherCoreManager.changeSelectedVersion(localResVersion)
                        // 开启下载
                        mySelf.sendStatePublisherToSubject(.startDownloading, nil, nil) //先重置按钮状态
                        mySelf.updateAppVersionCheckStatus(index: nil, status: .startDownloading)
                        mySelf.doUpdate()
                    }
                }
            }
        }))
        alertView.show(inView: nil)
    }
    
    // 检查扫描需要修复的资源
    func checkRepairAppRes(handler: ((LauncherRepairCheckInfo) -> Void)? = nil) {
        // 获取最新下载速度
        downloadSpeedTest(handler: nil)
        getDiskWriteReadSpeed {[weak self]  diskReadSpeed in
            self?.launcherCoreManager.checkResourceHash("") { [weak self] totalFileCount, checkedFileCount, totalBytes, checkedBytes, currentResName, currentFileSize, currentCheckedSize in
                guard let mySelf = self else {
                    return
                }
                
                // 计算预估时间
                let remainSize = totalBytes - checkedBytes
                let remainSizeMB = mySelf.bytesToMB(bytes: Double(remainSize))
                let remainTimeRounding = ceil(remainSizeMB / Double(diskReadSpeed))
                let remainTime = max(remainTimeRounding,1)
                let remainTimeFormat = String.formatTime(seconds: Int(remainTime))
                
                var checkInfo = LauncherRepairCheckInfo()
                checkInfo.status = .doing
                checkInfo.totalFileCount = String(totalFileCount)
                checkInfo.checkedFileCount = String(checkedFileCount)
                checkInfo.estimatedTime = remainTimeFormat
                let progress = Double(checkedBytes)/Double(totalBytes)
                let progressFormat = WMMacPatcher.progress(toString: progress)
                checkInfo.progressPercent = progressFormat
                checkInfo.progress = progress
                DispatchQueue.main.async {
                    handler?(checkInfo)
                }
            } resultCallback: { [weak self] code, message, resStatus, totalBytes, needDownloadBytes, needSpace, totalcout, needupdatecount in
                guard let mySelf = self else {
                    return
                }
                var checkInfo = LauncherRepairCheckInfo()
                if code == 0 {
                    if resStatus == .needUpdate {
                        // 计算预估时间
                        var needDownloadTimeFormat = "估算中..."
                        if let downloadSpeed = mySelf.latestDownloadSpeed, downloadSpeed > 0 {
                            let needDownloadTime = needDownloadBytes/downloadSpeed
                            needDownloadTimeFormat = String.formatTime(seconds: Int(needDownloadTime))
                        }
                        checkInfo.status = .needUpdate
                        checkInfo.message = message
                        checkInfo.needDownloadBytes = WMMacPatcher.byte(toCapacity: UInt64(needDownloadBytes))
                        checkInfo.needUpdateCount = String(needupdatecount)
                        checkInfo.estimatedTime = needDownloadTimeFormat
                    } else if resStatus == .completed {
                        checkInfo.status = .completed
                    } else {
                        var checkInfo = LauncherRepairCheckInfo()
                        checkInfo.status = .fail
                    }
                } else {
                    var checkInfo = LauncherRepairCheckInfo()
                    checkInfo.status = .fail
                }
                DispatchQueue.main.async {
                    handler?(checkInfo)
                }
            }
        }
    }
    
    // 取消扫描需要修复的资源
    func cancelCheckRepairAppRes() {
        launcherCoreManager.cancelCheckResourceHash()
    }
    
    // 开始修复资源
    func beginRepairAppRes() {
        launcherCoreManager.setExceptionResolve(.delete)
        sendStatePublisherToSubject(.needUpdating, nil, nil) //先重置按钮状态
        updateAppVersionCheckStatus(index: nil, status: .needUpdating)
        doUpdate()
    }
    
    // 获取游戏上报过的异常文件
    func getExceptionResFiles() -> [String]?{
        return launcherCoreManager.getExceptionFiles()
    }
    
    func getDiskWriteReadSpeed(handler:((Int) -> Void)? = nil) {
        if let tempSpeed = cachedDiskReadSpeedMB {
            handler?(tempSpeed)
            return
        }
        WMDevice.sharedInstance().diskPerformanceAsync {[weak self] writeSpeed, readSpeed  in
            if writeSpeed > 0 && readSpeed > 0 {
                let averageValue = Int((writeSpeed + readSpeed)/2.0 * 0.8) //减少误差，*0.8
                self?.cachedDiskReadSpeedMB = averageValue
                handler?(averageValue)
            } else {
                handler?(2000) //获取失败时，默认返回值，基本不会触发这个流程
            }
        }
    }
    
    // 获取资源修复所有方式修复的预估时间
    func getResRepairEstimatedTimes(types:[LauncherResRepairType], handler: ((String, String, String) -> Void)? = nil){
        
        let totalRemoteSize = launcherCoreManager.getTotalRemoteSize("")
        let totalRemoteSizeMB = bytesToMB(bytes: Double(totalRemoteSize))
        getDiskWriteReadSpeed {diskReadSpeed in
            DispatchQueue.global().async {[weak self] in
                self?.downloadSpeedTest {[weak self] bytesPerSecond in
    //            重新安装时间=当前分支资源总大小/(网速峰值*1.2)
    //            完整修复时间=8分钟*500GB/(读写速度*0.9)*当前分支资源总大小/120GB+0.33*当前分支资源总大小/(网速峰值*1.2)
    //            仅损坏修复时间=损坏文件总大小/(网速峰值*1.2)
                    guard let weakSelf = self else {
                        return
                    }
                    guard let downloadSpeed = bytesPerSecond else {
                        return
                    }
                    
                    self?.latestDownloadSpeed = downloadSpeed
                    // 计算卸载重装总用时
                    let bytesPerSecondDouble = Double(downloadSpeed)
                    let reinstallTime = max(Double(totalRemoteSize) / (bytesPerSecondDouble * 1.2),1) //至少显示1s
                    let reinstallTimeFormat = String.formatTime(seconds: Int(reinstallTime))
                    
                    // 计算完整修复总用时
                    let diskReadSpeedDouble = Double(diskReadSpeed)
                    let speedMB = weakSelf.bytesToMB(bytes: bytesPerSecondDouble)
                    
                    let allStep1 = 8 * 500 / diskReadSpeedDouble
                    let allStep2 = totalRemoteSizeMB / 120
                    let allStep3 = 0.33 * (totalRemoteSizeMB / (speedMB * 1.2))
                    
                    let repairAllTime = max(allStep1 * allStep2 + allStep3, 1) //至少显示1s
                    let repairAllTimeFormat = String.formatTime(seconds: Int(repairAllTime))
                    
                    // 计算仅修复损害时间
                    var breakdowTimeFormat = ""
                    if types.contains(.breakdown),let exceptionFiles = weakSelf.getExceptionResFiles() {
                        let totalFileSize = weakSelf.getTotalFileSize(atPaths: exceptionFiles)
                        let breakdowTime = max(Double(totalFileSize) / (bytesPerSecondDouble * 1.2), 1) //至少显示1s
                        breakdowTimeFormat = String.formatTime(seconds: Int(breakdowTime))
                    }
                    DispatchQueue.main.async {
                        handler?(reinstallTimeFormat, repairAllTimeFormat, breakdowTimeFormat)
                    }
                }
            }
        }
    }
    
    // 下载速度测试
    func downloadSpeedTest(handler: ((Int64?) -> Void)? = nil) {
        speedTestRetryCount = speedTestRetryCount+1
        launcherCoreManager.speedTest(5) {[weak self] code, messsage, bytesPerSecond in
            if code == 0 && bytesPerSecond > 0 {
                self?.latestDownloadSpeed = bytesPerSecond
                handler?(bytesPerSecond)
                self?.speedTestRetryCount = 0
            } else {
                if let retryCount = self?.speedTestRetryCount, retryCount <= 3 {
                    // 重试测速
                    DispatchQueue.global().asyncAfter(deadline: .now() + 2.0) {
                        self?.downloadSpeedTest(handler: handler)
                    }
                } else {
                    handler?(nil)
                }
            }
        }
    }
    
    // 字节转换
    func bytesToMB(bytes: Double) -> Double {
        let mb = NSDecimalNumber(value: bytes / 1_048_576.0)
        let handler = NSDecimalNumberHandler(roundingMode: .plain, scale: 2, raiseOnExactness: false, raiseOnOverflow: false, raiseOnUnderflow: false, raiseOnDivideByZero: true)
        let roundedMB = mb.rounding(accordingToBehavior: handler)
        return roundedMB.doubleValue
    }
    
    // 获取本地文件大小
    func getTotalFileSize(atPaths filePaths: [String]) -> Int64 {
        let fileManager = FileManager.default
        var totalSize: Int64 = 0
        for filePath in filePaths {
            do {
                let attributes = try fileManager.attributesOfItem(atPath: filePath)
                if let fileSize = attributes[.size] as? Int64 {
                    totalSize += fileSize
                }
            } catch {
                log.debug("[\(LogModule.resfix)] Error getting file size for \(filePath): \(error.localizedDescription)")
            }
        }
        return totalSize
    }
}

// MARK: - 资源修复弹窗
extension LauncherPatcherManager {
    func canBeginRepair(index: Int) -> Bool {
        let installed = isInstalledApplication(index: index)
        var isRunning = false
        if let uniqueId = getSelectedAppUniqueId(index: index) {
            isRunning = isApplicationRunning(uniqueId: uniqueId)
        }
        return installed && !isRunning
    }
    
    // 显示资源修复主功能界面
    func showResRepairAlertView(index: Int) {
        LauncherEventManager.clickRepair()
        var resRepairInfo = [LauncherResRepairInfo(type: .reinstall),
                             LauncherResRepairInfo(type: .all)]
        if let exceptionfiles = self.getExceptionResFiles(), !exceptionfiles.isEmpty {
            resRepairInfo.append(LauncherResRepairInfo(type: .breakdown))
        }
        
        let _ = LauncherResRepairView.show(repairInfos: resRepairInfo, cancelHandler: {
            LauncherEventManager.cancleRepair()
        }, confirHandler:  {[weak self] type in
            if type == .reinstall {
                self?.reinstallSelectedApp()
            } else if type == .all {
                self?.beginShowRepairCheckAlertView()
            } else if type == .breakdown {
                self?.beginRepairAppRes()
            }
        })
    }
    
    // 点击完整修复后扫描界面
    func beginShowRepairCheckAlertView() {
        let (resRepairCheckView,alertView) = LauncherResRepairCheckView.show { [weak self] in
            self?.cancelCheckRepairAppRes()
        }
        
        checkRepairAppRes { [weak self] checkInfo in
            if checkInfo.status == .doing {
                resRepairCheckView.update(repairInfo: checkInfo)
            } else {
                alertView.dismiss()
                self?.showRepairCheckResultView(result: checkInfo)
            }
        }
    }
    // 点击完整修复扫描结果界面
    func showRepairCheckResultView(result: LauncherRepairCheckInfo) {
        
        if result.status == .needUpdate {
            let message = String(format: LauncherLocalization.localizedString(forKey: "repair_check_result"), result.needUpdateCount, result.needDownloadBytes, result.estimatedTime)
            let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "repair_check_tips"), message: message)
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "cancel"), handler: { action in
            }))
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "immediate_repair"), handler: {[weak self] action in
                self?.beginRepairAppRes()
            }))
            alertView.show(inView: nil)
        } else if result.status == .completed {
            let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "repair_check_tips"), message: LauncherLocalization.localizedString(forKey: "repair_check_completed"))
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "confirm"), handler: { action in
            }))
            alertView.show(inView: nil)
        } else {
            let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "repair_check_tips_error"), message: LauncherLocalization.localizedString(forKey: "repair_check_error"))
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "confirm"), handler: { action in
            }))
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "retry_check"), handler: {[weak self] action in
                self?.beginShowRepairCheckAlertView()
            }))
            alertView.show(inView: nil)
        }
    }
    
    // 主动触发损坏文件检查
    func beginCheckExceptionFilesAlertView() {
        // 计算仅修复损害时间
        guard let exceptionFiles = getExceptionResFiles(), !exceptionFiles.isEmpty else {
            return
        }
        
        DispatchQueue.global().async {[weak self] in
            self?.downloadSpeedTest {[weak self] bytesPerSecond in
                //            仅损坏修复时间=损坏文件总大小/(网速峰值*1.2)
                guard let weakSelf = self else {
                    return
                }
                guard let downloadSpeed = bytesPerSecond else {
                    return
                }
                let bytesPerSecondDouble = Double(downloadSpeed)
                self?.latestDownloadSpeed = downloadSpeed
                var breakdowTimeFormat = ""
                let totalFileSize = weakSelf.getTotalFileSize(atPaths: exceptionFiles)
                let breakdowTime = max(Double(totalFileSize) / (bytesPerSecondDouble * 1.2), 1) //至少显示1s
                breakdowTimeFormat = String.formatTime(seconds: Int(breakdowTime))
                let totalFileSizeFormat = WMMacPatcher.byte(toCapacity: UInt64(totalFileSize))
                DispatchQueue.main.async {
                    let message = String(format: LauncherLocalization.localizedString(forKey: "exception_files_found"), exceptionFiles.count, totalFileSizeFormat, breakdowTimeFormat)
                    let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "resource_repair"), message: message)
                    alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "cancel"), handler: { action in
                    }))
                    alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "immediate_repair"), handler: {[weak self] action in
                        self?.beginRepairAppRes()
                    }))
                    alertView.show(inView: nil)
                }
            }
        }
    }
}

// MARK: - Crash、统计日志、对象存储，文件上报等业务
extension LauncherPatcherManager {
    func sendFeedback(content: String?,
                      attachments: [LauncherAttachment]?,
                      progress: (( WMFeedbackStepType,Float) -> Void)?,
                      completion: (([AnyHashable:Any]?, Error?) -> Void)?) {
        
        guard let oudConfig = LaunchUIConstant.LauncherConfig?.oudConfig else {
            let userInfo: [String: Any] = [
                NSLocalizedDescriptionKey: "初始化配置文件错误",
            ]
            completion?(nil,  NSError(domain: NSCocoaErrorDomain, code: -1, userInfo: userInfo))
            return
        }
        var attachmentUrls = [URL]()
        if let attachments = attachments {
            for attachment in attachments {
                if let url = attachment.url {
                    attachmentUrls.append(url)
                }
            }
        }
        let branchInfo = getSelectedAppBranchVersionInfo(index: nil)
        launcherCoreManager.setupOudAppId(oudConfig.appId, appKey: oudConfig.appKey, channelId:oudConfig.channelId, hostUrl: oudConfig.hostUrl, areaType: Int(oudConfig.areaType)!)
        launcherCoreManager.sendFeedback(withContent: content, appVersion:branchInfo?.localVersion, attachments: attachmentUrls, progress: progress, completion: completion)
    }
    
    func setupDependencySDK() {
        launcherCoreManager.launcherLogDirPath = LauncherAppDataManager.shared.launcherLogDirPath
        if let gameLogPath = LaunchUIConstant.LauncherConfig?.config.gameLogPath {
            let expandedPath = NSString(string: gameLogPath).expandingTildeInPath
            launcherCoreManager.gameLogDirPath = expandedPath
        }
        
        if let analysisConfig = LaunchUIConstant.LauncherConfig?.analysisConfig {
            if let mediaId = analysisConfig.mediaId {
                WMMacLauncherMonitor.setupAnalysisMediaId(mediaId)
            }
            WMMacLauncherMonitor.setupAnalysisAppId(analysisConfig.appId, channelId: analysisConfig.channelId)
        }
        
        if let crashConfig = LaunchUIConstant.LauncherConfig?.crashConfig {
#if LAUNCHER_AREA_OVERSEA
        let isOVersea = true
#else
        let isOVersea = false
#endif
            WMMacLauncherMonitor.setupCrashAppId(crashConfig.appId, appKey: crashConfig.appKey,isOversea: isOVersea)
        }
    }
}
