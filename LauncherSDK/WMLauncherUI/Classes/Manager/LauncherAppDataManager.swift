import Foundation
import CryptoKit
import WMLauncherCore

final class LauncherAppDataManager {
    
    static let shared = LauncherAppDataManager()
    
    private var launcherPath: String!
    public var launcherLogDirPath: String!
    public var launcherLogFullPath: String!
    private let accessLock = NSLock()
    private var allAppStatusDict: [String: LauncherAppAllInfo] = [:]
    public var runningAppUniqueInfo: (String,String)? //只启动时候检查一次并记录
    public var allBranchWorkPaths: [String: [String: String]] = [:]
    
    // MARK: - 初始化
    private init() {
        self.launcherPath = generateLauncherPath()
        launcherLogDirPath = launcherPath.appendingPathComponent("Cache").appendingPathComponent("Log")
        launcherLogFullPath = launcherLogDirPath.appendingPathComponent("launcher.log")
        initAllAppStatusData()
    }
    
    private func generateLauncherPath() -> String! {
        // 获取 Application Support 目录路径
        if let applicationSupportPath = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first {
            let launcherPath = applicationSupportPath.appendingPathComponent(LaunchUIConstant.LauncherBundleId)
            return launcherPath.path
        }
        
        // 如果获取 Application Support 目录失败，返回默认值路径
        return "/Users/<USER>/Application Support/\(LaunchUIConstant.LauncherBundleId)"
    }
    
    func initAllAppStatusData() {
        guard let launcherConfig = LaunchUIConstant.LauncherConfig else {
            return
        }
        
        for appItem in launcherConfig.config.multiConfig {
            
            let configPath = appItem.patcherConfig
            let appCachePath = appItem.cachePath
            let clientExeName = appItem.client
            let bootstrapBundleId = appItem.bootstrapBundleId
            let bundleId = appItem.bundleId
            let displayAppName = appItem.versionName
            
            /* 路径示例
             * launcherPath => "~/Library/Application Support/com.laohu.Launcher"
             * subAppCachePath => "~"/Library/Application Support/com.laohu.Launcher/{cachePath}"
             * logPath =>" ~/Library/Application Support/com.laohu.Launcher/{cachePath}/log"
             * patchPath =>" ~/Library/Application Support/com.laohu.Launcher/{cachePath}/patcher"
             * resPath =>" ~/Library/Application Support/com.laohu.Launcher/{cachePath}/game"
             * targetAppFullPath => ~/Library/Application Support/com.laohu.Launcher/{cachePath}/game/${client}"
             */
            let configFullPath = Bundle.main.path(forResource: configPath, ofType: ".json")
            let subAppCachePath = launcherPath.appendingPathComponent(appCachePath)
            let logPath = subAppCachePath.appendingPathComponent(LaunchUIConstant.PatcherLogPath)
            let patchPath = subAppCachePath.appendingPathComponent(LaunchUIConstant.PatcherSelfPath)
            let resPath = subAppCachePath.appendingPathComponent(LaunchUIConstant.PatcherGamePath)
            let targetAppFullPath = resPath.appendingPathComponent(clientExeName)
            
            var appConfig = LauncherAppConfig()
            appConfig.cacheBasePath = subAppCachePath
            appConfig.clientPath = targetAppFullPath
            appConfig.bootstrapBundleId = bootstrapBundleId
            appConfig.bundleId = bundleId
            appConfig.patcherConfig = configFullPath!
            appConfig.resPath = resPath
            appConfig.patcherPath = patchPath
            appConfig.logPath = logPath
            appConfig.displayAppName = displayAppName
            appConfig.uniqueId = appItem.uniqueId
            
            let key = appItem.uniqueId
            allAppStatusDict[key] = LauncherAppAllInfo(base: appConfig)
            
            //判断当前应用是否正在运行
            if let runningAppPath = WMMacLauncherCore.isApplicationRunning(bundleId) {
                let appCachePath = URL(fileURLWithPath: targetAppFullPath)
                // 获取appCachePath除.app文件名外的其他路径
                let appFullDirPath = appCachePath.deletingLastPathComponent()
                if runningAppPath.contains(appFullDirPath.absoluteString) {
                    runningAppUniqueInfo = (key,bundleId)
                }
            }
            
            // Save branch work path
            if let branchName = getBranchName(for: configPath) {
                allBranchWorkPaths[branchName] = ["userdata": WMMacLauncherCore.getGameLogDataPath(withLogPath: logPath)];
            }
        }
    }
    
    // MARK: - 更新接口
    func updateVersionInfo(for uniqueId: String, versionInfo: LauncherAppBranchVersionInfo?) {
        accessLock.lock()
        defer { accessLock.unlock() }
        
        let key = uniqueId
        if var info = allAppStatusDict[key] {
            info.version = versionInfo
            allAppStatusDict[key] = info
        }
    }
    
    func updateDownloadInfo(for uniqueId: String, downloadInfo: LauncherAppDownloadInfo?) {
        accessLock.lock()
        defer { accessLock.unlock() }
        
        let key = uniqueId
        if var info = allAppStatusDict[key] {
            info.download = downloadInfo
            allAppStatusDict[key] = info
        }
    }
    
    func updateVersionCheckStatus(for uniqueId: String, status: LauncherAppStatus) {
        accessLock.lock()
        defer { accessLock.unlock() }
        
        let key = uniqueId
        allAppStatusDict[key]?.checkStatus = status
    }
    
    
    func getVersionCheckStatus(for uniqueId: String) -> LauncherAppStatus {
        accessLock.lock()
        defer { accessLock.unlock() }
        
        let key = uniqueId
        return allAppStatusDict[key]?.checkStatus ?? .unknown
    }
    
    func getBaseInfo(for uniqueId: String) -> LauncherAppConfig? {
        accessLock.lock()
        defer { accessLock.unlock() }
        
        let key = uniqueId
        return allAppStatusDict[key]?.base
    }
    
    func getVersionInfo(for uniqueId: String) -> LauncherAppBranchVersionInfo? {
        accessLock.lock()
        defer { accessLock.unlock() }
        
        let key = uniqueId
        return allAppStatusDict[key]?.version
    }
    
    func getDownloadInfo(for uniqueId: String) -> LauncherAppDownloadInfo? {
        accessLock.lock()
        defer { accessLock.unlock() }
        
        let key = uniqueId
        return allAppStatusDict[key]?.download
    }
    
    func getBranchName(for configName: String) -> String? {
        if let configPath = Bundle.main.path(forResource: configName, ofType: "json") {
            do {
                let data = try Data(contentsOf: URL(fileURLWithPath: configPath))
                let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any]
                
                // 解析branchName
                if let branchName = json?["branchName"] as? String {
                    return branchName
                }
            } catch {
                log.emergency("[\(LogModule.core)] Error parsing patcher config JSON file: \(error)")
            }
        }
        return nil
    }
}
