//
//  String.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/9.
//

import Foundation
import CryptoKit

extension String {
    // 时间格式化
    static func formatTime(seconds: Int) -> String {
        let days = seconds / (24 * 3600)
        let hours = (seconds % (24 * 3600)) / 3600
        let minutes = (seconds % 3600) / 60
        let secs = seconds % 60
        
        var timeComponents: [String] = []
        if days > 0 {
            timeComponents.append("\(days)天")
        }
        if hours > 0 {
            timeComponents.append("\(hours)小时")
        }
        if minutes > 0 {
            timeComponents.append("\(minutes)分")
        }
        timeComponents.append("\(secs)秒")
        
        return timeComponents.joined(separator: "")
    }
    
    // md5前16位
    func md5Prefix16() -> String {
        let digest = Insecure.MD5.hash(data: self.data(using: .utf8) ?? Data())
        return digest.map { String(format: "%02hhx", $0) }.joined().prefix(16).uppercased()
    }
    
    // 针对url追加自定义参数
    func appendingQueryParameters(_ parameters: [String: String]) -> String {
        guard var urlComponents = URLComponents(string: self) else {
            return self
        }
        
        var queryItems = urlComponents.queryItems ?? []
        for (key, value) in parameters {
            queryItems.append(URLQueryItem(name: key, value: value))
        }
        urlComponents.queryItems = queryItems
        return urlComponents.string ?? self
    }
}
