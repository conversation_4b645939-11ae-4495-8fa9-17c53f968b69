//
//  WKWebView+Extensions..swift
//  WMMacLauncherUI
//
//  Created by zhangjia on 2024/12/10.
//

import Foundation
import WebKit

extension WKWebView {
    /// 禁用右键点击菜单
    func disableRightClickMenu() {
        let scriptSource = """
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        }, false);
        """
        let userScript = WKUserScript(source: scriptSource, injectionTime: .atDocumentEnd, forMainFrameOnly: true)
        self.configuration.userContentController.addUserScript(userScript)
    }
}
