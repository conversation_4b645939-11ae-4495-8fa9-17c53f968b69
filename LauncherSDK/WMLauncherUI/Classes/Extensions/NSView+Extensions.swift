//
//  NSView+Extensions.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON>jia on 2024/11/12.
//

import AppKit

// NSView 扩展类
extension NSView {
    
    // 将指定的子视图移到指定视图的上面
    func bringSubviewToFront(_ subview: NSView) {
        guard subview.superview == self else {
            return
        }
        // 移动子视图到最上层
        subview.removeFromSuperview()
        addSubview(subview)
    }

    // 将 viewToMove 移动到 siblingView 的上面
    func bringSubview(targetView: NSView, above referenceView: NSView) {
        guard !self.subviews.isEmpty,
              targetView.superview == self,
              referenceView.superview == self else {
            return
        }
        
        // 获取当前子视图的索引
        if  let indexOfTargetView = self.subviews.firstIndex(of: targetView),
            let indexOfReferenceView = subviews.firstIndex(of: referenceView) {
            
            if indexOfTargetView != indexOfReferenceView + 1 {
                targetView.removeFromSuperview()
                addSubview(targetView, positioned: .above, relativeTo: referenceView)
            }
        }
    }
}
