//
//  NSImage+Extensions.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON>ji<PERSON> on 2024/12/2.
//

import Foundation
extension NSImage {
    func resizableImage(capInsets: NSEdgeInsets) -> NSImage {
        let resizableImage = NSImage(size: self.size)
        
        // 开始绘制
        resizableImage.lockFocus()
        
        // 绘制原始图像
        draw(in: NSRect(origin: .zero, size: self.size))
        
        // 解锁焦点
        resizableImage.unlockFocus()
        
        // 设置capInsets
        resizableImage.capInsets = capInsets
        
        return resizableImage
    }
    
    static func load(inLauncherBundle name: NSImage.Name) -> NSImage? {
        return Bundle.launcherMainBundle().image(forResource: name)
    }
}
