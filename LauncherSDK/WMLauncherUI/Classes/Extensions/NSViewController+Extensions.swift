//
//  NSViewController+Extensions.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON>jia on 2024/11/13.
//

import Foundation
import WMXAppKit

extension NSViewController {
    
    func showViewController() {
        var didShowPopView = false
        if let contentView = NSWindow.wmxkit_applicationKey().contentView {
            // 判断contviews subview中，是否已包含WMXOverlayContainerView的类
            for subview in contentView.subviews {
                if ((subview as? WMXOverlayContainerView) != nil) {
                    didShowPopView = true
                    break
                }
            }
        }
        if didShowPopView {
            // 覆盖弹出的view不再设置背景色
            self.wmxkit_showModalSession(false, bgColor: NSColor.clear, position: CGPointZero)
        } else {
            self.wmxkit_showModalSession(false)
        }
    }
    
    func pushViewController(toVC: NSViewController) {
        self.wmxkit_push(toVC, animated: true)
    }

    func popViewController() -> NSViewController?
    {
        return self.wmxkit_pop(animated: false)
    }
}
