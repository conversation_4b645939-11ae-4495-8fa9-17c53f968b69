//
//  LaunchUIConstant.swift
//  WMMacLauncherUI
//
//  Created by z<PERSON>jia on 2024/11/4.
//

import Foundation

// 游戏下载、运行状态
enum LauncherAppEnv {
    case unknown
    case beta
    case betaTest
    case release
    case releaseTest
}

// 游戏下载、运行状态
// 其他已知错误码：-8（patcher配置文件不对 or 游戏没传资源）
//              22（游戏资源包上传有问题，新版本目录大小写有修改）
enum LauncherErrorCode : Int  {
    case unknown = 201 //对应PatcherTag_UnKnown，patcher返回未知错误
    case parsingFailed = 202 //数据解析失败
    case networkError = 203  //网络异常
}

struct LauncherAppAllInfo {
    var checkStatus : LauncherAppStatus = .unknown
    var base : LauncherAppConfig!   //基础信息，解析配置获得
    var version : LauncherAppBranchVersionInfo?     //版本信息，通过patcher接口获取
    var download : LauncherAppDownloadInfo?     //版本下载进度信息
}

struct LauncherAppDownloadInfo {
    let speed: String       // 下载速度
    let downloadedSize: String   //已下载的大小
    let totalSize: String   // 总大小
    let progress: String    // 进度百分比
    let remainingTime: String    // 剩余时间
    let appVersionName:String //当前下载的版本信息
    var localVersion: String? = nil // 本地安装的版本
    var fixResTotalSize: Int64? = nil    // 下载完，资源修复阶段，总大小
    var fixResFixedSize: Int64? = nil    // 下载完，资源修复阶段，已修复大小
    var fixResFile: String? = nil    // 下载完，资源修复阶段，当前修复的文件名
}

struct LauncherAppAavaliableVersion {
    var branchName: String
    var version: String
}

struct LauncherAppBranchVersionInfo {
    let environment: LauncherAppEnv
    var localVersion: String // 本地当前版本
    var remoteVersion: String // 最新版本
    var beingInstallVersion: String? = nil // 正在安装的版本
    let preRelease: String
    var avaliableVersions: [LauncherAppAavaliableVersion]? = nil
    var appVersionName:String? = nil //当前下载的版本名
    
    var avaliableVersionStrings: [String] {
        return avaliableVersions?.map { $0.version } ?? []
    }
}

// 游戏下载、运行状态
enum LauncherAppStatus: Int {
    case unknown
    case checking
    case checkFail
    case startDownloading   // 本地没安装，开始下载
    case downloading        // 本地没安装，正在下载
    case resProcessing      // 下载完成，正在处理资源
    case pauseDownloading   // 暂停下载
    case cancelDownloading  // 取消下载
    case needUpdating       // 本地已安装，需要更新
    case downloadingFail    // 下载失败
    case switchVersionUpdate //手动切换版本，导致需要更新
    case startGame
    case gameInProgress
    case operationDone //操作中断、完成等响应，仅用于还原按钮点击状态
}

// 调用Patcher操作类型
enum PatcherDownloadStatus {
    case idle        // 空闲
    case doing       // 下载任务
    case pause       // 暂停任务
}

// sidebar item App右键菜单项
enum AppOperateType: Int, CaseIterable {
    case openAppDir = 0
    case removeApp
    case switchApp
    case resRepair
    case cleanCache //从此处往下的，不需要加到右键菜单中
    case download
    case update
    case launcher
    case changeAppVersion
    func description() -> String {
        switch self {
        case .openAppDir:
            return LauncherLocalization.localizedString(forKey: "open_game_directory")
        case .removeApp:
            return LauncherLocalization.localizedString(forKey: "delete_game")
        case .switchApp:
            return LauncherLocalization.localizedString(forKey: "switch_version")
        case .resRepair:
            return LauncherLocalization.localizedString(forKey: "one_key_repair")
        case .cleanCache:
            return LauncherLocalization.localizedString(forKey: "clear_cache")
        default:
            return ""
        }
    }
}
struct LaunchUIConstant {
    
    public static let MainWindowMaxWidth = 1600.0
    
    public static let MainWindowMaxHeight = 900.0
    
    public static var MainWindowScaleleFactor = 1.0
    
    public static var MainWindowWidth: Double {
        return MainWindowMaxWidth * MainWindowScaleleFactor
    }
    
    public static var MainWindowHeight: Double {
        return MainWindowMaxHeight * MainWindowScaleleFactor
    }

    public static let SidebarWidth = 120.0
    
    public static let WindowTitlebarHegight = 28.0 //窗口标题栏高度
    
    public static let MainPopWindowWidth = 648.0 //弹窗视图(设置、问题反馈等)宽
    
    public static let MainPopWindowHeight = 403.0    //弹窗视图(设置、问题反馈等)高
    
    public static var LauncherConfig : LauncherConfigModel?
    
    public static let LauncherBundleId = Bundle.main.bundleIdentifier ?? "com.laohu.Launcher"
    
    public static let PatcherLogPath = "/log"
    public static let PatcherSelfPath = "/patcher"
    public static let PatcherGamePath = "/game"
}

extension Notification.Name {
    static let LauncherMainWindowScreenDidNotification = Notification.Name("LauncherMainWindowScreenDidNotification")
    static let LauncherPlayerVideoSwitchDidNotification = Notification.Name("LauncherPlayerVideoSwitchDidNotification")
}

// 资源修复检查状态
enum LauncherRepairCheckStatus {
    case doing           //正在检查
    case fail           //检查失败
    case needUpdate     //检查完成，资源损坏，需要更新
    case completed      //检查完成，资源完整
}

struct LauncherRepairCheckInfo {
    var status: LauncherRepairCheckStatus = .doing
    var message: String = ""
    var totalFileCount: String = ""        //需要检查总文件个数
    var checkedFileCount: String = ""      //已检查总文件个数
    var progress: Double = 0.0             //检查进度
    var progressPercent: String = ""       //检查进度百分比
    var needDownloadBytes: String = ""     //需要修复下载的总大小
    var needUpdateCount: String = ""       //需要需要下载的总个数
    var estimatedTime: String = ""         //预估时间
}

// 设置界面中枚举类型
enum LauncherSettingsType: Hashable {
    case startup
    case update
    case speedLimit
    case route
    case cleanCache
    case resRepair
    case version
    case about
    case feedback

    var titles: (title: String, buttonTitle: String) {
        switch self {
        case .startup:
            return (LauncherLocalization.localizedString(forKey: "startup_setting"), "")
        case .update:
            return (LauncherLocalization.localizedString(forKey: "update_setting"), "")
        case .speedLimit:
            return (LauncherLocalization.localizedString(forKey: "speedLimit_setting"), "")
        case .route:
            return (LauncherLocalization.localizedString(forKey: "route_setting"), "")
        case .cleanCache:
            return (LauncherLocalization.localizedString(forKey: "clear_cache"), LauncherLocalization.localizedString(forKey: "clear_cache"))
        case .resRepair:
            return (LauncherLocalization.localizedString(forKey: "game_resource_repair"), LauncherLocalization.localizedString(forKey: "one_key_repair"))
        case .version:
            return (LauncherLocalization.localizedString(forKey: "launcher_version"), LauncherLocalization.localizedString(forKey: "check_update"))
        case .about:
            return (LauncherLocalization.localizedString(forKey: "about"), LauncherLocalization.localizedString(forKey: "user_agreement"))
        case .feedback:
            return (LauncherLocalization.localizedString(forKey: "feedback"), LauncherLocalization.localizedString(forKey: "one_key_submit"))
        }
    }
}

// 资源修复检查状态
enum LauncherTargetClientEnv: Int, CaseIterable {
    case shipping = 0
    case dev = 1
    case test = 2
}

// 资源下载时，自动安装 or 更新类型
enum LauncherGameAutoDownloadType {
    case none           //不自动更新、安装
    case install        //自动安装
    case update         //自动更新
}


// Launcher确认关闭操作类型
enum LauncherExitChoiceType {
    case unknown    //未知
    case cancel     //取消退出
    case confirm    //确定退出
}

// Launcher确认关闭操作类型
enum LauncherShortcutKeyType {
    case exit       //退出整个应用
    case hide       //隐藏
    case close      //关闭窗口，但不退出应用
}
