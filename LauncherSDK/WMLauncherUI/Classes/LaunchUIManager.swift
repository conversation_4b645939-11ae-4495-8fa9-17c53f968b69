//
//  LaunchUIManager.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/4.
//

import Foundation
import WMCategories

public class LaunchUIManager {
    public static let shared = LaunchUIManager()
    private var mainScreenSize = CGSizeZero
    private init() { }
    
    public func setup(configName: String, appKey: String) -> Bool {
        
        if LaunchUIConstant.LauncherConfig == nil {
            if let configModel = loadEncryptConfig(configName:configName, appKey: appKey) {
                LaunchUIConstant.LauncherConfig = configModel
                return true
            }
        }
        
        return false
    }
    
    public func getLauncherAppDelegate() -> any NSApplicationDelegate {
        let appDelegate = LauncherAppDelegate()
        return appDelegate
    }
    
    func loadEncryptConfig(configName: String, appKey: String) -> LauncherConfigModel? {
        guard let url = Bundle.main.url(forResource:configName, withExtension: "json"),
              let encryptStr = try? String(contentsOf: url, encoding: .utf8) else {
            
            log.emergency("[\(LogModule.core)] Error: Could not load \(configName).json file.")
            return nil
        }
        
        // 创建 JSON 解码器
        let decoder = JSONDecoder()
        let key: String
        if appKey.count >= 16 {
            key = String(appKey.suffix(16))
        } else {
            return nil         
        }
        let decryptStr = (encryptStr as NSString).wm_decryptedWithAES(usingKey: key, andIV: nil)
        if let tempDecryptStr = decryptStr, let tempDecryptData = tempDecryptStr.data(using: .utf8) {
            decoder.keyDecodingStrategy = .convertFromSnakeCase  // 自动转换驼峰命名法
            do {
                let config = try decoder.decode(LauncherConfigModel.self, from: tempDecryptData)
                return config
            } catch {
                log.emergency("[\(LogModule.core)] Error decoding \(configName).json file.")
                return nil
            }
        }
        return nil
    }
    
    
    // 根据显示器大小，确定主窗口缩放比例
    func loadMainWindowScalingFactor() -> Double {
        guard let screenSize = NSScreen.main?.frame.size else {
            return 1.0
        }
        
        // 判断显示器大小是有有变化
        if CGSizeEqualToSize(mainScreenSize, screenSize) {
            return LaunchUIConstant.MainWindowScaleleFactor
        }
        
        mainScreenSize = screenSize
        let screenSizeWidth = screenSize.width - LaunchUIConstant.SidebarWidth * 2.0 //留出侧边栏多App列表显示空间
        let screenSizeHeight = screenSize.height
        
        let widthScale =  screenSizeWidth/LaunchUIConstant.MainWindowMaxWidth
        let heightScale = screenSizeHeight/LaunchUIConstant.MainWindowMaxHeight
        
        var scale = min(widthScale, heightScale)
        if scale < 1.0 {
            scale = max(0.5, scale) //确保缩放值最小为0.5
        } else {
            scale = 1.0 //确保缩放值最大为1.0
        }
        return scale
    }
}
