//
//  LauncherFullWebviewController.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/13.
//

import Cocoa
import WebKit
import WMWebViewJavascriptBridge
import WMMasonry

class LauncherFullWebviewController: NSViewController {
    private var mainWebview: WKWebView!
    private var webviewBridge: WMWKWebViewJavascriptBridge!
    public var webUrl: String!
    
    override func loadView() {
        let contentRect = NSRect(x: 0, y: 0, width: LaunchUIConstant.MainWindowWidth, height: LaunchUIConstant.MainWindowHeight)
        view = NSView(frame: contentRect)
    }
    
    init(webUrl: String) {
        self.webUrl = webUrl
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.clear.cgColor
        
        let config = WKWebViewConfiguration()
        mainWebview = WKWebView(frame: view.bounds, configuration: config)
        //        mainWebview.navigationDelegate = self
        view.addSubview(mainWebview)
        mainWebview.setValue(false, forKey: "drawsBackground")
        webviewBridge = WMWKWebViewJavascriptBridge(for: mainWebview)
        webviewBridge.registerHandler("callWmNative") { [weak self] (data, responseCallback) in
            self?.didReceiveJSMessage(jsData: data)
        }
        
        if let url = URL(string: webUrl) {
            mainWebview.load(URLRequest(url: url))
        }
        mainWebview.disableRightClickMenu()
        
        let center = NotificationCenter.default
        center.addObserver(forName: .LauncherMainWindowScreenDidNotification, object: nil, queue: .main) { [weak self] notification in
            guard let self = self else { return }
            self.handleMainWindowScreenChangeNotification(notification)
        }
    }
    deinit {
        NotificationCenter.default.removeObserver(self, name: .LauncherMainWindowScreenDidNotification, object: nil)
    }
    
    private func didReceiveJSMessage(jsData: Any?) {
        if let dictionary = jsData as? [String: Any] {
            if let funcName = dictionary["funcname"] as? String {
                if funcName == "close" {
                    let _ = popViewController()
                }
            }
        }
    }
}

extension LauncherFullWebviewController {

    private func handleMainWindowScreenChangeNotification(_ notification: Notification) {
        view.frame = NSRect(x: 0, y: 0, width: LaunchUIConstant.MainWindowWidth, height: LaunchUIConstant.MainWindowHeight)
        mainWebview.frame = view.bounds
    }
}
