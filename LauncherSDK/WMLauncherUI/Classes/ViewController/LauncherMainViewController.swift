//
//  MainLauncherViewController.swift
//  WMMacLauncherUI
//
//  Created by z<PERSON>ji<PERSON> on 2024/11/4.
//


import Cocoa
import AVKit
import WebKit
import WMWebViewJavascriptBridge
import WMMasonry
import Combine

class LauncherMainViewController: NSViewController {
    private let expandButton = LauncherCustomButton()
    private let mainView = NSView()
    private var sidebarViewController: SidebarViewController?
    private var isSidebarVisible = false
    private var placeholderBgImageView: NSImageView? //视频没加载出来的占位背景图
    private var playerView: AVPlayerView?
    private var mainWebview: WKWebView!
    private var webviewBridge: WMWKWebViewJavascriptBridge!
    private var downloadView: LauncherAppDownloadPanel?
    private var launcherPanel: LauncherAppStatusPanel?
    private var toolbarView: LauncherToolbarView!
    private var cancellable: AnyCancellable?
    private let patcherManager = LauncherPatcherManager.shared
    private var webviewExpandStatus = false
    
    override func loadView() {
        let contentRect = NSRect(x: 0, y: 0, width: LaunchUIConstant.MainWindowWidth, height: LaunchUIConstant.MainWindowHeight)
        view = NSView(frame: contentRect)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.clear.cgColor
        
        // 配置 mainView
        mainView.wantsLayer = true
        mainView.layer?.backgroundColor = NSColor.clear.cgColor
        mainView.frame = NSRect(x: 0, y: 0, width: LaunchUIConstant.MainWindowWidth, height: LaunchUIConstant.MainWindowHeight)
        view.addSubview(mainView)
        
        let placeholderImageView = NSImageView(frame: mainView.bounds)
        placeholderImageView.autoresizingMask = [.width, .height]
        placeholderImageView.imageScaling = .scaleProportionallyUpOrDown
#if LAUNCHER_TYPE_BENCHMARK
        placeholderImageView.image = NSImage.load(inLauncherBundle: "mainview_placeholder_bg_bm")
#else
        placeholderImageView.image = NSImage.load(inLauncherBundle: "mainview_placeholder_bg")
#endif
        
        mainView.addSubview(placeholderImageView)
        placeholderBgImageView = placeholderImageView
        
        // 创建 AVPlayerView
#if !LAUNCHER_TYPE_BENCHMARK
        playerView = AVPlayerView(frame: mainView.bounds)
        playerView?.autoresizingMask = [.width, .height]
        playerView?.controlsStyle = .none
        playerView?.isHidden = true
        mainView.addSubview(playerView!)
#endif
        // 设置主背景webview
        setupMainWebview()
        // 添加Logo
        addLogoImageView()
        // 添加工具栏
        addTopToolbarView()
        
        // 根据配置文件，判断是否需要显示侧边栏
        if patcherManager.isSupportMultiAppSwitch() {
            // 创建自定义按钮，设置不同状态的图片
            expandButton.target = self
            expandButton.action = #selector(toggleSidebar)
            expandButton.title = ""
            changeExpandButtonImage()
            mainView.addSubview(expandButton)
            expandButton.my_mas_makeConstraints { make in
                make?.width.mas_equalTo()(24)
                make?.height.mas_equalTo()(108)
                make?.right.equalTo()(mainView)
                make?.centerY.equalTo()(mainView)
            }
            // 默认打开
            let sidebarCloseStatus = LauncherSettingsManager.shared.sidebarCloseStatus
            if !sidebarCloseStatus {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.toggleSidebar()
                }
            }
        } else {
            loadWebView(pageUrl: nil)
        }
        
        // 播放背景视频
        playVideo()
        // 调用Patcher检查本地游戏资源
        bindPatcherManager()
        beginSeletedAppCheck()
        
        let center = NotificationCenter.default
        center.addObserver(forName: .LauncherMainWindowScreenDidNotification, object: nil, queue: .main) { [weak self] notification in
            guard let self = self else { return }
            self.handleMainWindowScreenChangeNotification(notification)
        }
        
        // 添加视频播放开关修改通知通知监听
        if let _ = playerView {
            center.addObserver(forName: .LauncherPlayerVideoSwitchDidNotification, object: nil, queue: .main) { [weak self] notification in
                guard let self = self else { return }
                self.playerVideoSwitchDidChange()
            }
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    private func setupMainWebview() {
        let config = WKWebViewConfiguration()
        mainWebview = WKWebView(frame: mainView.bounds, configuration: config)
        mainWebview.uiDelegate = self
        mainView.addSubview(mainWebview)
        mainWebview.setValue(false, forKey: "drawsBackground")
       
        mainView.wantsLayer = true
        mainView.layer?.backgroundColor = NSColor.clear.cgColor
        webviewBridge = WMWKWebViewJavascriptBridge(for: mainWebview)
        webviewBridge.setWebViewDelegate(self)
        webviewBridge.registerHandler("callWmNative") { [weak self] (data, responseCallback) in
            self?.didReceiveJSMessage(jsData: data)
        }
        mainWebview.disableRightClickMenu()
    }
    
    private func loadWebView(pageUrl: String?) {
        mainWebviewDidOpen(isOpen:false)
        if let launcherConfig = pageUrl,
           let url = URL(string: launcherConfig) {
            mainWebview.load(URLRequest(url: url))
            mainWebview.isHidden = false
        } else {
            mainWebview.isHidden = true
        }
    }
    
    private func beginSeletedAppCheck() {
        let autoUpdateGame = LauncherSettingsManager.shared.gameAutoUpdate
        patcherManager.beginPatcherDidSeletedApp(index: patcherManager.latestSelectAppIndex, forced: false, autoDownloadType: autoUpdateGame ? .update : .none)
        patcherManager.loadAllBranchAppVersionInfo()
    }
    
    private func addLogoImageView() {
        if let gameLogoView = gameLogoImageView {
            mainView.addSubview(gameLogoView)
            gameLogoView.my_mas_makeConstraints { make in
                make?.width.mas_equalTo()(142)
                make?.height.mas_equalTo()(142)
                make?.top.offset()(24)
                make?.left.offset()(24)
            }
        }
        
        if let studioLogoView = studioLogoImageView {
            mainView.addSubview(studioLogoView)
            studioLogoView.my_mas_makeConstraints { make in
                make?.width.mas_equalTo()(128)
                make?.height.mas_equalTo()(40)
                make?.left.offset()(36)
                make?.bottom.offset()(-46)
            }
            
#if LAUNCHER_TYPE_BENCHMARK
            studioLogoView.isHidden = true
#endif
        }
    }
    
    lazy var gameLogoImageView: NSImageView? = {

        guard let image = NSImage.load(inLauncherBundle: "mainview_game_logo") else {
            return nil
        }
        
        let imageView = NSImageView()
        imageView.image = image
        return imageView
    }()
    
    lazy var studioLogoImageView: NSImageView? = {
        guard let image = NSImage.load(inLauncherBundle: "mainview_studio_logo") else {
            return nil
        }
        
        let imageView = NSImageView()
        imageView.image = image
        return imageView
    }()
    
    func addTopToolbarView() {
        toolbarView = LauncherToolbarView()
        mainView.addSubview(toolbarView)
        toolbarView.my_mas_makeConstraints { make in
            make?.top.offset()(0)
            make?.left.offset()(0)
            make?.right.offset()(0)
            make?.height.offset()(30)
        }
        
        toolbarView.buttonClickHandler = {[weak self] type in
            switch type {
            case .sound:
                if let player = self?.playerView?.player {
                    self?.playerView?.player?.isMuted = !player.isMuted
                }
            case .close:
                if let window = NSApplication.shared.windows.first {
                    window.performClose(nil)
                }
            case .minimize:
                if let window = NSApplication.shared.windows.first {
                    window.performMiniaturize(nil)
                }
            case .settings:
                self?.showSettingsViewCtr()
                break
            default:
                break
            }
        }
    }
    
    func showSettingsViewCtr() {
        let viewCtr = LauncherSettingsViewController()
        viewCtr.showViewController()
    }
}

// MARK: - 侧边栏显示、隐藏相关
extension LauncherMainViewController {
    private func setupSidebarView(shouldShow: Bool) {
        if shouldShow {
            guard sidebarViewController == nil else { return }
            
            let sidebarVC = SidebarViewController()
            addChild(sidebarVC)
            sidebarVC.view.frame = NSRect(x: mainView.frame.width, y: 0, width: LaunchUIConstant.SidebarWidth, height: LaunchUIConstant.MainWindowHeight)
            view.addSubview(sidebarVC.view)
            sidebarVC.mainView = mainView
            sidebarViewController = sidebarVC
        } else {
            guard let sidebarVC = sidebarViewController else { return }
            
            sidebarVC.removeFromParent()
            sidebarVC.view.removeFromSuperview()
            sidebarViewController = nil
        }
    }
    
    @objc private func toggleSidebar() {
        guard let window = view.window else { return }
        expandButton.isEnabled = false
        isSidebarVisible.toggle()
        LauncherSettingsManager.shared.sidebarCloseStatus = !isSidebarVisible
        changeExpandButtonImage()
        let frame = window.frame
        if isSidebarVisible {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.2 // 设定动画时长
                context.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut) // 平滑动画

                let newFrame = NSRect(x: window.frame.origin.x,
                                      y: window.frame.origin.y,
                                      width: frame.width + LaunchUIConstant.SidebarWidth,
                                      height: window.frame.height)

                window.animator().setFrame(newFrame, display: true)
            } completionHandler:{
                self.expandButton.isEnabled = true
            }
            setupSidebarView(shouldShow: true)
        } else {
            NSAnimationContext.runAnimationGroup { context in
                context.duration = 0.2 // 设定动画时长
                context.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut) // 平滑动画

                let newFrame = NSRect(x: window.frame.origin.x,
                                      y: window.frame.origin.y,
                                      width: frame.width - LaunchUIConstant.SidebarWidth,
                                      height: window.frame.height)

                window.animator().setFrame(newFrame, display: true)
                
            } completionHandler: {
                self.setupSidebarView(shouldShow: false)
                self.expandButton.isEnabled = true
            }
        }
    }
    
    private func changeExpandButtonImage() {
        if isSidebarVisible {
            let normalImage = NSImage.load(inLauncherBundle: "siderbar_open_btn_normal")!
            let hoverImage = NSImage.load(inLauncherBundle: "siderbar_open_btn_hover")!
            let pressedImage = NSImage.load(inLauncherBundle: "siderbar_open_btn_pressed")!
            let disabledImage = NSImage.load(inLauncherBundle: "siderbar_open_btn_disabled")!
            expandButton.updateStateImage(normal: normalImage, hover: hoverImage, pressed: pressedImage, disabled: disabledImage)
        } else {
            let normalImage = NSImage.load(inLauncherBundle: "siderbar_close_btn_normal")!
            let hoverImage = NSImage.load(inLauncherBundle: "siderbar_close_btn_hover")!
            let pressedImage = NSImage.load(inLauncherBundle: "siderbar_close_btn_pressed")!
            let disabledImage = NSImage.load(inLauncherBundle: "siderbar_close_btn_disabled")!
            expandButton.updateStateImage(normal: normalImage, hover: hoverImage, pressed: pressedImage, disabled: disabledImage)
        }
    }
}

// MARK: - 视频播放相关
extension LauncherMainViewController {
    func playVideo() {
        
        guard let myPlayerView = playerView else {
            return
        }
        
        guard let videoURL = Bundle.launcherMainBundle().url(forResource: "backgroundvideo", withExtension: "mp4") else {
            return
        }
        
        let playerItem = AVPlayerItem(url: videoURL)
        let player = AVPlayer(playerItem: playerItem)
        
        myPlayerView.player = player
        myPlayerView.player?.isMuted = toolbarView.latestCacheSoundOffState
        
        // 监听 timeControlStatus
        myPlayerView.player?.addObserver(self, forKeyPath: "timeControlStatus", options: [.new, .initial], context: nil)
        
        // 添加通知以在播放完成时循环播放
        NotificationCenter.default.addObserver(forName: .AVPlayerItemDidPlayToEndTime,
                                               object: player.currentItem,
                                               queue: nil) { notification in
            DispatchQueue.main.async {
                player.seek(to: CMTime.zero)
                player.play()
            }
        }
        
        if !LauncherSettingsManager.shared.playBgVideo {
            playerView?.isHidden = true
            placeholderBgImageView?.image = NSImage.load(inLauncherBundle: "mainview_placeholder_bg_bm")
            toolbarView.changeButtonIsHidden(ofType: .sound, isHidden: true)
        } else {
            // 启动视频播放
            player.play()
        }
    }
    
    func pauseVideo() {
        if !LauncherSettingsManager.shared.playBgVideo {
            return
        }
        
        playerView?.player?.pause()
    }
    
    func resumeVideo() {
        if !LauncherSettingsManager.shared.playBgVideo {
            return
        }
        
        if patcherManager.haveRunningApplication() {
            return
        }
        playerView?.isHidden = false
        playerView?.player?.play()
    }
    
    func playerVideoSwitchDidChange() {
        if !LauncherSettingsManager.shared.playBgVideo {
            placeholderBgImageView?.image = NSImage.load(inLauncherBundle: "mainview_placeholder_bg_bm")
            placeholderBgImageView?.isHidden = false
            playerView?.isHidden = true
            playerView?.player?.pause()
            toolbarView.changeButtonIsHidden(ofType: .sound, isHidden: true)
        } else {
            playerView?.isHidden = false
            if patcherManager.haveRunningApplication() {
                playerView?.player?.pause()
            } else {
                playerView?.player?.play()
            }
            
            placeholderBgImageView?.isHidden = true
            toolbarView.changeButtonIsHidden(ofType: .sound, isHidden: false)
        }
    }
    
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "timeControlStatus" {
            if playerView?.player?.timeControlStatus == .playing {
                playerView?.isHidden = false
                placeholderBgImageView?.isHidden = true
                
                playerView?.player?.removeObserver(self, forKeyPath: "timeControlStatus")
            }
        }
    }
}

// MARK: - 绑定patcher下载进度
extension LauncherMainViewController {
    private func bindPatcherManager() {
        cancellable = patcherManager.statePublisher.sink { [weak self] (state, versionInfo, downloadInfo, errCode) in
            DispatchQueue.main.async {
                self?.updateUI(for: state, versionInfo: versionInfo, downloadInfo: downloadInfo, errCode: errCode)
            }
        }
        
        patcherManager.selectedAppDidChage = { [weak self] appConfig in
            if let webUrl = appConfig?.launcherWebPart, !webUrl.isEmpty {
                if let status = self?.webviewExpandStatus, status == true {
                    let expandUrl = webUrl.appendingQueryParameters(["expand":"1"])
                    self?.loadWebView(pageUrl: expandUrl)
                } else {
                    self?.loadWebView(pageUrl: webUrl)
                }
            } else {
                self?.loadWebView(pageUrl: nil)
            }
        }
    }
    
    private func updateUI(for state: LauncherAppStatus, versionInfo: LauncherAppBranchVersionInfo?, downloadInfo: LauncherAppDownloadInfo?, errCode: Int?) {
        var downloading = false
        var progressInfo : LauncherAppDownloadInfo?
        var appInfo : LauncherAppBranchVersionInfo?
        var latestState = state
        
        switch state {
        case .downloading,.resProcessing:
            downloading = true
            progressInfo = downloadInfo
        case .pauseDownloading:
            downloading = true
            progressInfo = downloadInfo
        case .startGame:
            resumeVideo()
        case .gameInProgress:
            pauseVideo()
        case .checkFail,.downloadingFail:
            showRetryCheckResAlertView(errCode)
        default: break
        }
        if let branchVersionInfo = versionInfo {
            appInfo = branchVersionInfo
        }
        
        if downloading {
            showDownloadPanelView()
            hideAppStatusPannelView()
            
            if let info = progressInfo {
                downloadView?.updateProgress(info, status: state)
                // 还原操作按钮点击状态
                launcherPanel?.setStatus(.operationDone)
            }
        } else {
            showAppStatusPannelView()
            hideDownloadPanelView()
            if state == .cancelDownloading {
                downloadView = nil
                var installingStatus = LauncherAppStatus.startDownloading
                if let appInfo = appInfo, !appInfo.localVersion.isEmpty {
                    installingStatus = .needUpdating
                }
                latestState = installingStatus
            }
            
            if let info = appInfo {
                launcherPanel?.setVersion(appName: info.appVersionName ?? "",  currentVer: info.localVersion, latestVer: info.remoteVersion)
                var hadMoreVersions = false
                if let moreVersion = info.avaliableVersions, moreVersion.isEmpty == false {
                    hadMoreVersions = true
                }
                let downloading = patcherManager.haveDownloadingTask()
                launcherPanel?.didShowSitchButton(didShow: hadMoreVersions, enable: downloading == false)
            }
            launcherPanel?.setStatus(latestState)
        }
    }
    
    // 显示重新检查提示框
    func showRetryCheckResAlertView(_ errorCode: Int?) {
        var errCode = LauncherErrorCode.unknown.rawValue
        if let tempCode = errorCode  {
            errCode = tempCode
        }
        let errorMessage = String(format: LauncherLocalization.localizedString(forKey: "network_error"), errCode)
        let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "blank_tips"), attributedMessage: LauncherCommonAlertView.defaultAttributed(message: errorMessage, alignment: .center))
        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "retry"), handler: {[weak self] action in
            self?.launcherPanel?.retryStatusButtonClicked()
        }))
        alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "cancel"), handler: { action in
        }))
        alertView.show(inView: nil)
    }
}

// MARK: - 游戏状态、下载进度面板相关
extension LauncherMainViewController {
    func showAppStatusPannelView() {
        guard launcherPanel == nil else {
            launcherPanel?.isHidden = false
            return
        }
        
        let tempLauncherPanel = LauncherAppStatusPanel()
        tempLauncherPanel.wantsLayer = true
        tempLauncherPanel.layer?.backgroundColor = NSColor.clear.cgColor
        mainView.addSubview(tempLauncherPanel)
        launcherPanel = tempLauncherPanel
        
        tempLauncherPanel.my_mas_makeConstraints { make in
            make?.size.mas_equalTo()(NSSize(width: 320, height: 90))
            make?.bottom.offset()(-20)
            make?.right.offset()(-40)
        }
        // 设置回调
        tempLauncherPanel.statusButtonCallback = {[weak self] status in
            self?.collapsePageCallJS()
            self?.patcherManager.statusPanelOperateAction(status: status)
        }
        
        tempLauncherPanel.switchVersionButtonCallback = { [weak self] in
            self?.collapsePageCallJS()
            self?.showVersionSelectorView()
        }
    }
    
    func hideAppStatusPannelView() {
        launcherPanel?.isHidden = true
    }
    
    func showDownloadPanelView() {
        guard downloadView == nil else {
            downloadView?.isHidden = false
            return
        }
        
        let newDownloadView = LauncherAppDownloadPanel()
        newDownloadView.wantsLayer = true
        newDownloadView.layer?.backgroundColor = NSColor.init(white: 0.0, alpha: 0.2).cgColor
        mainView.addSubview(newDownloadView)
        
        newDownloadView.my_mas_makeConstraints { make in
            make?.size.mas_equalTo()(NSSize(width: 320, height: 200))
            make?.bottom.offset()(-20)
            make?.right.offset()(-40)
        }
        
        // 设置事件回调
        newDownloadView.actionCallback = { [weak self] actionType in
            switch actionType {
            case .pause:
                self?.patcherManager.pauseDownload()
            case .resume:
                self?.patcherManager.continueDownload()
            case .cancel:
                self?.patcherManager.cancelDownload()
            default:
                break
            }
        }
        downloadView = newDownloadView
    }
    
    func hideDownloadPanelView() {
        downloadView?.isHidden = true
    }
    
    func showVersionSelectorView() {
        let selectedIndex = LauncherPatcherManager.shared.currentSelectedAppIndex
        LauncherPatcherManager.shared.siderBarItemAppOperateAction(type: .changeAppVersion, index: selectedIndex)
    }
}

// MARK: - 网页相关
extension LauncherMainViewController {
    func mainWebviewDidOpen(isOpen: Bool) {
        webviewExpandStatus = isOpen
        if isOpen {
            if let tempPanelView = downloadView {
                if let tempPlayerView = playerView {
                    mainView.bringSubview(targetView: tempPanelView, above: tempPlayerView)
                } else if let tempBgImageView = placeholderBgImageView {
                    mainView.bringSubview(targetView: tempPanelView, above: tempBgImageView)
                }
            }
        } else {
            if let tempPanelView = downloadView {
                mainView.bringSubviewToFront(tempPanelView)
            }
        }
    }
    
    func didReceiveJSMessage(jsData: Any?) {
        if let dictionary = jsData as? [String: Any] {
            // 现在可以安全地使用 dictionary 作为 [String: Any]
            log.debug("[\(LogModule.base)] receive js data: \(dictionary)")

            if let funcName = dictionary["funcname"] as? String,
                let data = dictionary["data"] as? [String: Any] {
                if funcName == "pageCollapsedChanged" {
                    let collapsed = data["collapsed"] as! Int
                    if collapsed == 1 {
                        // 网页收回
                        mainWebviewDidOpen(isOpen: false)
                    } else {
                        // 网页弹出
                        mainWebviewDidOpen(isOpen: true)
                    }
                } else if funcName == "openNativeWebview" {
                    if let urlString =  data["url"] as? String {
                        let webViewCtr = LauncherFullWebviewController(webUrl: urlString)
                        webViewCtr.showViewController()
                    }
                } else if funcName == "repairGame" {
                    let _ = LauncherSettingsManager.resRepairAction()
                } else if funcName == "showSetting" {
                    self.showSettingsViewCtr()
                }
            }
        }
    }
    
    // 收回网页
    func collapsePageCallJS() {
        if webviewExpandStatus {
            let requestData: [String: Any] = [
                "type": "collapsePage",
                "address": [:]
            ]
            webviewBridge.callHandler("nativeToJs", data: requestData)
        }
    }
}

// 处理在不同分辨率屏幕间拖拽后，主窗口大小自适应
extension LauncherMainViewController {
    private func handleMainWindowScreenChangeNotification(_ notification: Notification) {
        guard let window = view.window else { return }
        
        mainView.frame = NSRect(x: 0, y: 0, width: LaunchUIConstant.MainWindowWidth, height: LaunchUIConstant.MainWindowHeight)
        mainWebview.frame = mainView.bounds
        var frame = NSRect(x: window.frame.origin.x, y: window.frame.origin.y, width: LaunchUIConstant.MainWindowWidth, height: LaunchUIConstant.MainWindowHeight + LaunchUIConstant.WindowTitlebarHegight)
        if let sidebarVC = sidebarViewController {
            sidebarVC.view.frame = NSRect(x: mainView.frame.width, y: 0, width: LaunchUIConstant.SidebarWidth, height: LaunchUIConstant.MainWindowHeight)
            frame.size.width += LaunchUIConstant.SidebarWidth
        }
        
        window.setFrame(frame, display: true, animate: false)
        window.center()
    }
}

extension LauncherMainViewController : WKUIDelegate,WKNavigationDelegate {
    func webView(_ webView: WKWebView, createWebViewWith configuration: WKWebViewConfiguration, for navigationAction: WKNavigationAction, windowFeatures: WKWindowFeatures) -> WKWebView? {
        // 解决点击的标签带有 target='_blank' 时，导致WKWebView无法跳转加载的问题
        if let url = navigationAction.request.url {
            LauncherSettingsManager.openExternalBrowser(url: url.absoluteString)
        }
        return nil
    }
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        
    }
}
