//
//  LauncherSettingsViewController.swift
//  WMLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/27.
//

import Foundation
import WMMasonry


class LauncherSettingsViewController: NSViewController {
    
    // 左侧一级菜单
    private var categoryTableView: NSTableView!
    private var categories: [String] = [LauncherLocalization.localizedString(forKey: "common_settings"), LauncherLocalization.localizedString(forKey: "feedback")]

    // 右侧二级菜单
    private var subMenuTableView: NSTableView!
    private var subMenuScrollView: NSScrollView!
    private var subMenuTypeData: [[LauncherSettingsType]] = [
        [.startup, .update, .speedLimit, .route, .resRepair, .version, .about],
        [.feedback]
    ]

    
    private let confirmButton = LauncherCustomButton()
    private var settingConfigData:[LauncherSettingsType: [CheckBoxButtonConfig]] = [:]
    private var originalSelectedData:[CheckBoxButtonConfig] = [] // 原始各设置项的选中状态集合，没个设置项保存一个代表子项
    
    private var currentCategoryIndex: Int = 0 {
        didSet {
            if oldValue != currentCategoryIndex {
                updateTableViewForCurrentCategory()
            }
        }
    }
    
    override func loadView() {
        let contentRect = NSRect(x: 0, y: 0, width: 832, height: 556)
        view = NSView(frame: contentRect)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.init(white: 0.0, alpha: 0).cgColor
        
        let backgroundImageView = NSImageView()
        backgroundImageView.image = NSImage.load(inLauncherBundle: "setting_bg")
        backgroundImageView.imageScaling = .scaleAxesIndependently
        view.addSubview(backgroundImageView)
        backgroundImageView.my_mas_makeConstraints { make in
            make?.edges.equalTo()(view)
        }
        
        loadSettingsSelectedStatus()
        setupSettingsView()
    }
    
    private func loadSettingsSelectedStatus() {
        let settingManager = LauncherSettingsManager.shared
        // 获取启动设置项
        let playBgVideo = settingManager.playBgVideo
        let startupConfig = [
            CheckBoxButtonConfig(type: .playBgVideo, isSelected: playBgVideo)]
        settingConfigData[.startup] = startupConfig
        originalSelectedData.append(contentsOf:startupConfig)
    
        // 获取启动设置项
        let gameAutoUpdate = settingManager.gameAutoUpdate
        let updateConfig = [
            CheckBoxButtonConfig(type: .noAutoUpdate, isSelected: !gameAutoUpdate),
            CheckBoxButtonConfig(type: .autoUpdate, isSelected: gameAutoUpdate)
        ]
        settingConfigData[.update] = updateConfig
        originalSelectedData.append(updateConfig.first!)
        
        // 下载速度设置
        let downloadSpeedLimit = settingManager.downloadSpeedLimit
        var limitedSpeed = false
        var limitedSpeedValue = ""
        if let speedLimit = downloadSpeedLimit, let limitValue = Int(speedLimit), (100...40960).contains(limitValue) {
            limitedSpeed = true
            limitedSpeedValue = String(limitValue)
        }
        let speedConfig = [
            CheckBoxButtonConfig(type: .unlimitedSpeed, isSelected: !limitedSpeed),
            CheckBoxButtonConfig(type: .limitedSpeed, isSelected: limitedSpeed, vaule: limitedSpeedValue)
        ]
        settingConfigData[.speedLimit] = speedConfig
        originalSelectedData.append(speedConfig.last!)
        
        // 下载线路选择
        let downloadRoute = settingManager.downloadRoute
        let resUrls = settingManager.getDownloadResUrls()
        let resRouteCount = resUrls.count
        var routeSelectdIndex = 0
        var routeConfig : [CheckBoxButtonConfig] = [CheckBoxButtonConfig(type: .defaultLine, isSelected: downloadRoute == nil)]
        if resRouteCount > 1 {
            for (index, value) in resUrls.enumerated() {
                let indexString = String(index + 1)
                let isSelected = downloadRoute == indexString
                if isSelected {
                    routeSelectdIndex = index + 1
                }
                routeConfig.append(CheckBoxButtonConfig(type: .otherLine, isSelected: isSelected, vaule: value, extend: indexString))
            }
        }
        if let downloadRouteValue = downloadRoute, downloadRouteValue == String(routeSelectdIndex) {
            // 本地缓存的route值在配置文件中找到了,不需要处理
        } else if downloadRoute != nil {
            //本地缓存的route值在配置文件中找不到，可能配置文件变了，还原默认值
            settingManager.downloadRoute = nil
            routeConfig[0].isSelected = true
        }
        settingConfigData[.route] = routeConfig
        originalSelectedData.append(routeConfig[routeSelectdIndex])
    }
    
    private func setupSettingsView() {
        // 添加标题
        let titleLabel = NSTextField(labelWithString: LauncherLocalization.localizedString(forKey: "settings"))
        titleLabel.font = NSFont.systemFont(ofSize: 22, weight: .regular)
        titleLabel.textColor = NSColor.wmxkit_color(withHexString: "#EDDDBE")
        view.addSubview(titleLabel)
        titleLabel.my_mas_makeConstraints { make in
            make?.top.offset()(24)
            make?.centerX.equalTo()(view)?.multipliedBy()(1.25)
        }
        
        // 设置左侧菜单
        let tableWidth = 200.0
        categoryTableView = NSTableView(frame: CGRectZero)
        // 修改tbaleviewcell两端不顶头，有缩进问题,需要先设置
        if #available(macOS 11.0, *)  {
            categoryTableView.style = .plain;
        }
        categoryTableView.intercellSpacing = CGSizeMake(0, 0);
        let tableColumn = NSTableColumn(identifier: NSUserInterfaceItemIdentifier("categoryColumn"))
        tableColumn.width = tableWidth
        categoryTableView.addTableColumn(tableColumn)
        categoryTableView.delegate = self
        categoryTableView.dataSource = self
        categoryTableView.selectionHighlightStyle = .none
        categoryTableView.wantsLayer = true
        categoryTableView.backgroundColor = NSColor.clear
        categoryTableView.layer?.backgroundColor = NSColor.clear.cgColor
        categoryTableView.headerView?.frame = CGRectZero
        categoryTableView.focusRingType = .none
       
        
        view.addSubview(categoryTableView)
        categoryTableView.my_mas_makeConstraints { make in
            make?.top.offset()(80)
            make?.bottom.offset()(0)
            make?.left.offset()(0)
            make?.width.mas_equalTo()(tableWidth)
        }
        // 设置右侧菜单
        subMenuTableView = NSTableView(frame: CGRectZero)
        subMenuTableView.addTableColumn(NSTableColumn(identifier: NSUserInterfaceItemIdentifier("subMenuTableCellView")))
        subMenuTableView.delegate = self
        subMenuTableView.dataSource = self
        subMenuTableView.selectionHighlightStyle = .none
        subMenuTableView.wantsLayer = true
        subMenuTableView.backgroundColor = NSColor.clear
        subMenuTableView.layer?.backgroundColor = NSColor.clear.cgColor
        subMenuTableView.headerView?.frame = CGRectZero
        
        subMenuScrollView = NSScrollView(frame: CGRectZero)
        subMenuScrollView.documentView = subMenuTableView
        subMenuScrollView.hasVerticalScroller = true
        subMenuScrollView.contentView.postsBoundsChangedNotifications = true
        NotificationCenter.default.addObserver(self, selector: #selector(didScrollSubMenu), name: NSView.boundsDidChangeNotification, object: subMenuScrollView.contentView)
        view.addSubview(subMenuScrollView)
        subMenuScrollView.drawsBackground = false
        subMenuScrollView.my_mas_makeConstraints { make in
            make?.top.equalTo()(categoryTableView)
            make?.left.equalTo()(categoryTableView.my_mas_right)
            make?.right.offset()(-31)
            make?.bottom.offset()(-116)
        }
        
        // 添加分割线
        let dividingLine = NSImageView()
        dividingLine.image = NSImage.load(inLauncherBundle: "setting_dividing_line")
        view.addSubview(dividingLine)
        dividingLine.my_mas_makeConstraints { make in
            make?.top.equalTo()(subMenuScrollView.my_mas_bottom)
            make?.right.offset()(-37)
            make?.size.mas_equalTo()(CGSize(width: 559, height: 1))
        }
        
        // 添加关闭按钮
        let normalCloseImage = NSImage.load(inLauncherBundle: "common_close")!
        let closeButton = LauncherCustomButton()
        closeButton.updateStateImage(normal: normalCloseImage, hover: nil, pressed: nil, disabled: nil)
        closeButton.isBordered = false
        closeButton.imageScaling = .scaleProportionallyUpOrDown
        closeButton.target = self
        closeButton.action = #selector(dismissSettingsView)
        view.addSubview(closeButton)
        closeButton.my_mas_makeConstraints { make in
            make?.top.offset()(16)
            make?.right.offset()(-20)
            make?.size.mas_equalTo()(CGSize(width: 34, height: 36))
        }
        
        
        let normalImage = NSImage.load(inLauncherBundle: "setting_btn_normal_img")!
        let normalColor = NSColor.wmxkit_color(withHexString: "#161615")
        
        // 添加确定按钮
        confirmButton.updateStateImage(normal: normalImage)
        confirmButton.updateTitleColor(normal: normalColor)
        confirmButton.font = NSFont.systemFont(ofSize: 22)
        confirmButton.title = LauncherLocalization.localizedString(forKey: "confirm")
        confirmButton.target = self
        confirmButton.isEnabled = false
        confirmButton.action = #selector(confirmButtonAction)
        view.addSubview(confirmButton)
        confirmButton.my_mas_makeConstraints { make in
            make?.bottom.offset()(-25)
            make?.size.mas_equalTo()(CGSizeMake(161, 56))
            make?.right.equalTo()(subMenuScrollView.my_mas_right)
        }
        
        // 添加取消按钮
        let cancelButton = LauncherCustomButton()
        cancelButton.updateStateImage(normal: normalImage)
        cancelButton.updateTitleColor(normal: normalColor)
        cancelButton.font = NSFont.systemFont(ofSize: 22)
        cancelButton.title = LauncherLocalization.localizedString(forKey: "cancel")
        cancelButton.target = self
        cancelButton.action = #selector(dismissSettingsView)
        view.addSubview(cancelButton)
        cancelButton.my_mas_makeConstraints { make in
            make?.bottom.equalTo()(confirmButton.my_mas_bottom)
            make?.size.mas_equalTo()(CGSizeMake(181, 56))
            make?.right.equalTo()(confirmButton.my_mas_left)?.offset()(-30)
        }
    }
    
    @objc func dismissSettingsView() {
        let configDidChange = confirmButton.isEnabled
        if configDidChange {
            let alertView = LauncherCommonAlertView.setup(title: LauncherLocalization.localizedString(forKey: "blank_tips"), message:LauncherLocalization.localizedString(forKey: "settings_config_change"))
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "yes"), handler: {[weak self] action in
                self?.confirmButtonAction()
            }))
            alertView.add(action: LauncherAlertAction.actionWithTitle(LauncherLocalization.localizedString(forKey: "no"), handler: { [weak self] action in
                let _ = self?.popViewController()
            }))
            alertView.show(inView: nil)
        } else {
            let _ = popViewController()
        }
    }
    
    @objc func confirmButtonAction() {
        let settingManager = LauncherSettingsManager.shared
        let allCheckBoxConfigs = settingConfigData.values.flatMap { $0 }
        var speedDidChanged = false
        var routeDidChanged = false
        var needAutoUpdate = false
        
        for originalConfig in originalSelectedData {
            var newItemConfig : CheckBoxButtonConfig!
            if originalConfig.type == .otherLine {
                newItemConfig = allCheckBoxConfigs.first { $0.type == originalConfig.type && $0.extend == originalConfig.extend}
            } else {
                newItemConfig = allCheckBoxConfigs.first { $0.type == originalConfig.type }
            }
            var isEqualValue = newItemConfig!.isSelected == originalConfig.isSelected
            if originalConfig.type == .limitedSpeed && isEqualValue == true {
                isEqualValue = newItemConfig!.vaule == originalConfig.vaule
            }
            
            // !!!!! 值有修改了
            if isEqualValue == false {
                if originalConfig.type == .playBgVideo {
                    // 启动设置项
                    settingManager.playBgVideo = newItemConfig!.isSelected
                } else if originalConfig.type == .noAutoUpdate {
                    // 更新设置
                    settingManager.gameAutoUpdate = !newItemConfig!.isSelected
                    if settingManager.gameAutoUpdate {
                        needAutoUpdate = true
                    }
                } else if originalConfig.type == .limitedSpeed {
                    // 限速设置
                    if newItemConfig!.isSelected {
                        var newItemValue = newItemConfig?.vaule
                        if let number = Int(newItemConfig?.vaule ?? ""), number < 100 {
                            newItemValue = "100"
                        }
                        settingManager.downloadSpeedLimit = newItemValue
                    } else {
                        settingManager.downloadSpeedLimit = nil
                    }
                    speedDidChanged = true
                } else if [CheckBoxButtonType.defaultLine, .otherLine].contains(originalConfig.type){
                    // 下载路线选择
                    if let routeConfig = settingConfigData[.route] {
                        if let selecedIndex = routeConfig.firstIndex(where: { $0.isSelected == true }) {
                            if selecedIndex == 0 {
                                settingManager.downloadRoute = nil
                            } else {
                                settingManager.downloadRoute = String(selecedIndex)
                            }
                        } else {
                            settingManager.downloadRoute = nil
                        }
                    }
                    routeDidChanged = true
                }
            }
        }
        // 更新下载路线和限速设置
        LauncherPatcherManager.shared.setPatcherDownload(speed: speedDidChanged, cdn: routeDidChanged)
        if needAutoUpdate {
            LauncherPatcherManager.shared.startUpdateAfterChangeSetting(true)
        }
        
        let _ = popViewController()
    }
    
    private func updateTableViewForCurrentCategory() {
        categoryTableView.reloadData()
    }
    
    // 判断设置项是否有变化
    private func configSelectedDidChange() -> Bool {
        let allCheckBoxConfigs = settingConfigData.values.flatMap { $0 }
        for originalConfig in originalSelectedData {
            var itemConfig : CheckBoxButtonConfig!
            if originalConfig.type == .otherLine {
                itemConfig = allCheckBoxConfigs.first { $0.type == originalConfig.type && $0.extend == originalConfig.extend}
            } else {
                itemConfig = allCheckBoxConfigs.first { $0.type == originalConfig.type }
            }
            
            var isEqualValue = itemConfig!.isSelected == originalConfig.isSelected
            if isEqualValue == false {
                return true
            }
            
            if originalConfig.type == .limitedSpeed {
                isEqualValue = originalConfig.vaule == itemConfig?.vaule
                if isEqualValue == false {
                    return true
                }
            }
        }
        
        return false
    }
    
    // 更新确定按钮enable状态
    private func checkConfirButtonEnable() {
        let configDidChange = configSelectedDidChange()
        confirmButton.isEnabled = configDidChange
    }
}

extension LauncherSettingsViewController: NSTableViewDelegate, NSTableViewDataSource {
    @objc private func didScrollSubMenu(notification: Notification) {
        
        let visibleRows = self.subMenuTableView.visibleRows
        guard let firstVisibleRow = visibleRows.first else { return }
        
        if firstVisibleRow >= subMenuTypeData[0].count {
            currentCategoryIndex = 1
        } else {
            currentCategoryIndex = 0
        }
    }
    
    // MARK: - NSTableViewDelegate / NSTableViewDataSource
    func numberOfRows(in tableView: NSTableView) -> Int {
        if tableView == categoryTableView {
            return categories.count
        } else if tableView == subMenuTableView {
            return subMenuTypeData.flatMap { $0 }.count
        }
        return 0
    }
    
    func tableView(_ tableView: NSTableView, viewFor tableColumn: NSTableColumn?, row: Int) -> NSView? {
        if tableView == categoryTableView {
            var cellView = tableView.makeView(withIdentifier: NSUserInterfaceItemIdentifier("SettingsCategoryTableCellView"), owner: self)
            if cellView == nil {
                cellView = SettingsCategoryTableCellView()
                cellView?.identifier = NSUserInterfaceItemIdentifier("SettingsCategoryTableCellView")
                
            }
            if let settingsCell = cellView as? SettingsCategoryTableCellView {
                settingsCell.updateCell(title: categories[row], isSelected: row == currentCategoryIndex)
            }
            return cellView
        } else if tableView == subMenuTableView {
            let allSubMenus = subMenuTypeData.flatMap { $0 }
            let actionType = allSubMenus[row]
            
            if [LauncherSettingsType.startup, .update, .speedLimit, .route].contains(actionType) {
                // 使用自定义单元格
                var cellView = tableView.makeView(withIdentifier: NSUserInterfaceItemIdentifier("SettingsCheckBoxTableCellView"), owner: self)
                if cellView == nil {
                    cellView = SettingsCheckBoxTableCellView()
                    cellView?.identifier = NSUserInterfaceItemIdentifier("SettingsCheckBoxTableCellView")
                }
                
                if let settingsCell = cellView as? SettingsCheckBoxTableCellView {
                    let itemConfig = settingConfigData[actionType]
                    settingsCell.configure(with: itemConfig!, actionType:actionType, allowsMultipleSelection: actionType == .startup, isVertical: actionType == .route)
                    settingsCell.selectionChangedHandler = { [weak self] newItemConfig in
                        self?.settingConfigData[actionType] = newItemConfig
                        self?.checkConfirButtonEnable()
                    }
                }
                return cellView
            } else {
                // 使用自定义单元格
                var cellView = tableView.makeView(withIdentifier: NSUserInterfaceItemIdentifier("SettingsSubMenuTableCellView"), owner: self)
                if cellView == nil {
                    cellView = SettingsSubMenuTableCellView()
                    cellView?.identifier = NSUserInterfaceItemIdentifier("SettingsSubMenuTableCellView")
                    
                }
                
                if let settingsCell = cellView as? SettingsSubMenuTableCellView {
                    settingsCell.configure(with: actionType) {[weak self] selectedAction in
                        switch selectedAction {
                        case .cleanCache:
                            LauncherSettingsManager.cleanAppCacheAction()
                            break
                        case .resRepair:
                            let canRespair = LauncherSettingsManager.resRepairAction()
                            if canRespair {
                                self?.dismissSettingsView()
                            }
                            break
                        case .version:
                            LauncherSettingsManager.checkForUpdates()
                            break
                        case .about:
                            LauncherSettingsManager.openUserAgreementTermsAction()
                            break
                        case .feedback:
                            LauncherSettingsManager.openFeedbackAction()
                            break
                        default:
                            break
                        }
                    }
                }
                return cellView
            }
        }
        return nil
    }
    
    func tableView(_ tableView: NSTableView, heightOfRow row: Int) -> CGFloat {
        if tableView == categoryTableView {
            return 60
        } else {
            if row == subMenuTypeData.flatMap({ $0 }).count-1 {
                return 350.0
            } else {
                let allSubMenus = subMenuTypeData.flatMap { $0 }
                let actionType = allSubMenus[row]
                if actionType == .route {
                    let itemConfig = settingConfigData[actionType]
                    return CGFloat(itemConfig!.count) * 50.0 + 50.0
                } else {
                    return 100.0
                }
            }
        }
    }
    
    
    func tableViewSelectionDidChange(_ notification: Notification) {
        if notification.object as? NSTableView == categoryTableView {
            let selectedIndex = categoryTableView.selectedRow
            if selectedIndex >= 0 && selectedIndex < categories.count {
                currentCategoryIndex = selectedIndex
                subMenuTableView.scrollRowToVisible(selectedIndex * subMenuTypeData[0].count)
            }
        }
    }
}


extension NSTableView {
    var visibleRows: [Int] {
        let visibleRect = self.visibleRect
        let rows = self.rows(in: visibleRect)
        return Array(rows.lowerBound..<rows.upperBound)
    }
}
