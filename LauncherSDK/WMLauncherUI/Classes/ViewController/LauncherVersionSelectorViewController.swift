//
//  LauncherVersionSelectorViewController.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/13.
//

import Foundation
import WMMasonry
class LauncherVersionSelectorViewController: NSViewController {

    var onVersionSelected: ((String) -> Void)?
    // 左侧一级菜单
    private var categoryTableView: NSTableView!
    private var categories: [String] = [LauncherLocalization.localizedString(forKey: "game_version"), LauncherLocalization.localizedString(forKey: "game_upgrade_setting"), LauncherLocalization.localizedString(forKey: "game_app_selected_name")]
    // 右侧子菜单
    private var collectionView: NSCollectionView?
    private var versions: [String] = []
    private var upgradeTypes: [String] = [LauncherLocalization.localizedString(forKey: "automatic_update"),LauncherLocalization.localizedString(forKey: "manual_update")]
    private var appClientNames: [String] = []
    
    private var versionSelectedValue: String?
    private var upgradeSelectedIndex: Int = 0
    private var clientSelectedIndex: Int = 0
    private let confirmButton =  LauncherCustomButton()
    private let cancelButton = LauncherCustomButton()
    private var currentCategoryIndex: Int = 0 {
        didSet {
            if oldValue != currentCategoryIndex {
                categoryTableView.reloadData()
            }
        }
    }
    
    override func loadView() {
        let contentRect = NSRect(x: 0, y: 0, width: LaunchUIConstant.MainPopWindowWidth, height: LaunchUIConstant.MainPopWindowHeight)
        view = NSView(frame: contentRect)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.init(white: 0.0, alpha: 0).cgColor
        
        versionSelectedValue = LauncherSettingsManager.shared.selectedAppVersion
        upgradeSelectedIndex = LauncherSettingsManager.shared.autoUpdateClient ? 0 : 1
        clientSelectedIndex = LauncherSettingsManager.shared.targetClientEnv == .test ? 2 : (LauncherSettingsManager.shared.targetClientEnv == .dev ? 1 : 0)
        
        let backgroundImageView = NSImageView()
        backgroundImageView.image = NSImage.load(inLauncherBundle: "version_selector_bg")
        backgroundImageView.imageScaling = .scaleAxesIndependently
        view.addSubview(backgroundImageView)
        backgroundImageView.my_mas_makeConstraints { make in
            make?.edges.equalTo()(view)
        }
        
        
        versions = LauncherPatcherManager.shared.currentAppAvailableVersions
        versions.insert("-1", at: 0)
        
        let appConfig = LauncherPatcherManager.shared.multiAppConfig(index: nil)
        if let shippingApp = appConfig?.client {
            var shippingName = shippingApp
            let shippingAppArray = shippingApp.components(separatedBy: "/")
            if shippingAppArray.count > 1 {
                shippingName = shippingAppArray.last!
            }
            appClientNames.append("main-Shipping  (\(shippingName))")
        }
        
        if let devApp = appConfig?.clientDev {
            var devName = devApp
            let devAppArray = devApp.components(separatedBy: "/")
            if devAppArray.count > 1 {
                devName = devAppArray.last!
            }
            appClientNames.append("main-Dev  (\(devName))")
        }
        
        if let testApp = appConfig?.clientTest {
            var testName = testApp
            let testAppArray = testApp.components(separatedBy: "/")
            if testAppArray.count > 1 {
                testName = testAppArray.last!
            }
            appClientNames.append("main-Test  (\(testName))")
        }
        
        setupLeftMenuView()
        setupCollectionView()
        let confirmButton = LauncherCustomButton()
        let normalImage = NSImage.load(inLauncherBundle: "alert_btn_normal_img")!
        let normalColor = NSColor.wmxkit_color(withHexString: "#3C3E40")
        confirmButton.updateStateImage(normal: normalImage)
        confirmButton.updateTitleColor(normal: normalColor)
        confirmButton.font = NSFont.systemFont(ofSize: 18)
        confirmButton.title = LauncherLocalization.localizedString(forKey: "confirm")
        confirmButton.target = self
        confirmButton.action = #selector(confirmSelection)
        view.addSubview(confirmButton)
        confirmButton.my_mas_makeConstraints { make in
            make?.bottom.offset()(-20)
            make?.size.mas_equalTo()(CGSizeMake(175, 49))
            make?.right.offset()(-40)
        }
        
        let normalCloseImage = NSImage.load(inLauncherBundle: "alert_close_btn_light_img")!
        cancelButton.updateStateImage(normal: normalCloseImage, hover: nil, pressed: nil, disabled: nil)
        cancelButton.isBordered = false
        cancelButton.imageScaling = .scaleProportionallyUpOrDown
        cancelButton.target = self
        cancelButton.action = #selector(cancelSelection)
        view.addSubview(cancelButton)
        cancelButton.my_mas_makeConstraints { make in
            make?.top.offset()(16)
            make?.right.offset()(-16)
            make?.size.mas_equalTo()(CGSize(width: 28, height: 28))
        }
        
        categoryTableView.reloadData()
        collectionView?.reloadData()
    }

    private func setupLeftMenuView() {
        // 设置左侧菜单
        let tableWidth = 180.0
        categoryTableView = NSTableView(frame: CGRectZero)
        // 修改tbaleviewcell两端不顶头，有缩进问题,需要先设置
        if #available(macOS 11.0, *)  {
            categoryTableView.style = .plain;
        }
        categoryTableView.intercellSpacing = CGSizeMake(0, 0);
        let tableColumn = NSTableColumn(identifier: NSUserInterfaceItemIdentifier("categoryColumn"))
        tableColumn.width = tableWidth
        categoryTableView.addTableColumn(tableColumn)
        categoryTableView.delegate = self
        categoryTableView.dataSource = self
        categoryTableView.selectionHighlightStyle = .none
        categoryTableView.wantsLayer = true
        categoryTableView.backgroundColor = NSColor.clear
        categoryTableView.layer?.backgroundColor = NSColor.clear.cgColor
        categoryTableView.headerView?.frame = CGRectZero
        categoryTableView.focusRingType = .none
        categoryTableView.intercellSpacing = CGSizeMake(0, 0);
        view.addSubview(categoryTableView)
        categoryTableView.my_mas_makeConstraints { make in
            make?.top.offset()(80)
            make?.bottom.offset()(0)
            make?.left.offset()(0)
            make?.width.mas_equalTo()(tableWidth)
        }
    }
    
    private func setupCollectionView() {
        let layout = LeftAlignedCollectionViewLayout()
        layout.sectionInset = NSEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
        
        collectionView = NSCollectionView()
        collectionView!.collectionViewLayout = layout
        collectionView!.delegate = self
        collectionView!.dataSource = self
        collectionView!.isSelectable = true
        collectionView!.backgroundColors = [.clear]
        collectionView!.wantsLayer = true
        collectionView!.allowsEmptySelection = false
        collectionView!.layer?.backgroundColor = NSColor.clear.cgColor
        collectionView!.register(LauncherVersionItem.self, forItemWithIdentifier: NSUserInterfaceItemIdentifier("LauncherVersionFirstItem"))
        collectionView!.register(LauncherVersionItem.self, forItemWithIdentifier: NSUserInterfaceItemIdentifier("LauncherVersionItem"))
        collectionView!.register(LauncherVersionItem.self, forItemWithIdentifier: NSUserInterfaceItemIdentifier("LauncherUpgradeItem"))
        collectionView!.register(LauncherVersionItem.self, forItemWithIdentifier: NSUserInterfaceItemIdentifier("LauncherClientItem"))
        // 注册 FooterView 类型
        collectionView!.register(NSView.self, forSupplementaryViewOfKind: NSCollectionView.elementKindSectionFooter, withIdentifier: NSUserInterfaceItemIdentifier("FooterView"))
        
        let scrollView = NSScrollView()
        scrollView.documentView = collectionView
        scrollView.drawsBackground = false
        scrollView.wantsLayer = true
        scrollView.backgroundColor = NSColor.clear
        scrollView.layer?.backgroundColor = NSColor.clear.cgColor
        NotificationCenter.default.addObserver(self, selector: #selector(collectionDidScrollNotification), name: NSView.boundsDidChangeNotification, object: scrollView.contentView)
        view.addSubview(scrollView)
        scrollView.my_mas_makeConstraints { make in
            make?.top.equalTo()(categoryTableView)
            make?.left.equalTo()(categoryTableView.my_mas_right)
            make?.bottom.offset()(-100)
            make?.right.offset()(-40)
        }
    }
    
    @objc func confirmSelection() {
        if let newVersionValue = versionSelectedValue {
            LauncherSettingsManager.shared.selectedAppVersion = newVersionValue
        } else {
            if let collectionItem = collectionView!.item(at: IndexPath(item: 0, section: 0 )) as? LauncherVersionItem {
                if collectionItem.versionLabel.stringValue.isEmpty {
                    LauncherSettingsManager.shared.selectedAppVersion = nil
                } else {
                    LauncherSettingsManager.shared.selectedAppVersion = collectionItem.versionLabel.stringValue
                }
            }
        }
        
        LauncherSettingsManager.shared.autoUpdateClient = upgradeSelectedIndex == 0
        LauncherSettingsManager.shared.targetClientEnv = clientSelectedIndex == 2 ? .test : (clientSelectedIndex == 1 ? .dev : .shipping)
        
        onVersionSelected?(versionSelectedValue ?? "")
        let _ = popViewController()
    }
    
    @objc func cancelSelection() {
        let _ = popViewController()
    }
    
    override func keyUp(with event: NSEvent) {
        if event.keyCode == 0x24 { //回车
            confirmSelection()
        }
        super.keyUp(with: event)
    }
}

extension LauncherVersionSelectorViewController: NSCollectionViewDataSource, NSCollectionViewDelegate, NSCollectionViewDelegateFlowLayout {
    @objc private func collectionDidScrollNotification(notification: Notification) {
        let visibleRect = collectionView!.visibleRect
        guard let attributesArray = collectionView!.collectionViewLayout?.layoutAttributesForElements(in: visibleRect) else {
            return
        }
        
        // 过滤出 item 类型的布局属性
        let itemAttributes = attributesArray.filter { $0.representedElementCategory == .item }
        guard !itemAttributes.isEmpty else { return }
        
        // 按 minY 升序排序，第一个即为最顶部的 item
        let sortedItems = itemAttributes.sorted { $0.frame.minY < $1.frame.minY }
        if let topItem = sortedItems.first {
            currentCategoryIndex = topItem.indexPath!.section
        }
    }
    
    func numberOfSections(in collectionView: NSCollectionView) -> Int {
        return 3
    }
    
    func collectionView(_ collectionView: NSCollectionView, numberOfItemsInSection section: Int) -> Int {
        if section == 0 {
            return versions.count
        } else if section == 1 {
            return upgradeTypes.count
        } else if section == 2 {
            return appClientNames.count
        }
        return 0
    }
    
    func collectionView(_ collectionView: NSCollectionView, itemForRepresentedObjectAt indexPath: IndexPath) -> NSCollectionViewItem {
        
        if indexPath.section == 0 {
            if indexPath.item == 0 {
                let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("LauncherVersionFirstItem"), for: indexPath) as! LauncherVersionItem
                item.configVersion("", selected: versionSelectedValue == nil, eitable: true)
                item.inputHandler = { [weak self] in
                    if !collectionView.selectionIndexPaths.isEmpty {
                        //对collectionView.selectionIndexPaths进行过滤，提取IndexPath.section == 0的IndexPath
                        let selectedIndexPaths = collectionView.selectionIndexPaths.filter({ $0.section == 0 })
                        collectionView.deselectItems(at: selectedIndexPaths)
                    }
                    if self?.versionSelectedValue != nil {
                        self?.versionSelectedValue = nil
                        collectionView.reloadData()
                    }
                }
                return item
            }
            
            let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("LauncherVersionItem"), for: indexPath) as! LauncherVersionItem
            let currentVer = versions[indexPath.item]
            item.configVersion(currentVer, selected: currentVer == versionSelectedValue, eitable: false)
            return item
            
        } else if indexPath.section == 1 {
            let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("LauncherUpgradeItem"), for: indexPath) as! LauncherVersionItem
            let currentVer = upgradeTypes[indexPath.item]
            item.configVersion(currentVer, selected: upgradeSelectedIndex == indexPath.item, eitable: false)
            return item
            
        } else if indexPath.section == 2 {
            let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("LauncherClientItem"), for: indexPath) as! LauncherVersionItem
            let currentVer = appClientNames[indexPath.item]
            item.configVersion(currentVer, selected: clientSelectedIndex == indexPath.item, eitable: false)
            return item
        }
        return NSCollectionViewItem()
    }
    
    // 动态设置每个 item 的大小
    func collectionView(_ collectionView: NSCollectionView, layout collectionViewLayout: NSCollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> NSSize {
        if indexPath.section == 0 {
            if (indexPath.item == 0) {
                return CGSizeMake(collectionView.frame.size.width, 50);
            }
            return NSSize(width: (collectionView.frame.size.width-6.0)/2.0, height: 50)
        } else if indexPath.section == 1 {
            return NSSize(width: (collectionView.frame.size.width-6.0)/2.0, height: 50)
        } else {
            return CGSizeMake(collectionView.frame.size.width, 50);
        }
    }
    
    // 设置每行之间的最小间距
    func collectionView(_ collectionView: NSCollectionView, layout collectionViewLayout: NSCollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 10.0
    }
    
    // 设置每个 item 之间的最小间距
    func collectionView(_ collectionView: NSCollectionView, layout collectionViewLayout: NSCollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0.0
    }
    
    func collectionView(_ collectionView: NSCollectionView, didSelectItemsAt indexPaths: Set<IndexPath>) {
        for index in indexPaths {
            if index.section == 0 {
                let currentVer = versions[index.item]
                versionSelectedValue = currentVer
            } else if index.section == 1 {
                upgradeSelectedIndex = index.item
            } else if index.section == 2 {
                clientSelectedIndex = index.item
            }
        }
        collectionView.reloadData()
    }
    
    // 返回 Footer view
    func collectionView(_ collectionView: NSCollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> NSView {
        if kind == NSCollectionView.elementKindSectionFooter {
            let footerView = collectionView.makeSupplementaryView(ofKind: kind, withIdentifier: NSUserInterfaceItemIdentifier("FooterView"), for: indexPath)
            footerView.wantsLayer = true
            footerView.layer?.backgroundColor = NSColor.clear.cgColor
            return footerView
        }
        
        return NSView()
    }
    // 返回 Footer view 高度
    func collectionView(_ collectionView: NSCollectionView, layout collectionViewLayout: NSCollectionViewLayout, referenceSizeForFooterInSection section: Int) -> NSSize {
        // 最后一个为了滑动效果好点，返回50
        return NSSize(width: collectionView.frame.width, height: section == 2 ? 60 : 30)
    }
}

extension LauncherVersionSelectorViewController: NSTableViewDelegate, NSTableViewDataSource {
    // MARK: - NSTableViewDelegate / NSTableViewDataSource
    func numberOfRows(in tableView: NSTableView) -> Int {
        return categories.count
    }
    
    func tableView(_ tableView: NSTableView, viewFor tableColumn: NSTableColumn?, row: Int) -> NSView? {
        var cellView = tableView.makeView(withIdentifier: NSUserInterfaceItemIdentifier("SettingsCategoryTableCellView"), owner: self)
        if cellView == nil {
            cellView = SettingsCategoryTableCellView()
            cellView?.identifier = NSUserInterfaceItemIdentifier("SettingsCategoryTableCellView")
            
        }
        if let settingsCell = cellView as? SettingsCategoryTableCellView {
            settingsCell.updateCell(title: categories[row], isSelected: row == currentCategoryIndex)
        }
        return cellView
    }
    
    func tableView(_ tableView: NSTableView, heightOfRow row: Int) -> CGFloat {
        return 60.0
    }
    
    
    func tableViewSelectionDidChange(_ notification: Notification) {
        if notification.object as? NSTableView == categoryTableView {
            let index = categoryTableView.selectedRow
            if index >= 0 && index < categories.count {
                currentCategoryIndex = index
                collectionView?.scrollToItems(at: [IndexPath(item: 0, section: currentCategoryIndex)], scrollPosition: .top)
            }
        }
    }
}

class LeftAlignedCollectionViewLayout: NSCollectionViewFlowLayout {
    override func layoutAttributesForElements(in rect: NSRect) -> [NSCollectionViewLayoutAttributes] {
        let attributes = super.layoutAttributesForElements(in: rect)
        for (index, attr) in attributes.enumerated() {
            if index%2 == 1, attr.indexPath?.section == 0 {
                // 确保版本号列表，左侧的都顶头，左对其
                attr.frame.origin.x = 0
            }
        }

        return attributes
    }
}
