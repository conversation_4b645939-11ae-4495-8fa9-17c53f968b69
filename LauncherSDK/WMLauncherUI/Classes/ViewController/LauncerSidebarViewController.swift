//
//  LauncerSidebarView.swift
//  WMMacLauncherUI
//
//  Created by z<PERSON>ji<PERSON> on 2024/11/5.
//

import Foundation
import Cocoa
import WMMasonry
import WMXAppKit
import Combine

class SidebarCollectionViewItem: NSCollectionViewItem {
    private var trackingArea: NSTrackingArea?
    var leftClickCallback: ((SidebarCollectionViewItem,NSEvent) -> Void)?
    var rightClickCallback: ((SidebarCollectionViewItem,NSEvent) -> Void)?
    private var downloadIndexCancellable: AnyCancellable?
    private var downloadStatusCancellable: AnyCancellable?
    private var configItemIndex = -1
    private var downloadingIndex = -1
    private var operateType: PatcherDownloadStatus = .idle
    private var currentIsInstalled = false
    
    let iconImageView: NSImageView = {
        let imageView = NSImageView()
        imageView.wantsLayer = true
        imageView.layer?.cornerRadius = 12.0
        return imageView
    }()
    
    let titleLabel: NSTextField = {
        let label = NSTextField(labelWithString: "")
        label.font = NSFont.systemFont(ofSize: 14)
        label.lineBreakMode = .byWordWrapping
        label.alignment = .center
        label.maximumNumberOfLines = 2
        return label
    }()
    
    let selectedBorderImageView: NSImageView = {
        let imageView = NSImageView()
        imageView.image = NSImage.load(inLauncherBundle: "siderbar_selected_border")
        return imageView
    }()
    
    let selectedBgImageView: NSImageView = {
        let imageView = NSImageView()
        imageView.image = NSImage.load(inLauncherBundle: "siderbar_selected_bg")
        imageView.imageScaling = .scaleAxesIndependently
        return imageView
    }()
    
    let mouseEnteredImageView: NSImageView = {
        let imageView = NSImageView()
        imageView.image = NSImage.load(inLauncherBundle: "siderbar_selected_bg")
        imageView.imageScaling = .scaleAxesIndependently
        imageView.alphaValue = 0.8
        return imageView
    }()
    
    let maskingView: NSView = {
        let tempView = NSView()
        tempView.wantsLayer = true
        tempView.layer?.backgroundColor = NSColor.init(white: 0, alpha: 0.5).cgColor
        tempView.layer?.cornerRadius = 12.0
        return tempView
    }()
    
    
    let arrowAnimatedView: LauncherAnimatedArrowView = {
        let tempArrowView = LauncherAnimatedArrowView(frame: NSRect(x: 0, y: 0, width: 20, height: 20))
        return tempArrowView
    }()
    
    override func loadView() {
        view = NSView()
        setupView()
        
        // 订阅 downloadIndex 变化
        downloadIndexCancellable = LauncherPatcherManager.shared.$currentInstallingAppIndex.receive(on: DispatchQueue.main)
            .sink { [weak self] newIndex in
                self?.downloadingIndex = newIndex
                self?.updateDownloadStatus(index: newIndex, status: nil)
            }
        
        downloadStatusCancellable = LauncherPatcherManager.shared.$updateStatus.receive(on: DispatchQueue.main)
            .sink { [weak self] status in
                self?.operateType = status
                self?.updateDownloadStatus(index: nil, status: status)
            }
    }
    
    deinit {
        downloadIndexCancellable?.cancel() // 释放订阅，防止内存泄漏
        downloadStatusCancellable?.cancel() // 释放订阅，防止内存泄漏
    }
    
    private func setupView() {
        view.addSubview(mouseEnteredImageView)
        view.addSubview(selectedBgImageView)
        view.addSubview(selectedBorderImageView)
        view.addSubview(iconImageView)
        view.addSubview(titleLabel)
        view.addSubview(maskingView)
        view.addSubview(arrowAnimatedView)
        mouseEnteredImageView.isHidden = true
        selectedBorderImageView.isHidden = true
        selectedBgImageView.isHidden = true
        maskingView.isHidden = true
        arrowAnimatedView.isHidden = true
        
        mouseEnteredImageView.my_mas_makeConstraints { make in
            make?.edges.offset()(0)
        }
        
        selectedBgImageView.my_mas_makeConstraints { make in
            make?.edges.offset()(0)
        }
        
        selectedBorderImageView.my_mas_makeConstraints { make in
            make?.width.equalTo()(72)
            make?.height.equalTo()(73)
            make?.center.equalTo()(iconImageView)
        }
        
        
        iconImageView.my_mas_makeConstraints { make in
            make?.width.equalTo()(60)
            make?.height.equalTo()(60)
            make?.centerX.offset()(0)
            make?.top.offset()(13)
        }
        
        titleLabel.my_mas_makeConstraints { make in
            make?.left.offset()(0)
            make?.right.offset()(0)
            make?.top.equalTo()(iconImageView.my_mas_bottom)?.offset()(10)
        }
        
        maskingView.my_mas_makeConstraints { make in
            make?.edges.equalTo()(iconImageView)
        }
        
        arrowAnimatedView.my_mas_makeConstraints { make in
            make?.width.equalTo()(20)
            make?.height.equalTo()(20)
            make?.top.equalTo()(iconImageView)?.offset()(-2)
            make?.right.equalTo()(iconImageView)?.offset()(2)
        }
        // 添加鼠标滑动区域
        addTrackingArea()
    }

    func configure(icon: String, title: String, selected: Bool, configIndex: Int) {
        mouseEnteredImageView.isHidden = true
        configItemIndex = configIndex
        iconImageView.image = NSImage(named: icon)
        titleLabel.stringValue = title
        let installed = LauncherPatcherManager.shared.isInstalledApplication(index: configIndex)
        
        if selected {
            selectedBorderImageView.isHidden = false
            selectedBgImageView.isHidden = false
        } else {
            selectedBorderImageView.isHidden = true
            selectedBgImageView.isHidden = true
        }
        currentIsInstalled = installed
        updateDownloadStatus(index: downloadingIndex, status: operateType)
    }
    
    private func updateMaskViewHighlight(_ mouseEntered: Bool = false) {
        if mouseEntered || currentIsInstalled || (downloadingIndex >= 0 && downloadingIndex == configItemIndex) {
            titleLabel.textColor = NSColor.wmxkit_color(withHexString: "#FEFEFE")
            maskingView.isHidden = true
        } else {
            titleLabel.textColor = NSColor.wmxkit_color(withHexString: "#FEFEFE", alpha: 0.5)
            maskingView.isHidden = false
        }
    }
    
    private func updateDownloadStatus(index: Int?,status: PatcherDownloadStatus?) {
        var targetIndex = downloadingIndex
        var targetStatus = operateType
        
        if let tempIndex = index {
            targetIndex = tempIndex
        }
        
        if let tempStatus = status {
            targetStatus = tempStatus
        }
        
        if configItemIndex >= 0 && targetIndex >= 0 && targetIndex == configItemIndex {
            arrowAnimatedView.isHidden = false
            
            if targetStatus == .doing  {
                arrowAnimatedView.startAnimation()
            } else {
                arrowAnimatedView.stopAnimation()
            }
        } else {
            arrowAnimatedView.isHidden = true
            arrowAnimatedView.stopAnimation()
        }
        updateMaskViewHighlight()
    }
    
    private func addTrackingArea() {
        if let trackingArea = self.trackingArea {
            self.view.removeTrackingArea(trackingArea)
        }
        
        let options: NSTrackingArea.Options = [.mouseEnteredAndExited, .activeInKeyWindow, .inVisibleRect]
        let newTrackingArea = NSTrackingArea(rect: .zero, options: options, owner: self, userInfo: nil)
        self.view.addTrackingArea(newTrackingArea)
        self.trackingArea = newTrackingArea
    }
    
    override func mouseEntered(with event: NSEvent) {
        mouseEnteredImageView.isHidden = false
        updateMaskViewHighlight(true)
    }
    
    override func mouseExited(with event: NSEvent) {
        mouseEnteredImageView.isHidden = true
        updateMaskViewHighlight(false)
    }
    
    override func mouseDown(with event: NSEvent) {
        leftClickCallback?(self,event)
    }
    
    override func rightMouseDown(with event: NSEvent) {
        rightClickCallback?(self,event)
    }
}

class SidebarHitCollectionView: NSCollectionView {
    public var viewDidClickAction: (() -> Void)?
    override func mouseDown(with event: NSEvent) {
        viewDidClickAction?()
    }
    
    override func rightMouseDown(with event: NSEvent) {
        viewDidClickAction?()
    }
}

class SidebarViewController: NSViewController {

    private var collectionView: SidebarHitCollectionView?
    private let patcherManager = LauncherPatcherManager.shared
    private var sidebarMenu: LauncherCustomMenu?
    public weak var mainView: NSView?
    private let scrollView = NSScrollView()
    private var headerReferenceHeight = 32.0
    override func loadView() {
        let contentRect = NSRect(x: 0, y: 0, width: LaunchUIConstant.SidebarWidth, height: LaunchUIConstant.MainWindowHeight)
        view = NSView(frame: contentRect)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        let backgroundImageView = NSImageView()
        backgroundImageView.image = NSImage.load(inLauncherBundle: "sidebar_bg")
        backgroundImageView.imageScaling = .scaleAxesIndependently
        view.addSubview(backgroundImageView)
        backgroundImageView.my_mas_makeConstraints { make in
            make?.edges.equalTo()(view)
        }
        
        setupCollectionView()
        addSelectedAppObserver()
    }
    
    public var selectedRow : Int = 0 {
        didSet {
            collectionView?.reloadData()
        }
    }
    
    private func setupCollectionView() {
        
        let layout = NSCollectionViewFlowLayout()
        layout.sectionHeadersPinToVisibleBounds = true
        layout.headerReferenceSize = NSSize(width: 10, height: headerReferenceHeight) //顶部预留空白
        
        collectionView = SidebarHitCollectionView()
        collectionView!.collectionViewLayout = layout
        collectionView!.delegate = self
        collectionView!.dataSource = self
        collectionView!.isSelectable = true
        collectionView!.allowsMultipleSelection = false
        collectionView!.backgroundColors = [.clear]
        collectionView!.wantsLayer = true
        collectionView!.allowsEmptySelection = false
        collectionView!.layer?.backgroundColor = NSColor.clear.cgColor
        collectionView!.register(SidebarCollectionViewItem.self, forItemWithIdentifier: NSUserInterfaceItemIdentifier("SidebarCollectionViewItem"))
        collectionView?.viewDidClickAction = { [weak self] in 
            self?.closeContextMenu()
        }
        
        
        scrollView.documentView = collectionView
        scrollView.drawsBackground = false
        scrollView.wantsLayer = true
        scrollView.backgroundColor = NSColor.clear
        scrollView.layer?.backgroundColor = NSColor.clear.cgColor
        scrollView.hasVerticalScroller = false
        if #available(macOS 11.0, *) {
            scrollView.automaticallyAdjustsContentInsets = false
        }
        scrollView.contentInsets = NSEdgeInsetsZero
        scrollView.documentView?.autoresizingMask = [.width, .height]
        
        view.addSubview(scrollView)
        scrollView.my_mas_makeConstraints { make in
            make?.top.offset()(0)
            make?.bottom.offset()(0)
            make?.left.offset()(4)
            make?.right.offset()(-4)
        }
    }
    
    public func addSelectedAppObserver() {
        selectedRow = patcherManager.latestSelectAppIndex
        patcherManager.selectedAppStatusDidChage = { [weak self] index in
            if let chageIndex = index {
                self?.selectedRow = chageIndex
            } else {
                self?.collectionView?.reloadData()
            }
        }
    }

   func handleLeftClick(_ indexPath: IndexPath) {
        if indexPath.item != self.selectedRow {
            menuItemDidClicked(type: .switchApp, index: indexPath.item)
        }
        closeContextMenu()
    }

    func handleRightClick(_ indexPath: IndexPath, item: SidebarCollectionViewItem) {
        showContextMenu(for: item,index: indexPath.item)
    }
}


extension SidebarViewController: NSCollectionViewDataSource {
    func numberOfSections(in collectionView: NSCollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: NSCollectionView, numberOfItemsInSection section: Int) -> Int {
        return LaunchUIConstant.LauncherConfig!.config.multiConfig!.count
    }
    
    func collectionView(_ collectionView: NSCollectionView, itemForRepresentedObjectAt indexPath: IndexPath) -> NSCollectionViewItem {
        let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("SidebarCollectionViewItem"), for: indexPath) as! SidebarCollectionViewItem
        
        let itemConfig = LaunchUIConstant.LauncherConfig!.config.multiConfig![indexPath.item]
        item.configure(icon: itemConfig.iconPath, title: itemConfig.versionName, selected: selectedRow == indexPath.item, configIndex: indexPath.item)
        
        // 为每个 item 设置左右键点击回调
        item.leftClickCallback = { [weak self] clickedItem,event in
            self?.handleLeftClick(indexPath)
        }

        item.rightClickCallback = { [weak self] clickedItem,event in
            self?.handleRightClick(indexPath, item: clickedItem)
        }
        return item
    }
}

extension SidebarViewController: NSCollectionViewDelegateFlowLayout {
    
    func collectionView(_ collectionView: NSCollectionView, layout collectionViewLayout: NSCollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> NSSize {
        return NSSize(width: collectionView.frame.width, height: 120)
    }
    
    func collectionView(_ collectionView: NSCollectionView, layout collectionViewLayout: NSCollectionViewLayout, insetForSectionAt section: Int) -> NSEdgeInsets {
        return NSEdgeInsets(top: 0, left: 0, bottom: 0, right: 0)
    }
    
    func collectionView(_ collectionView: NSCollectionView, layout collectionViewLayout: NSCollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
    
    func collectionView(_ collectionView: NSCollectionView, layout collectionViewLayout: NSCollectionViewLayout, minimumInteritemSpacingForSectionAt section: Int) -> CGFloat {
        return 0
    }
}

//extension SidebarViewController: NSCollectionViewDelegate {
//    func collectionView(_ collectionView: NSCollectionView, didSelectItemsAt indexPaths: Set<IndexPath>) {
//
//    }
//    
//    func collectionView(_ collectionView: NSCollectionView, didDeselectItemsAt indexPaths: Set<IndexPath>) {
//    }
//}


extension SidebarViewController {
    private func showContextMenu(for item: SidebarCollectionViewItem, index: Int) {
        closeContextMenu()
        // 创建并显示新菜单
        let menubar = LauncherCustomMenu()
        for operateType in AppOperateType.allCases {
            if operateType.rawValue > AppOperateType.resRepair.rawValue {
                continue
            }
            let installed = LauncherPatcherManager.shared.isInstalledApplication(index: index)
            
            if operateType == .switchApp && index == selectedRow {
                continue
            }
            
            if operateType == .removeApp && !installed {
                // 所选的和未安装的都跳过，不能删除
                continue
            }
            
            if operateType == .resRepair && (index != selectedRow || !installed) {
                // 非选中的和未安装的，不显示一键修复
                continue
            }
            
            menubar.addItem(type: operateType) { [weak self]  selectedType in
                self?.menuItemDidClicked(type: selectedType, index: index)
            }
        }
        
        self.sidebarMenu = menubar

        let centerOffsetY = (item.view.frame.height - menubar.viewHeight) / 2.0
        let topOffset = item.view.frame.origin.y + centerOffsetY
        menubar.show(inView: mainView, topOffset: topOffset)
    }
    
    private func menuItemDidClicked(type: AppOperateType, index: Int) {
        closeContextMenu()
        patcherManager.siderBarItemAppOperateAction(type: type, index: index)
    }
    
    private func closeContextMenu() {
        sidebarMenu?.dismiss()
    }
}
