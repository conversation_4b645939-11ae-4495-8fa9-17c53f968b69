//
//  LauncherFeedbackViewController.swift
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/18.
//

import Cocoa
import WMMasonry
import WMXAppKit
import WMMBProgressHUD

class IgnoreRightEventTextView: NSTextView {
    override func rightMouseDown(with event: NSEvent) {
        // 什么都不做，完全屏蔽右键
    }
}

class FeedbackViewController: NSViewController, NSCollectionViewDelegate, NSCollectionViewDataSource {
    
    private let totalCount = 300
    private let fileCount = 3
    private let attachmentTotalSize = 1024 * 1024 * 200

    private let titleTextField = NSTextField(labelWithString: "")
    
    private let feedbackTextView = IgnoreRightEventTextView()
    private let textScrollView = NSScrollView()
    
    private let feedbackTipsField = NSTextField()
    private let feedbackBottomTipsField = NSTextField()
    
    private let countLabel = NSTextField()
    private let collectionView: NSCollectionView = NSCollectionView()
//    private let scrollView = NSScrollView()
    
    private let submitButton = LauncherCustomButton()
    private var errorCountTipView: LauncherTipView?
    
    private var attachmentSize = 0
    
    private lazy var attachmentURLs: [LauncherAttachment] = {
        var attachment = [LauncherAttachment]()
        let addAttachment = LauncherAttachment(type: .add,enable: true)
        attachment.append(addAttachment)
        return attachment
    }()
    
    override func loadView() {
        let contentRect = NSRect(x: 0, y: 0,  width: 752, height: 476)
        view = NSView(frame: contentRect)
    }
    
    deinit {
        LauncherSettingsManager.shared.isOpenFeedbackView = false
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        setupLayout()
    }
    
    private func setupUI() {
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.init(white: 0.0, alpha: 0).cgColor
        
        let backgroundImageView = NSImageView()
        backgroundImageView.image = NSImage.load(inLauncherBundle: "feedback_bg_img")
        backgroundImageView.imageScaling = .scaleAxesIndependently
        view.addSubview(backgroundImageView)
        backgroundImageView.my_mas_makeConstraints { make in
            make?.edges.equalTo()(view)
        }
        
        titleTextField.textColor = NSColor.wmxkit_color(withHexString: "#EDDDBE")
        titleTextField.font = NSFont.systemFont(ofSize: 22)
        titleTextField.stringValue = LauncherLocalization.localizedString(forKey: "feedbackDesc")
        
        // 添加关闭按钮
        let normalCloseImage = NSImage.load(inLauncherBundle: "common_close")!
        let closeButton = LauncherCustomButton()
        closeButton.updateStateImage(normal: normalCloseImage, hover: nil, pressed: nil, disabled: nil)
        closeButton.isBordered = false
        closeButton.imageScaling = .scaleProportionallyUpOrDown
        closeButton.target = self
        closeButton.action = #selector(dismissFeedbackView)
        view.addSubview(closeButton)
        closeButton.my_mas_makeConstraints { make in
            make?.top.offset()(16)
            make?.right.offset()(-20)
            make?.size.mas_equalTo()(CGSize(width: 34, height: 36))
        }
        
        // 文本输入框设置
        feedbackTextView.isEditable = true
        feedbackTextView.isSelectable = true
        feedbackTextView.isRichText = false
        feedbackTextView.menu = nil
        feedbackTextView.font = NSFont.systemFont(ofSize: 20)
        feedbackTextView.backgroundColor = NSColor.black.withAlphaComponent(0.3)
        feedbackTextView.textColor = NSColor.white
        feedbackTextView.delegate = self
        feedbackTextView.autoresizingMask = [.width] // 宽度自动调整
        feedbackTextView.textContainerInset = NSSize(width: 8, height: 8) // 四边缩进
        feedbackTextView.textContainer?.widthTracksTextView = true
        feedbackTextView.textContainer?.lineBreakMode = .byWordWrapping // 自动换行
        feedbackTextView.becomeFirstResponder()
        feedbackTextView.smartInsertDeleteEnabled = true
        
        textScrollView.scrollerStyle = .overlay
        textScrollView.hasVerticalScroller = true
        textScrollView.drawsBackground = false
        textScrollView.backgroundColor = NSColor.clear
        textScrollView.documentView = feedbackTextView
        textScrollView.contentView.postsBoundsChangedNotifications = true
        
        feedbackTipsField.wantsLayer = true
        feedbackTipsField.drawsBackground = false
        feedbackTipsField.backgroundColor = NSColor.clear
        feedbackTipsField.isEditable = false
        feedbackTipsField.isBordered = false
        let textColor = NSColor.wmxkit_color(withHexString: "#686868")!
        let tipsLabelStr = LauncherLocalization.localizedString(forKey: "attachment")
        let tipsContentStr = LauncherLocalization.localizedString(forKey: "feedback_tips")
   
        let attributedString = NSMutableAttributedString(string: "\(tipsLabelStr)\(tipsContentStr)")
        attributedString.addAttributes( [.font: NSFont.systemFont(ofSize: 16), .foregroundColor: textColor], range: NSRange(location: 0, length: tipsLabelStr.count))
        attributedString.addAttributes( [.font: NSFont.systemFont(ofSize: 16), .foregroundColor: textColor], range: NSRange(location: tipsLabelStr.count, length: tipsContentStr.count))
        feedbackTipsField.attributedStringValue = attributedString
        
        countLabel.wantsLayer = true
        countLabel.drawsBackground = false
        countLabel.backgroundColor = NSColor.clear
        countLabel.isEditable = false
        countLabel.isBordered = false
        countLabel.textColor = textColor
        countLabel.stringValue = "0/300"
        
        // 设置 CollectionView
        let collectionViewLayout = NSCollectionViewFlowLayout()
        collectionViewLayout.itemSize = NSMakeSize(346, 39);
        collectionViewLayout.minimumInteritemSpacing = 14;
        collectionViewLayout.minimumLineSpacing = 10;
        
        collectionView.collectionViewLayout = collectionViewLayout
        collectionView.backgroundColors = [NSColor.clear]
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(LauncherAttachmentAddItem.self, forItemWithIdentifier: NSUserInterfaceItemIdentifier("LauncherAttachmentAddItem"))
        collectionView.register(LauncherAttachmentItem.self, forItemWithIdentifier: NSUserInterfaceItemIdentifier("LauncherAttachmentItem"))
        
//        scrollView.documentView = collectionView
//        scrollView.hasVerticalScroller = false
//        scrollView.scrollerStyle = .overlay
//        scrollView.drawsBackground = false
//        scrollView.backgroundColor = NSColor.clear
//        scrollView.contentView.automaticallyAdjustsContentInsets = false
//        scrollView.contentView.contentInsets = NSEdgeInsets(top: 8, left: 12, bottom: 24, right: 12)
        
        // 提交按钮
        let normalImage = NSImage.load(inLauncherBundle: "feedback_upload")!
        let normalColor = NSColor.wmxkit_color(withHexString: "#161615")
        submitButton.updateStateImage(normal: normalImage)
        submitButton.updateTitleColor(normal: normalColor)
        submitButton.isBordered = false
        submitButton.imageScaling = .scaleProportionallyUpOrDown
        submitButton.target = self
        submitButton.action = #selector(submitFeedback)
        submitButton.title = LauncherLocalization.localizedString(forKey: "submit_feedback")
        submitButton.font = NSFont.systemFont(ofSize: 20)
        
        feedbackBottomTipsField.wantsLayer = true
        feedbackBottomTipsField.drawsBackground = false
        feedbackBottomTipsField.backgroundColor = NSColor.clear
        feedbackBottomTipsField.isEditable = false
        feedbackBottomTipsField.isBordered = false
        feedbackBottomTipsField.textColor = NSColor.wmxkit_color(withHexString: "#717171")!
        feedbackBottomTipsField.font = NSFont.systemFont(ofSize: 14)
        feedbackBottomTipsField.stringValue = LauncherLocalization.localizedString(forKey: "feedback_bottom_tips")
        
        changeSubmitButton(enable: false)
        // 添加子视图
        view.addSubview(titleTextField)
        view.addSubview(textScrollView)
        view.addSubview(countLabel)
        view.addSubview(feedbackTipsField)
        view.addSubview(collectionView)
        view.addSubview(submitButton)
        view.addSubview(feedbackBottomTipsField)
    }
    
    private func setupLayout() {
        titleTextField.my_mas_makeConstraints { make in
            make?.top.offset()(23)
            make?.centerX.offset()(0)
        }
        
        textScrollView.my_mas_makeConstraints { make in
            make?.top.offset()(64)
            make?.left.offset()(20)
            make?.right.offset()(-20)
            make?.height.mas_equalTo()(151)
        }
        
        countLabel.my_mas_makeConstraints { make in
            make?.right.equalTo()(textScrollView.my_mas_right)?.offset()(-14)
            make?.bottom.equalTo()(textScrollView.my_mas_bottom)?.offset()(-9)
        }
        
        feedbackTipsField.my_mas_makeConstraints { make in
            make?.top.equalTo()(textScrollView.my_mas_bottom)?.offset()(12)
            make?.left.equalTo()(textScrollView)
            make?.right.equalTo()(textScrollView)
        }
        
        collectionView.my_mas_makeConstraints { make in
            make?.top.equalTo()(feedbackTipsField.my_mas_bottom)?.offset()(12)
            make?.left.equalTo()(textScrollView)
            make?.right.equalTo()(textScrollView)
            make?.height.equalTo()(90)
        }
        
        submitButton.my_mas_makeConstraints { make in
            make?.top.equalTo()(collectionView.my_mas_bottom)?.offset()(24)
            make?.centerX.offset()(0)
            make?.width.equalTo()(251)
            make?.height.equalTo()(56)
        }
        
        feedbackBottomTipsField.my_mas_makeConstraints { make in
            make?.top.equalTo()(submitButton.my_mas_bottom)?.offset()(16)
            make?.centerX.offset()(0)
        }
    }
    
    @objc private func submitFeedback() {
        if feedbackTextView.string.isEmpty {
            let _ = showToastMessage(message: LauncherLocalization.localizedString(forKey: "fill_feedback_question"))
            return
        }
        let progressHUD = showToastMessage(message: LauncherLocalization.localizedString(forKey: "file_uploading"), autoHide: false)
        LauncherPatcherManager.shared.sendFeedback(content: feedbackTextView.string, attachments: attachmentURLs) {[weak self] step, progress in
            var message = LauncherLocalization.localizedString(forKey: "file_uploading")
#if DEBUG
            // 为了测试查看进度
            switch step {
            case .zip:
                message = LauncherLocalization.localizedString(forKey: "file_compressing")
            case .upload:
                message = LauncherLocalization.localizedString(forKey: "file_uploading")
            case .confirm:
                message = LauncherLocalization.localizedString(forKey: "file_processing")
            @unknown default:
                break
            }
#endif
            self?.updateToastProgress(progress: progress, progressHUD: progressHUD, message: message)
        } completion: {[weak self]  result, error in
            self?.hideToastProgress(progressHUD)
            
            if let tempError = error {
                let _ = LauncherTipView.show(in: self?.view, type: .error, tip: LauncherLocalization.localizedString(forKey: "upload_failed_retry"))
                log.emergency("[\(LogModule.setting)] submit feedback Error: \(tempError)")
            } else {
                if let response = result as? [String: Any] {
                    let code = response["code"] as! Int
                    if code == 0 {
                        let traceid = response["uuid"] as! String
                        NSPasteboard.general.clearContents()
                        NSPasteboard.general.setString(traceid, forType: .string)
                        let _ = LauncherTipView.show(in: self?.view, type: .success, tip: String(format: LauncherLocalization.localizedString(forKey: "log_upload_code"), traceid))
                        DispatchQueue.main.asyncAfter(deadline: .now() + 5) { [weak self] in
                            self?.dismissFeedbackView()
                        }
                    } else {
                        let message = response["message"] as! String
                        let _ = LauncherTipView.show(in: self?.view, type: .error, tip: String(format: LauncherLocalization.localizedString(forKey: "submission_failed_retry"), message))
                    }
                }
            }
        }
    }
    
    // MARK: - NSCollectionViewDataSource
    func collectionView(_ collectionView: NSCollectionView, numberOfItemsInSection section: Int) -> Int {
        return attachmentURLs.count
    }
    
    func collectionView(_ collectionView: NSCollectionView, itemForRepresentedObjectAt indexPath: IndexPath) -> NSCollectionViewItem {
        let attachment = attachmentURLs[indexPath.item]
        if attachment.type == .add {
            let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("LauncherAttachmentAddItem"), for: indexPath) as! LauncherAttachmentAddItem
            item.enable = attachment.enable
            item.delegate = self
            return item
        } else {
            let item = collectionView.makeItem(withIdentifier: NSUserInterfaceItemIdentifier("LauncherAttachmentItem"), for: indexPath) as! LauncherAttachmentItem
            item.delegate = self
            item.configItemWithAttachment(attachment)
            return item
        }
    }
    
    @objc func dismissFeedbackView() {
        let _ = popViewController()
    }
}

extension FeedbackViewController:LauncherAttachmentAddItemDelegate,NSOpenSavePanelDelegate {
    func addAttachment() {
        let openPanel = NSOpenPanel()
        openPanel.delegate = self
        openPanel.allowsMultipleSelection = false
        openPanel.canChooseDirectories = false
        
        openPanel.beginSheetModal(for: self.view.window!) { [weak self] result in
            guard let self = self else { return }
            if result == .cancel {
                return
            }
            
            guard result == .OK, let url = openPanel.url, !url.absoluteString.isEmpty else {
                return
            }
            
            // 检查文件大小
            if !self.doesAttachmentSizeLimit(url) {
                let _ = showToastMessage(message: LauncherLocalization.localizedString(forKey: "upload_file_size_limit"))
                return
            }
            
            // 创建附件
            let attachment = LauncherAttachment(url: url)
            let fileExtension = url.pathExtension.lowercased()
            let imageTypes = ["png", "jpg", "jpeg", "bmp"]
            let videoTypes = ["mp4", "avi", "mov", "fv", "wmv", "mkv"]
            if imageTypes.contains(fileExtension) {
                attachment.type = .image
            } else if videoTypes.contains(fileExtension) {
                attachment.type = .video
            } else if fileExtension == "zip" {
                attachment.type = .zip
            }
            
            self.attachmentURLs.append(attachment)
            self.attachmentURLs.first?.enable = !(self.attachmentURLs.count == (self.fileCount + 1))
            self.collectionView.reloadData()
        }
    }
    
    // MARK: - NSOpenSavePanelDelegate
    func panel(_ sender: Any, shouldEnable url: URL) -> Bool {
        // 允许所有目录被点击
        let fileExtension = url.pathExtension.lowercased()
        var isDirectory: ObjCBool = false
        
        FileManager.default.fileExists(atPath: url.path, isDirectory: &isDirectory)
        if isDirectory.boolValue {
            // 获取 URL 资源属性，检查是否是 package（如 .app, .workspace 等）
            if let resourceValues = try? url.resourceValues(forKeys: [.isPackageKey]), resourceValues.isPackage == true {
                return false // 过滤掉 .app、.workspace 等包
            }
            return true
        }
        
        let allowed = Set(["png", "jpg", "jpeg", "bmp", "mp4", "avi", "mov", "fv", "wmv", "mkv"])
        return allowed.contains(fileExtension)
    }
    
    // 检查附件大小是否符合要求
    private func doesAttachmentSizeLimit(_ url: URL) -> Bool {
        do {
            let resourceValues = try url.resourceValues(forKeys: [.fileSizeKey])
            if let fileSize = resourceValues.fileSize {
                return fileSize <= attachmentTotalSize
            }
        } catch {
            log.debug("[\(LogModule.setting)] Error retrieving file size: \(error)")
        }
        return false
    }
}

extension FeedbackViewController:LauncherAttachmentItemDelegate {
    func deleteAttachment(_ attachment: LauncherAttachment!) {
        if let index = attachmentURLs.firstIndex(where: { $0.type == attachment.type && $0.url == attachment.url }) {
            attachmentURLs.remove(at: index)
            attachmentURLs.first?.enable = true
            collectionView.reloadData()
        }
    }
}

extension FeedbackViewController:NSTextViewDelegate {
    // MARK: - NSTextViewDelegate
    func textView(_ textView: NSTextView, shouldChangeTextIn range: NSRange, replacementString: String?) -> Bool {
        
        guard let replacementString = replacementString else { return true }
                
        let currentText = textView.string
        let newLength = currentText.count - range.length + replacementString.count
        
        let isValid =  (newLength <= 300)
        
        if isValid {
            updateWordCount(textView: textView)
        } else {
            if let errorTipView = errorCountTipView, errorTipView.superview != nil {
                // 已经显示了，不处理
            } else {
                errorCountTipView = nil
                errorCountTipView = LauncherTipView.show(in: view, type: .warning, tip: String(format: LauncherLocalization.localizedString(forKey: "word_limit")))
            }
            
        }
        return isValid
    }
    
    private func updateWordCount(textView: NSTextView) {
        DispatchQueue.main.async {
            let count = textView.string.count
            if count <= self.totalCount {
                self.changeSubmitButton(enable: count > 0)
                self.countLabel.stringValue = "\(count)/\(self.totalCount)"
                self.textScrollView.layer?.borderWidth = 0
            } else {
                let countString = "\(count)"
                let valueString = "\(count)/\(self.totalCount)"
                let attributedString = NSMutableAttributedString(string: valueString)
                attributedString.addAttributes([.foregroundColor: NSColor.red], range: (valueString as NSString).range(of: countString))
                self.countLabel.attributedStringValue = attributedString
                
                self.textScrollView.borderType = .grooveBorder
                self.textScrollView.layer?.borderWidth = 1.0
                self.textScrollView.layer?.borderColor = NSColor.wmxkit_color(withHexString: "#FF4B4B",alpha: 0.3).cgColor
            }
        }
    }
    
    private func changeSubmitButton(enable : Bool) {
        submitButton.isEnabled = enable
        if (enable) {
            submitButton.layer?.backgroundColor = NSColor.wmxkit_color(withHexString: "#E59117").cgColor;
        } else {
            submitButton.layer?.backgroundColor = NSColor.wmxkit_color(withHexString: "#767676").cgColor;
        }
    }
}

extension FeedbackViewController {
    func showToastMessage(message : String, autoHide : Bool = true) -> WMMBProgressHUD? {
        if let progressHUD = WMMBProgressHUD.init(view: view) {
            progressHUD.mode = .text
            progressHUD.labelText = message
            view.addSubview(progressHUD)
            progressHUD.show(true)
            if autoHide {
                progressHUD.hide(true, afterDelay: 3)
            }
            return progressHUD
        }
        return nil
    }
    
    func updateToastProgress(progress : Float, progressHUD : WMMBProgressHUD?, message : String? = nil) {
        if let progressHUD = progressHUD {
            progressHUD.mode = .determinate
            progressHUD.progress = progress
            if let labelText = message {
                progressHUD.labelText = labelText
            }
        }
    }
    
    func hideToastProgress(_ hud : WMMBProgressHUD?) {
        if let hud = hud {
            hud.removeFromSuperview()
        }
    }
}
