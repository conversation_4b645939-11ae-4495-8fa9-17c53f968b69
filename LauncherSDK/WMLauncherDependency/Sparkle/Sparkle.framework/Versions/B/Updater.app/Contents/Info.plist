<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BuildMachineOSBuild</key>
	<string>22G720</string>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleExecutable</key>
	<string>Updater</string>
	<key>CFBundleIdentifier</key>
	<string>org.sparkle-project.Sparkle.Updater</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>en</string>
		<string>ca</string>
		<string>ar</string>
		<string>cs</string>
		<string>da</string>
		<string>de</string>
		<string>el</string>
		<string>en</string>
		<string>es</string>
		<string>fa</string>
		<string>fi</string>
		<string>fr</string>
		<string>he</string>
		<string>hr</string>
		<string>hu</string>
		<string>is</string>
		<string>it</string>
		<string>ja</string>
		<string>ko</string>
		<string>nb</string>
		<string>nl</string>
		<string>pl</string>
		<string>pt-BR</string>
		<string>pt-PT</string>
		<string>ro</string>
		<string>ru</string>
		<string>sk</string>
		<string>sl</string>
		<string>sv</string>
		<string>th</string>
		<string>tr</string>
		<string>uk</string>
		<string>zh_CN</string>
		<string>zh_HK</string>
		<string>zh_TW</string>
	</array>
	<key>CFBundleName</key>
	<string>Updater</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>2.6.4</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleSupportedPlatforms</key>
	<array>
		<string>MacOSX</string>
	</array>
	<key>CFBundleVersion</key>
	<string>2039.1</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTPlatformBuild</key>
	<string></string>
	<key>DTPlatformName</key>
	<string>macosx</string>
	<key>DTPlatformVersion</key>
	<string>14.2</string>
	<key>DTSDKBuild</key>
	<string>23C53</string>
	<key>DTSDKName</key>
	<string>macosx14.2</string>
	<key>DTXcode</key>
	<string>1520</string>
	<key>DTXcodeBuild</key>
	<string>15C500b</string>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.utilities</string>
	<key>LSMinimumSystemVersion</key>
	<string>10.13</string>
	<key>LSUIElement</key>
	<string>1</string>
	<key>NSPrincipalClass</key>
	<string>NSApplication</string>
</dict>
</plist>
