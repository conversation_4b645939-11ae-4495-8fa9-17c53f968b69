<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Base.lproj/SUUpdateAlert.nib</key>
		<data>
		0rW1wTNcm/Uu4ix8yhISrWRKwKU=
		</data>
		<key>Resources/Base.lproj/SUUpdatePermissionPrompt.nib/keyedobjects-101300.nib</key>
		<data>
		0faCRWKAGe89/C2vGoilEmwgpjs=
		</data>
		<key>Resources/Base.lproj/SUUpdatePermissionPrompt.nib/keyedobjects-110000.nib</key>
		<data>
		gCq7ramhj8vmmJR4Ku5qE9S5Nhc=
		</data>
		<key>Resources/Base.lproj/Sparkle.strings</key>
		<data>
		yENpjnRY3Io5iY3w/nXBi2P2CFc=
		</data>
		<key>Resources/Info.plist</key>
		<data>
		8898FLXaAgWaPlgE1aZ3Sj7YpPg=
		</data>
		<key>Resources/ReleaseNotesColorStyle.css</key>
		<data>
		NjIvb1z7eJuLCKf9HS15O5heg50=
		</data>
		<key>Resources/SUStatus.nib</key>
		<data>
		wynGxRmaznvKG2L/LPvVOJEsppM=
		</data>
		<key>Resources/ar.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			qTOMJ1P/HhCcJQi4qSJV9l/b7q0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			zZ/0sjHdlPnBGe10CetKo1kF1xQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5Ukin0TnIF0ot6Daz8OSgIoDZJ0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			l9CaCmAXFcs+Z+8rRt7PX9onkf8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DL6k3g2A8CPQPkykQht4w+H/xYc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			G9Wgf14zMhU2alRSZvqclMmlTCA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rhsuTqRoVAfmLW+GJ1vvxJPRJ0U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KTLNyu97zLvTNgaUfYWqc8nB9C4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NEt5JVKz+OoMSynKxJC18KXMGaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			s6oFpgOPENk+LCyXJoLfVqZauVQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7MO7J38OUDrmZMLJiNSeNRATia8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YLQxXHDo3e3Udzaj8LHDIjotWzE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			W8+shbfn38JAPBpgHTMWuU0oHfQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FcFO4FvZjeiHQb9cbZI1wh1jHT4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BS+NpAFPK7X/XzX+n99gJLhlNKU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			TNa05IunzylN4fz2uHvkj5EnyRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6VadVc0qrgmUnWfL3FgiI6TzchM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			FSez7jCd0gDTFFGHiWL1QXY8OUU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7+SiSQLU1hqbN74YfiBS1cQFVqU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Q36SuanjGk70efU6liei3uz+Uds=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			kNfRs9Pgn30BdjtuNzhRvKXcqu0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bt9xytBAy/CZ0aLyzGAKrh1dVZc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IySTqO8MqmOO/IHR5WBZdf0jYaA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			My5YiAuNV+4oR1vPL1np+nMMMOI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NcjaY8nD4cpjcpK4M878R5JDK1s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			KRoZrUbgs7+EwIxs18H121Szw+0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ffz6ccHMgxcBdH6by1YAYX1jpOQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			p4tAU3Ek6hEWqW9e8+C1L8WMQIM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			BS6wdN+n1R2u/skiaNGAfrXwcKA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			nZXhvxaoacIflCBRrHxQ4NDkeKg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+bhO5LfEJtbZlq+wfPs/WUEO2ic=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			b/ru54Y0QwvH9Kz9sfRPEoP5z5k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rdv7bU5k1tUG/tyNsQ1i/Rniypk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			70rdfUc3cUNcMed6Hq4zQBWoGrk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			VD/QPXFfEHRW7ksDLYiiO1xl1LQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			PMarJZpNhDysjzZuBuyKv8KBTXQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5CCN2xKgiom6y3+mcWd48RVdX48=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			bQiB5tUCaD24QKubEYeBTXsAF1g=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vGNXtUX/4qNYIzE89IO7e4GxS60=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			tplLwN1kGq9MoWLnyPQhozI6c54=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Yev0Ro2PsLfgCLoY7JNED63PnqM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			IGMzQ4TCQgpEQaOcESzlhe8ny9I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HvJh7rlxinaVRX3rGu84YDTq5j8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			aqXsBwqycwXfSX0SDJcWfHOnzWA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			cAx3RQDMM17OoU39/UaI2sMDZLA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			XybbnN3dRODKY9v8li4+41QLCpc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Qw6/m/LeQ/b+ApIWLXJp2ByBp7k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ddXQhUAjY+oi62JXHPY2OYvbpa4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			30lZbzu4QerfbEwayFqoboTwLSs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			fck+vL9Sgcx19X7HthrjizRGhu8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			eiq9zVX/y56Q0ymxVNFnYahFbxs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Xm8t/g53ktlmyq8w1aI29nEiGO8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5ZpTsHPgV4inhhYiISGjC03BMG4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			LgvDZbmPK7Ox9+gNe7zXN3egxlM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HUFefTfqhwJx6nNNmACg3qxtHKo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v2c8gYdlWN2U2PbZlUQwmfWIeGk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xNizW6Xg8cPtYBhWBmexjNf5j8U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8GnPpOmTh7bQRiUCzZDL6pq1KNE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			HX2RXVrN+fpwO4I60/UDyNuGj5Y=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			abNyxpda7OkXoR5Ok35XgMr9eBc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			MXjhjMKrcFaSZhXYssMrBTXPu80=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YFXY6v+45ptf8TuBq2MsKKdhfQ8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			2iCpI0fy7Tm1zxR19dV1iCYW3bo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			mVP9x5C0h0Q+njDLXhZXmDsOjWM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			pWRHcAJRvjUt7BOLr/gd+IupcGA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Om//DOu8+gBjHYrCHVmxKxBDvPs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6IY8J7Jbjd3eG/BMld5iJSwZZvM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			a/RNqEdkehva+SwGWz11MktFGWA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JDMBsS6fp2v5X+C0d1EJAREHIkA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			rihcAKPJ1j7EoW5B+lq7Dpci/Zo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Lmn0e5MDPfan55gnani1dQbR10Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			z+XqvyZR2X6cb0PioKpfYDCF3YY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			ZtVq/Mf1qT9j8xdhz9ULfJ1O05k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			8o3l6mjHafwy5sLMMO2rZIe7xiQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			s3Cllq+eYT+urMLfXvnwsMkboWQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			gqdvwKoHMg+gDZ4MZVVqbV4yqI8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Ny5EoZGpd5UK5c3eMIUKLR8x4/I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			vbP9bj0jn5GKz9uEu3amXnozkWo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			hz8NwYxW1d0aWPQDMF8/c4lJRwU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			YWicg3ZZLCEoiJ9WOUUZ6WoTZJY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			JKbTlT+iKE5KOwvLD9N/Go2K+q0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			UH7udC5C4WHwnnx4Eg6Io23rBzk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Cd6guArNrSoJO3e2ntd1Eys3bok=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			L4ZWMKTKnMsbMsL8V2V6OLySKLE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			O9PZTdxbP4Y306ym/2sJ6p5klE0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			B+Y273q3UDMsG2/yaWuxMUGr2dI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			NstD9tjAfqULKbFJEcULPgVeJKk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DzlYjutWBP2dCq5D9RQaB5mCf94=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			6/WdcAg1mJs1/HT5krHhOxqyMWk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			I4B4qXPwnMhj/A+yU0vvngP7oak=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			3YLrl/aRzoPCubIKa873XDZeU1w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			DjCjxSor6wnKAz8bFLcPCnW1Kw0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			xeCj4c1ifxxhDFeLtNsSc4NgBFw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			Q18TnLdR6a0E72xXP6ETh0FF4CY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			+9prrb68fl57+m9WFQ+8Ay6XjRk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			5Pazf5ErH02Ny5mFB+R+dwCWPVM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			v2NQCc/vUJacBpqiLL5yANtiGc4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash</key>
			<data>
			167IbTfOhYu699bxXBhaGehjrco=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash</key>
			<data>
			SNJz+3Rb1AJ2cKstnbGWL6Q8OW8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/Sparkle.strings</key>
		<dict>
			<key>hash</key>
			<data>
			48nGyIkkDrsDKSq77pFReYsumCA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Autoupdate</key>
		<dict>
			<key>cdhash</key>
			<data>
			8SHNI2//iEq3D5kTas9tzb/l8Ok=
			</data>
			<key>requirement</key>
			<string>cdhash H"f121cd236fff884ab70f99136acf6dcdbfe5f0e9" or cdhash H"82f6861d22cb4b317c616706faea20f034a09868"</string>
		</dict>
		<key>Headers/SPUDownloadData.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yhS+aH6hPb1hSOt0tT6Rej2kQoPz6wrtntLOBuoIIJs=
			</data>
		</dict>
		<key>Headers/SPUStandardUpdaterController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gf8QsQfI+rsdQIHZdznFSFICnui4Oavnhgn9ybmv6Hs=
			</data>
		</dict>
		<key>Headers/SPUStandardUserDriver.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Y4fOofv2Ua1TAI9qM7wL8CalQGHLa1spvUNg9JB71NE=
			</data>
		</dict>
		<key>Headers/SPUStandardUserDriverDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Y1CXvdztYvr7TNU5uykV9jSjdvcpl2aCQPwjVHZe+IQ=
			</data>
		</dict>
		<key>Headers/SPUUpdateCheck.h</key>
		<dict>
			<key>hash2</key>
			<data>
			H30F2i5GYmOu/j4JEw5WsuZbiGJXnge5gpyb9e2SHAM=
			</data>
		</dict>
		<key>Headers/SPUUpdatePermissionRequest.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hnRNYPeaK0NWoKPXEYs2AoyD6QOE0CHDJWJxKM5Ma24=
			</data>
		</dict>
		<key>Headers/SPUUpdater.h</key>
		<dict>
			<key>hash2</key>
			<data>
			gHf2JF29zPpLfNllC9hyoYGyZVO9xJ0wNDCZNd65+hQ=
			</data>
		</dict>
		<key>Headers/SPUUpdaterDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			A7Rnysd5Q/yqhviRSeLa7hyuJQcqe4eRb+qxnUx7S9g=
			</data>
		</dict>
		<key>Headers/SPUUpdaterSettings.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Nnk1lMwSl/z+ugdM8ovU0U/bQUoOWG6zjYQHXPF+kCU=
			</data>
		</dict>
		<key>Headers/SPUUserDriver.h</key>
		<dict>
			<key>hash2</key>
			<data>
			6/byufu2xa4bjEdEwfuFxkHUiYnK9gbYEtplebyvTSY=
			</data>
		</dict>
		<key>Headers/SPUUserUpdateState.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Z4UKJcXMF/bUmSkIiM+D8jTiS8i5x1CLqoo4uG4aQvg=
			</data>
		</dict>
		<key>Headers/SUAppcast.h</key>
		<dict>
			<key>hash2</key>
			<data>
			M3KUgO+Ud/n5t/rXjFYzQMUTPDA8fK7W46QQfuh5DnA=
			</data>
		</dict>
		<key>Headers/SUAppcastItem.h</key>
		<dict>
			<key>hash2</key>
			<data>
			imKkb6r+8fp+9enH9Xlnh0VZ5S12ZkwmU53UHTx/Tdo=
			</data>
		</dict>
		<key>Headers/SUErrors.h</key>
		<dict>
			<key>hash2</key>
			<data>
			fmP8Y0mI10K5McjVGtVKtgzae36JLxmqLw3sr7vdBGY=
			</data>
		</dict>
		<key>Headers/SUExport.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XO8CQmbFThLbYg949NEGhg3g+iouIw3/3+BCCLtEdFE=
			</data>
		</dict>
		<key>Headers/SUStandardVersionComparator.h</key>
		<dict>
			<key>hash2</key>
			<data>
			fUB7nOch1cZQ50HstpLnfxLvO14Y6oE29yRI6NcgjGw=
			</data>
		</dict>
		<key>Headers/SUUpdatePermissionResponse.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HScQok/zuc704lNfwGl1Csr6nigQLAsjeRJXG1HTuks=
			</data>
		</dict>
		<key>Headers/SUUpdater.h</key>
		<dict>
			<key>hash2</key>
			<data>
			lvb1mNWCyofJ0X88zXvMsK5PBXrG6Pr7hUrY0E3jNvY=
			</data>
		</dict>
		<key>Headers/SUUpdaterDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			oaCMM/1B690QWRbYGB0iJBf5/toBzcQq+moxURBs3jY=
			</data>
		</dict>
		<key>Headers/SUVersionComparisonProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			KLu0JKx6uB4rk/YeNZ/vo+0J1qONCyZQBNIQPA9GvbQ=
			</data>
		</dict>
		<key>Headers/SUVersionDisplayProtocol.h</key>
		<dict>
			<key>hash2</key>
			<data>
			PyPE+5u9vBlxRYDuTdf3P/wxQ26nM8m7MIw/dOerUSw=
			</data>
		</dict>
		<key>Headers/Sparkle.h</key>
		<dict>
			<key>hash2</key>
			<data>
			OkQqMusip3u1oI5hrGeNr/32xpfTMCC4Kmg7r0Aijgw=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			K8rh62bVXcSfWKz+hWN1pbvJHG6DvbKY0ZRCrmJkz6Y=
			</data>
		</dict>
		<key>PrivateHeaders/SPUAppcastItemStateResolver.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HDvimACMGXV647Jwg2IqAFvOgJoB8G0sdPbeoW8yFO4=
			</data>
		</dict>
		<key>PrivateHeaders/SPUGentleUserDriverReminders.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9W2dJ38WQX151mpIS0r8/EfCqZV6jEh621xwna2JVAI=
			</data>
		</dict>
		<key>PrivateHeaders/SPUInstallationType.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hj9Br7Gf1Y8X1dqNvSUHMP70K+Q+S9xZAyPYMqKthFQ=
			</data>
		</dict>
		<key>PrivateHeaders/SPUStandardUserDriver+Private.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rMdk5zH+nm0wf+Mt6k0GtFGWQiXCsql0WiPwNanN6q0=
			</data>
		</dict>
		<key>PrivateHeaders/SPUUserAgent+Private.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DD6nxqq6syhA5BxWuyLPq03uTd4zAmA7b0q6msG1hQw=
			</data>
		</dict>
		<key>PrivateHeaders/SUAppcastItem+Private.h</key>
		<dict>
			<key>hash2</key>
			<data>
			9H7OIT9nEoYXUBBcvTZdSOzlq81uWIUS8Mqjuhfz9Ww=
			</data>
		</dict>
		<key>PrivateHeaders/SUInstallerLauncher+Private.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yy6cRl8IqwMJwr86TS98NdnHb/WkSAaUcAoiKYUg3ZQ=
			</data>
		</dict>
		<key>Resources/Base.lproj/SUUpdateAlert.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			msqgijJ6kRed766AfmWbJn145KdRi5g9UAZA1lbU8hc=
			</data>
		</dict>
		<key>Resources/Base.lproj/SUUpdatePermissionPrompt.nib/keyedobjects-101300.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			FE1Ls8JHeImpqisF2F5vkgNrgyLy3esoM5CoEU910U0=
			</data>
		</dict>
		<key>Resources/Base.lproj/SUUpdatePermissionPrompt.nib/keyedobjects-110000.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			T43hI+h3I1bnq0zpNmBQjHnZX8nF4ceCX9n8n0t8KNE=
			</data>
		</dict>
		<key>Resources/Base.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aZ6hIsTY6mApb4yL8CxqMHw0W1sTdp71lBqcEBR5ATk=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			+BB+A7FvWsdNGRdHPjnpR/ph7s+cZH/weSOLH1MDDuA=
			</data>
		</dict>
		<key>Resources/ReleaseNotesColorStyle.css</key>
		<dict>
			<key>hash2</key>
			<data>
			dr1pmXWP2OUdF+a0gttDT5tHaMArA3r2vS46AAzoy8E=
			</data>
		</dict>
		<key>Resources/SUStatus.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			LqrcSrjlLj0k1M9i0eVaZkLZQ2wmvWtwA7jvX8SGcwU=
			</data>
		</dict>
		<key>Resources/ar.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			33nOBJb6OPaZt3PKT2iUJ3RfF/c59DAGmt9TCQVn74A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ku6BdTbNrkSmKEdwyNA1hmoKbQ3uRv8JR4LK4cjqgpA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ar.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			yx9tkKjj3aOHvgdYCWXM89uhlyVeNb4oqcAenJxibwI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			18qLsTRnJfi0wDf6A85XbiMXGORSmuo9Ul3IK4m5gq0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ca.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			3iHHzb3P1DvR6KaXp59ybrj1JySnfOgPbOigOIgI7es=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qSoDl0PIYv+OrSxtJfUYk9xeQihmzfaxAf+egKyw4y4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			obkk1c1EawdfEyPHqo5ddIzsUcWfClFUbg895zj3/Ag=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/cs.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2Nm0MQj4WgMucaSuEdljuTIGS/oceXEuVWi2kDgjRq0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			aKNcPadrNnf7wuYmBAxoRzES9XhxXRHMrW/+9MtZBQs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xremLoAOqEfufOFyjOrH8S8ZkWwRklRhGCGB/igGD38=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/da.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			BOUi5PfyUb/ZRM6WZOuFC34IOic4+XPJkLikDtwhZIw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			A6JiLH5c4UX2iobAPXPHv7TLiBInrdHvtvqnnsTBxLI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GPjZbm0EAKfj0CK7Pb1UITo5WoDzNpf4m2XELfj3eio=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/de.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Wv+mo8RfxojepYE9GcBeYwUNycpqkk9qo/BjYGwb08M=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			utAXO7a8Od4ICYV3R0WQBa8ncUQ30SfruZACTuvyDxk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V0h7tXPJI0b1Z0FEMxe7RJIn2oWGg9QUhF/cRSz7aWE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/el.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Bl2oDZnDwbj55sSp/MNoHmcSbiOW5kxY1OGcL3k5scY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			EBVS8ZfEIJxGSghO17emwoHQo0LVWWzBJMFs8RwvKWg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/en.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			MN0HeTdXIxqALqUMUoLnVkRcDcvnDXqjsifU07tV3a8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8KSmmlZHYEiMGUwXQRV+ZDxs07XmaeH4XIYI+di1ono=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			GEtUsrVDWqXyHAV8lWPrEUWQm30jetvOjJZdlI7egwQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/es.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ePEUtDjS6DBYvydizpitYtRl22FpVXHcVWNAkniUsQg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fa.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Ue4NSlcLQL9OAOrD5Ibul1RaIwZOl6vcIv7DsffzQMA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			O+ja0EMKj5RxMmW3TRALc9XTpMJ7Y7dwXm706E33rUA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			MfjVC0QQ0Dxvz6Rt03EhMaahM5Gh5rhqMSJFEqzSRLo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fi.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			O6+s8+GKGX06x08WB1v526jOSl30MEoNnzjhYKe4IA0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Avyaxx14FRXq/CTIDvvF7uww42SRhYgNSc960h7MCfc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			qlNtkoH6vAA93/yxp8Stav74m46gvKb+3R26QDMSsXs=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/fr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			O5To7z4frtvm1D6zwFzz6rpleVtia2BFro3bElXznDg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			SmfKGCNVK9M61LCNGqWk4/FZInlcKG2U9uD5ajPVobw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/he.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			dWNbn7k2fHYYtPta1WhZ1DvglupayXIaQjBYK/8G7cQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			L7shRNgdZIfbt5y5pioLEIo+A9I7VtgIUFpzoCFkB1I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5x5zRCWzWYlbd7MkUcbPs5ZWrWQRDZnj3s9K2LmsnBw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Nmip9NgB014UDYN3yOsmsOFa9D3wED0L56Mve3WIVQg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8LbsWTkMSczHFa4Rh9XZDRo0uCOyrV9VXUYEiEvnG7I=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			twHi8JXysxao7MlTGr178ZpB8yz1mXkij2V5n8NJWSQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/hu.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			0UBqgjXjtRG51lEacNaLTmNvj5aFUeJ7oo1J4WYkrCw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			04Q9PpqtuYz6kfVhf6eI9XBxJn0LQB9Ck/ceBq1ztGU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			heK63dQF7YJvehrOEQk1jesq6v3bQBJy2jL+w5jjMlE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/is.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5/4A+HgH/PhUAQ3NVnURPeiIJsQYJyZ28sAObmxJlVU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			1zxkJlohqYtSJb0pj93fJXlPkedYm2IllbilGRDFo90=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XjyG4RGvcVUZia4jJHGYQEfgocs1iEx7iljn+vue5xY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/it.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2Glm2TwT78wZfe8iqBg8z/oCgrmtzqthnzNlHvHt5ls=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+grczSeMRPV+Oh8FAj+IPqtvuOC4WhJdknfh0eNdHWM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			pG/1lv3zRpIQkIlhEdvLHcTuPW/GfIPM8FALHstdt5c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ja.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			2afwbTtbV/x9iTHs2lInaksiJHghls+esi7MQ3bIq3Q=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/sIr/9ihcLhTxpc9CG0ZmCyhtI5lxHnRU2AY2Z7KjnA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Q6RJbOzFDzk17QpGPfUciOrtuitReWCBlt8ucofX2AM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			5GHHunaDv0dty7CogRs+i3C+zY3SkTVkReD90hjRdGk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RQbKlvLGnVjjVMP5eHHNUCv5kLJl4EA6zNGdDKatbH0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			F85iUA2aHbvo23Z0jJ/T/pwJ2HOQdYD5eRyAow9cSgY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nb.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6sl0vlKoRBQtvGvC7oGwtHA8/B+fNdwBGwN2AyISsXQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tp3fY8ogv+xcQOFkz5BkDNTZHIaRrhGgT9uKfCjDB70=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Yrwc9ESTayZoqv2JWm0nD1IHGLeAiBncPc2OeaVz8J8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			tBjWBavMtayqool9JGs+xH56A2Kui/7dRQX27xG2jx0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			giHYMaf7DkBFSModZ1CL3EswwwGHyOmkxRjtDP6bmk8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			V7VdecLbSo5sr8pMVUBnsJ04no1tM8XXsU/SHRWphs8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/nn.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ouNyYGUrFy2VqYdwZnSn1emcGTGoyHGqb8Sb9Cg0cWk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			oB2rGM/SPnJLdvhUz2CJfm8TS6XhrhmHD2gFyrVSq8U=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ubFfFWaG2RKXgeGR4DRtvbY0fH+SNJZzBebSPCo6K5c=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			KwctPd8Y+mrsEYWALAkKXeCfY4celUbA3MVn+Ye4Imc=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			hDN04zbJliR6KRqEv4lEuAVNTjbkmyYUpKjCbWKaKdU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			6NvKj6GmzGQLAsGBC6IUvRBoLSRfEJuWi7ZitoTyoTk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-BR.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			Kcwam/xq0V6VzMD4+rbUrDyAtrsl186J6lEeu5K2qD4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gto4ribWYRWZl0Eez6/7XZg3EesExPlGb5Nz1YVTuzE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			st4wdNoLjj5sVIFHqDAh3cjRFhxhpzkcFP7AJSXjYkw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/pt-PT.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			RLdbB4cQnps2k/crFyvGScdjmGE3KSkG448wTbYi4vY=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			PZZnueQNOLmQuEtkELhzxhnG+MDu7RyeOaySHSoHmYU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			IkIaDJ/HgpnBNNkP+MF2JGSd+rNgAI+o9c2aNor+ewo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ro.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			w2L0Ki7vhlIa92HDkDRmDExmCXIGkOWil+ROXr+6I+k=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wdiMmOcek4MJvdl1u2OoccWD56zCu2lKDGUd40bnMb8=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			/uARbwIQFupNOZvlyWgeE722GAsKcu4/QooLAEGHCBQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ru.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			INHL+VHSTd0hjYmUXHPShl3l4xTB4C3KcCLgVn/AHGw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			FDJ/dTwG5X34BF9lDDkFVGJUwpLeKi1MUbF072nYass=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			xa+UPXIC+og1IpGE6bA/+51E2uR9ZG+HGWKFA83tTNU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VmLLF6tJBA+j9jFby3BVx9GagD//qx4ETRywoU71PVQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			OAq7ojI6K/xR4nFEK1OBTiJeNaHqgb8xCgzZ5Y3P7Uo=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			ik4klDAQYgMT5hrecUkfi+K/tuGyvOYk96xp+z98l74=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sl.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			VTtzpxCnkPCO1yB2GviO6AkaZFKPpcUh530dTdqFcQE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LYXEBB7MF82Ig5MgIM9pTtJJAYJL51nzYzbVW1kdSGI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			XDORKepHKWfDihFVMFnshPW/qjSLPLoU/zHqJQbRoBk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/sv.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nidShaf5BKX1wQntkXeQhcGQoWUzNgVQhfHSM44GXVM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			dvWO9t2NYZ+cQoe/9B3Tib+EPOdPp4wgatHaVVhu8gQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			mxCL8k437ikdUpl3px2Ii/2fZqL85x1Fn/xe7h1YI6E=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/th.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			vj4h7104vuQqis9NXvSgrgQNz9czX6lMJqdvem4VCUk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			LbuuEfntGOLEDD5x0g9XHJ0C/aSUk14ltYe7Lr8tBXQ=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			cPWCofME3hhaqw5W8btHUEa2OMuG1gW/GQTnXET0/m0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/tr.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4gu++3cg7m13GuNxuYNnb8ug7xC0s/B4KtUZ5D+AMec=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			lR6DOvFkMHpmbtXQJNE1aXtRXgBbd0siVMoq01D4dhM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			+MphWMKEy2hsIqrjroJQcq+x1mytcNeZm+z3Lv+ll6A=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/uk.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			gvuLzOKGahqSQtAwSCb9CuBAYuDVwfj+lwwSv/NPq8s=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NXEAoNAKcjI5GBtGxYcUXmtz+rP06ocJSSVlaR/lnMA=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			8co96MJCMuKNvaPFe13uh2d028P/Cgpa8iOiNml9rfE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_CN.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			nKdLa3IQhWTKipj6MF+VwRUugjpvTVuGIpzQF/QDUYI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			zegYIhIFwtdJa87mjTlkalyYSz31LrnhiwNWDJDPqBU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			54ogzTvsgJOl4aSWIQRzRzky1TddmGlpamTLhHMJWb0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_HK.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AKEvYEi8FGZbbYnhpr2nqeUWrBQaj7wJjo8/KjED1U0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdateAlert.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			jdmB9inrJUf1OmYmVnORSMfdz5z1SWmBtdv39I776K4=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/SUUpdatePermissionPrompt.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			4tCLZKKcNuOJ1up1IgFXUeEp7s5U5BOBGHC1EZMyrhE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh_TW.lproj/Sparkle.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			NhrxIO01cQJckmOoEhEbQ73y2RcEy8drXCHDvsvcLEw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Updater.app</key>
		<dict>
			<key>cdhash</key>
			<data>
			FUu8fe4a0BThd4e3aAS1ESFerTA=
			</data>
			<key>requirement</key>
			<string>cdhash H"154bbc7dee1ad014e17787b76804b511215ead30" or cdhash H"98978bf031c6ea78567452af336111bb74f503e5"</string>
		</dict>
		<key>XPCServices/Downloader.xpc</key>
		<dict>
			<key>cdhash</key>
			<data>
			X5fZetRn2Saen2h7dEPry59CnV0=
			</data>
			<key>requirement</key>
			<string>cdhash H"5f97d97ad467d9269e9f687b7443ebcb9f429d5d" or cdhash H"d20a89312b4694cb7637a1beb849e39b54453b2b"</string>
		</dict>
		<key>XPCServices/Installer.xpc</key>
		<dict>
			<key>cdhash</key>
			<data>
			C2FFtOq0VOM5vJmKQYom8Xl9KVk=
			</data>
			<key>requirement</key>
			<string>cdhash H"0b6145b4eab454e339bc998a418a26f1797d2959" or cdhash H"bfe3d53e82fffda6c87e7dfa656e84ab49371973"</string>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
