# 2.6.4

* Fix app modification prompt from appearing when downloaded update overrides NSUpdateSecurityPolicy (#2593)

# 2.6.3

* Guard update timer update check against sessionIn<PERSON><PERSON>ress to fix rare crash when checking for updates (#2561)
* Remove extra writeData: call when unarchiving disk images (#2562)
* Ignore crashes due to SIGPIPE in generate_appcast when failing to extract zip files (#2563)

# 2.6.2

* Create and use temp extraction directory in generate_appcast again (#2555) (Zorg)

# 2.6.1

* Extract archives in a separate directory from the input archive (#2550) (Zorg)
* Fix the release notes WebKit view not updating background when transitioning from light to dark mode (#2542) (Zorg)
* Add NN (Norwegian Nynorsk) locale (#2532) (Sjur N Moshagen, Zorg)
* Create tar.xz files with built-in tar and remove bzip2 fallback for creating a release distribution (#2535) (Zorg)
* Add fallback in case SULocalizedStringFromTableInBundle() fails (#2533) (Zorg)
* Remove assert on download response being available fixing rare crash (#2547) (Zorg)
* Clarify when authoriation prompt may show in SPUUserDriver documentation (#2531, #2534) (Zorg)
* Fix typos in codebase (#2537) (<PERSON> Szépe)

# 2.6.0

* Perform Gatekeeper scan to pre-warm app launch (#2505) (Zorg)
* Disable sandboxing for the Downloader XPC service by default to fix downloader prompt warnings (#2511) (Zorg)
* Store private seed as the secret for newly generated keys (#2472) (Zorg)
* Improve signing error message to developers if they serve the wrong update file (#2471) (Zorg)
* Prevent app modification warnings from external updaters (like sparkli-cli) by improving installation (#2516) (Zorg)
* Update Korean localization (#2504) (CheolHyun Mun)
* Use $PROJECT_DIR instead of $SRCROOT (#2489) (Zorg)
* Set Package.swift minimum deployment to macOS 10.13 (#2481) (Eitot)
* Fix false positive analyzer warning about resumableUpdate type (#2454) (Zorg)

# 2.5.2

* Don't clean up update directory when Autoupdate receives SIGTERM (#2479) (Zorg)
* Update Japanese localization (#2475) (1024jp)
* Improve Turkish translations (#2464) (Emir SARI)
* Update Spanish translation for 'You are currently running version %@.' and 'Version History' (#2463) (Billy Gray)

# 2.5.1

* Default to English for XML nodes when no xml:lang is present (#2440) (Zorg)
* Filter for archive files in generate_appcast more intelligently (#2448) (Zorg)
* Use correct entitlements and dsym files when using custom bundle id and XPC names in ConfigCommon (#2446) (floorish)

# 2.5.0

* Add ability to adapt release notes based on the currently installed version (#2373) (Nathan Manceaux-Panot)
* Allow developers to use custom URL schemes in the release notes view (#2393) (Zorg)
* Adopt cooperative app activation APIs in macOS 14 Sonoma (#2409) (Zorg)
* Improve permission prompt layout (#2420) (Zorg)
* Remove hyphenation in "You're up to date" message (#2425) (Zorg, Dom Neill)
* Pre-warm installs before relaunch and resolve sporadic failures in CI (#2421) (Zorg)
* Fix make release not building distribution successfully (#2430) (Zorg)
* Fix Updater app not starting when running Sparkle as root (e.g. from CLI with sudo or a daemon) on macOS 14 Sonoma (#2432) (Zorg)
* Fix KVO usage for updaterController.updater.* (#2404) (Zorg)
* Replace CFUUID* with NSUUID (#2395) (Eitot)
* Report an error when detecting duplicate updates in generate_appcast (#2407) (Zorg)
* Improve error for rejecting xattr based code signing for delta updates (#2408) (Zorg)
* Fail gracefully when auxiliary tool cannot be located (#2436) (Zorg)

# 2.4.2

* Ignore release notes download when we shouldn't show release notes (#2381) (Zorg)
* Fix NSKeyedUnarchiver warning for not specifying keyed NSString class (#2381) (Zorg)
* Harden verification of Sparkle update download (#2392) (Zorg)

# 2.4.1

* Remove auxiliary apps and relocate symbols in SPM package to resolve missing AvailableLibraries warnings (#2356) (Zorg)
* Add -Wno-declaration-after-statement to silence warnings that only apply to pre-C99 (#2345) (Kent Sutherland)
* Fix compile error when setting SPARKLE_EMBED_DOWNLOADER_XPC_SERVICE=0 (#2346) (Zorg)
* Improve how downloaded update is passed to the installer (#2359) (Zorg)

# 2.4.0

* Reduce code size
  * Enable deployment postprocessing in Release to properly strip debug symbols and strip all non-global symbols (#2286, #2305) (Zorg)
  * Remove duplicate Sparkle localization strings in Updater app (#2288) (Zorg)
  * Optimize codebase for generated code size (less properties, direct methods, etc) (#2305) (Zorg)
  * Add additional settings to `ConfigCommon.xcconfig` for disabling features
* Ask permission for automatically downloading and installing new updates (#2285) (Zorg)
* Add support for plain text release notes view that does not use web view (#2315) (Zorg)
* Update `SUVersionDisplay` to better customize and display how versions are shown (#2321) (Zorg)
* Add deprecations/warnings for incorrect background update checking usage (#2295) (Zorg)
* Deprecate `-[SPUUpdater setFeedURL:]` API and add `-[SPUUpdater clearFeedURLFromUserDefaults]` for migrating away from `-[SPUUpdater setFeedURL:]` (#2295)
* Trigger a new update check in `-[SPUUpdater resetUpdateCycle]` if the updater's feed or allowed channels have changed (#2324) (Zorg)
* Exit with an error if generate_appcast cannot sign an update that must be signed (#2322) (Zorg)
* Remove a redundant Apple code signing check when verifying new updates (#2341) (Zorg)
* Turn off auto-linking for XPC targets to alleviate circular dependency issues (#2332) (Daniel Jalkut)

# 2.3.2

* Fix potential crash during download if appcast item includes invalid enclosure URL (#2317) (Zorg)
* Add delegate method to hide showing version history option (#2303) (Zorg)
* Finnish localization grammar fixes (#2311) (Lauri-Matti Parppei)

# 2.3.1

* Fix update permission alert title text overlapping with question text in some languages (#2284) (Zorg)
* Log the URL that failed to download correctly (#2296) (Zorg)
* Update Czech translation (#2275) (Sam)
* Add zh_HK (Chinese, Hong Kong) localization (#2273) (Bing Zheung)

# 2.3.0

* Bump minimum deployment target to macOS 10.13 (#2196) (Zorg)
* Remove and preserve necessary updates in generate_appcast (#2218) (Zorg)
* Move old update files no longer needed to old_updates/ in archive directory in generate_appcast (#2228) (Zorg)
* Expose maximum-versions option (per branch) in generate_appcast to preserve in the feed (#2259) (Zorg)
* Add -p option to sign_update to only print the signed signature (#2268) (Zorg)
* Hide automatic install check box when allowsAutomaticUpdates is disabled (#2202) (Jie)
* Allow developer to always force allowing automatic updates (#2266) (Zorg)
* Fix update window going haywire during resize when release notes are hidden by disabling resizing (#2200) (Zorg)
* Allow user to re-try installing/relaunching application when quit is delayed/cancelled (#2234) (Zorg)
* Add delta update attributes for validating an app hasn't been stripped from removed localizations or architectures (#2219) (Zorg)
* Fix delta patches not applying edge case if files from source that need to be diffed are not writeable (#2211) (Zorg)
* Add delegate callback when user makes choice to install, dismiss, or skip an update (#2250) (Zorg)
* Reject serving updates with DSA only and no EdDSA (#2167) (Zorg)
* Find potential matching running apps that are translocated in the updater agent (#2233) (Zorg)
* Expose -[SPUStandardUserDriver activeUpdateAlert] as private API (#2255) (Zorg)
* Add zh_CN l10n for "Version History" (#2247) (kakaiikaka)
* Update zh_TW localization (#2271) (Chiahong)
* Remove ed25519 git submodule in favor of including the dependency directly (#2244) (Zorg)
* Clean up left-over code (#2243) (Eitot)
* Update Package.resolved (#2245) (Zorg)
* Remove model translation table for system profiling (#2188) (Zorg)
* Improve documentation usage for SPUUpdater properties (#2256) (Zorg)

This release improves generate_appcast by automatically removing updates that are no longer needed in the generated appcast, and moving old update files to old_updates/ in the archives directory. New delta update attributes are also added to let Sparkle know when to skip downloading delta updates if the application has been stripped.

macOS 10.13 or later is now required (due to Xcode 14 dropping support for deploying to older OS versions).

If you're not generating appcasts automatically, remember to add `<sparkle:minimumSystemVersion>10.13</sparkle:minimumSystemVersion>` element to `<item>`s in your appcast.

# 2.2.2

* Critical update alerts may not show up as promptly as they should when they are being automatically installed (#2230) (Zorg)
* Remove module imports from framework headers (#2217) (Zorg)
* Update Portuguese localization (#2224) (Kent Sutherland)
* Update zh_TW localization (#2246) (Chiahong)

# 2.2.1

* Make scroll bar dark in dark theme for Release Notes by default (#2187) (Pavel Moiseenko)
* Fix memory leaks when using generate_appcast (#2193) (Zorg)
* Update Italian localization (#2192) (VinBoiSoft)

# 2.2.0

* Update Focus Improvements (#924)
  * Present new scheduled updates in utmost focus only at opportune times (Zorg)
    * For regular apps opportune times are: app launch, app re-activation, and system being idle (without a power assertion being held to prevent display sleep).
    * For background (dockless) apps opportune time is just app launch. Otherwise, the update is now shown behind other applications and windows, instead of previously stealing focus from other active apps.
  * Add APIs and documentation for adding gentle update reminders to compliment Sparkle's standard user interface (Zorg) (#2122)
  * Allow status window to be minimizable for regular app installs (Zorg) (#2100)
  * Center status window and inherit key focus from the previously shown update alert window (Zorg)
  * Activate app when checking for updates if the app is not currently active (e.g, from a menu bar extra menu item) (Zorg)
  * Fix issue where bringing status window to front made other active windows exit in macOS Ventura's Stage Manager (#2153) (Zorg)
  * Fix showing update in focus not bringing the "checking for updates" window in focus (Zorg) (#2150)
* Rename XPC Service filenames for Sandboxing to show more friendly human-readable name in authorization dialog (Zorg) (#2096)
* Add support for running the framework and sparkle-cli as root (Zorg) (#2119)
* Fix issue where update cycle may not complete in unusual configuration if automatic checks are disabled + automatic downloading is enabled + install requires user interaction (Zorg) (#2133)
* Synchronize usage of XPC connections to main queue to fix potential race conditions (Zorg) (#2178)
* Update last update check time when choosing to install an update & relaunch (Zorg) (#2136)
* Improve error reporting in the framework and sparkle-cli when installation fails with no write permission (Zorg) (#2157)
* Use displayVersionString instead of versionString for OS version mismatch error message (samschott) (#2138)
* Make displayVersionString non-null and update fallback documentation (Zorg) (#2139)
* Ignore custom icons set via resource forks when applying delta updates (Zorg) (#2114)
* Fall back to regular update if delta update fails to download (Zorg) (#2118)
* Skip downloading delta updates when application has been moved to a file system (like FAT) that doesn't support regular permission modes (Zorg) (#2148)
* Bump initial installer message timeouts and declare daemon/agents processes as Interactive (Zorg) (#2162)
* Add and improve translation strings and update pt-BR (BR Lingo) (#2094)
* Update localisations (Eitot) (#2113)
* Update Greek localizations (seitsme) (#2184, #2185)
* Update Japanese localization (1024jp) (#2182)
* Replace deprecated code with newer APIs (Eitot) (#2112)
* Remove obsolete fallbacks for older OS versions (Eitot) (#2110)
* Remove SPUURLRequest (Zorg) (#2124)
* Silence ivar deprecation warnings (Zorg) (#2099)
* Fix Xcode 14 project warnings (Zorg) (#2147, #2179)
* Deprecate -s flag and add --ed-key-file option to generate_appcast (Zorg) (#2170)
* Update text for external licenses (Zorg) (#2164)

This update renames the bundled XPC Services, brings improvements to notifying users of new updates without disrupting their focus, and adds [gentle update reminder APIs](https://sparkle-project.org/documentation/gentle-reminders/) to further customize how Sparkle's standard user interface delivers new update alerts.

The `-s` flag for passing a raw private EdDSA key to sign_update and generate_appcast is now deprecated. If you were using this option previously, please see the help pages of these tools for more information.

# 2.1.0

* New Binary Delta format (version 3)
  * Features a custom and more efficient container format, migrating away from the deprecated xar format (Zorg) (#2051)
  * Adds delta compression options for lzma, bzip2, zlib, lzfse, lz4, and no compression (Zorg) (#2051)
  * Changes default delta format compression from bzip2 (in version 2) to lzma (in version 3) resulting in smaller deltas (Zorg) (#2051)
  * Preserve file system (HFS+/apfs) level compression when applying delta updates (Zorg) (#2084)
  * Tracks renames and binary diffs for files that have moved around to new locations using intelligent heuristics (Zorg) (#2051, #2053)
  * Added more unit tests, UI tests, and generate_appcast/BinaryDelta tools support for the new format (Zorg) (#2052, #2054)
* Major upgrade improvements (#2070)
  * Fix skipping a major version to not skip subsequent major versions (Zorg) (#2079)
  * Add sparkle:belowVersion element for informational updates (Zorg) (#2080)
  * Add option to allow developers to ignore/reset user skipped upgrades (Zorg) (#2081)
* Fix progress bar and button alignment for checking updates (Zorg) (#2066)
  * This deprecates `-[SPUUserDriver showInstallingUpdate]` and `-[SPUUserDriver showSendingTerminationSignal]` in favor for `-[SPUUserDriver showInstallingUpdateWithApplicationTerminated:]`
* Fix unsteady progress when installing updates (Zorg) (#2072)
* Check http statusCode in didFinishDownloadingToURL (Eric Shapiro, Zorg) (#2049, #2073)
* Use strcoll_l() for locale-independent comparisons for delta updates (Dan Rigdon-Bel) (#2087)
* Fix version compare not treating '2.1.0' and '2.1' as being equal (Zorg) (#2065)
* Add verify and account options for signing updates (Zorg) (#2074)
* Add delta patch and Apple code signing verification in generate_appcast (Zorg) (#2076, #2077)
* Use more modern NSSecureCoding APIs when available (Zorg) (#2058)
* Use more modern FileManager APIs for copying files (Zorg) (#2059)
* Fix make release failing when customizing XPC_SERVICE_BUNDLE_ID_PREFIX (Zorg) (#2060)
* Preserve the Entitlements directory in podspec (digitalMoksha) (#2062)
* Add hidden option to generate_appcast to set max CDATA threshold (Zorg) (#2075)

This update introduces a new major format for [delta updates](https://sparkle-project.org/documentation/delta-updates/), which migrates away from deprecated APIs (xar) and creates smaller patches. If you don't use `generate_appcast`, please check the [compatibility notes for creating delta updates](https://sparkle-project.org/documentation/delta-updates/#compatibility).

# 2.0.0

* Support for Sandboxed Applications (Zorg)
* Support for writing custom user interfaces (Zorg)
* Support for updating external Sparkle-based bundles (Zorg)
  * Added command line utility to update Sparkle-based bundles (Zorg)
* Modern architecture
  * Moves extraction, validation, and installation into a submitted launchd agent/daemon with XPC communication (Zorg)
  * Features faster installs with shorter update/relaunch times (#1802) (Zorg)
  * Provides more robust installs when user authorization is needed (Zorg)
  * Adoption of improved atomic-safe updates leveraging APFS (#1801) (Zorg)
* API Changes
  * Introduced new SPUStandardUpdaterController, SPUUpdater, SPUUserDriver classes/protocols (Zorg)
  * Decoupled AppKit and UI logic in the framework from core functionality (Zorg)
  * Ensure (most) API compatibility with Sparkle 1; you can likely test Sparkle 2 in an existing app with little to no changes (Zorg)
  * Deprecated SUUpdater, albeit it is still functional for testing and transitional purposes (Zorg)
  * `-bestValidUpdateInAppcast:forUpdater:` delegate method behavior has been refined and discouraged for some cases. Please review its updated header documentation in `SPUUpdaterDelegate.h` if you use this method. (#1838, #1862, #1879, #1880) (Zorg)
  * Delegation methods may have been removed or added to the newer updater API. Please review `SPUUpdaterDelegate` if using `SPUUpdater`. (Zorg)
* Updater Changes
  * Automatic silent and manual update alert prompts are now merged together (Zorg)
  * Updates will attempt to install even if the user quits the application without relaunching the application update explicitly (Zorg)
  * Updates can be downloaded in the background automatically but later prompt the user to install them, particularly if Sparkle doesn't have sufficient permission to install them without the user's permission (Zorg)
  * Authorization now occurs before launching the installer and before terminating the application, which can be canceled by the user cleanly (Zorg)
  * Sparkle uses the icon of the bundle to update for its authorization dialog. A 32x32 image representation of the icon is needed. (Zorg)
  * Sudden termination for silent automatic updates isn't disabled anymore (Zorg)
* Policy Changes
  * Non-bare package based updates that are zipped or archived must add sparkle:installationType="package" to the appcast item enclosure (this doesn't apply to bare packages which aren't archived) (Zorg)
  * We now recommend using sparkle:version and sparkle:shortVersionString top level elements instead of enclosure attributes (#1878) (Zorg)
  * The link element in an appcast item is now used for directing users to the product's website if they don't meet minimum system requirements (#1877) (Zorg)
* Expose why a new update is unavailable and direct user to prior release notes or website info (#1877, #1886) (Zorg)
  * Add <sparkle:fullReleaseNotesLink> element allowing Sparkle to show a better stylized and full changelog to the user (#2001) (aONe)
  * Add delegate API allowing applications to show full in-app or offline version history to the user (#1989) (Billy Gray)
* Major/Paid Upgrades Enhancements
  * Latest minor updates are preferred over major updates (specified by sparkle:minimumAutoupdateVersion) (#1850) (Zorg)
  * Major updates can be skipped with a user confirmation (#1853) (Zorg)
* Informational only and critical updates can be specified selectively by app version (#1862)
* Add support for posting updates only on specific channels (eg for supporting beta updates) (#1879) (Zorg)
* System profiler privacy and transparency (#1690) (Martin Pilkington)
* Support getting app icon from asset catalog (#1694) (Charles Srstka)
* Don't bring up authorization just because group ID doesn't match (#1830) (Zorg)
* Raise minimum system version to macOS 10.11 (Zorg)
* Special thanks to developers using early builds of this release in production and contributors for keeping this running (Kornel, Jonas Zaugg, Gwynne Raskind, Jordan Rose, Tony Arnold, Bryan Jones, Christian Tietze, Jakob Egger, and many more)

Please visit [Sparkle's website](http://sparkle-project.org) for more information on documentation and migration.

If you are migrating from earlier beta versions of Sparkle 2 and use sandboxing, please re-familiarize yourself with the [Sandboxing guide](https://sparkle-project.org/documentation/sandboxing/). Some of the XPC Services are now optional and integration with XPC Services and code signing have been simplified. The SPUUserDriver protocol for custom user interfaces has been greatly simplified too.

# 1.27.3

 * Create and use temp extraction directory in generate_appcast again (#2556) (Zorg)

# 1.27.2

 * Extract archives in a separate directory from the input archive (#2552) (Zorg)
 * Fix incorrect xz log warning in make release (#2044) (trss)

# 1.27.1

 * Use px instead of pt when specifying font size to fix default font size in WebView from #1962 (regressed in 1.25.0 with WKWebView adoption) (Daniel Jalkut)
 * Fix to prevent Sparkle manipulating the host app's high level WebView defaults from #1961 (affecting only macOS versions before 10.11 in 1.x branch) (Daniel Jalkut)
 * Add safer handling of applying binary delta files from #1990 (Zorg)
 * Don't use current date unless necessary when scheduling next update from #1991 (Zorg)
 * Whitelist about:srcdoc as a safe web URL from #1938, #2007 (Louis Pontoise, Zorg)
 * Backport not hiding update window on deactivation from #2008 (Zorg)

# 1.27.0

 * Deprecate not using EdDSA and skip DSA verification when possible in #1892 (Zorg)
 * Pass http headers and user agent when downloading release notes from #1873 (Zorg)
 * Fix project warnings from #1893 (Zorg)
 * Update sample appcast from #1895 (Zorg)
 * Fix appcast pubDate tag generation in different locales from #1898 (Sungbin Jo)
 * Create valid xar archives for generating binary delta files from #1906 (Zorg)
 * Fix a few issues with German localization from #1931 (J-rg)
 * Fix issues with Russian localizations from #1947 (Konstantin Zamyakin)
 * Fix issues with Czech localizations from #1943 (Vojtěch Koňařík)
 * Add an automated workflow that builds and publishes official Sparkle releases (Tony Arnold, Zorg)

# 1.26.0

 * Flat package support from #1745 (Zorg)
 * Correct generate_appcast -s command line argument usage help (Lance Lovette)
 * Fix -f command line argument handling (Lance Lovette)
 * Fix progress for guided pkg install (Zorg)
 * Fix XQuartz update failing because NSLog caused issues (Zorg)
 * Update localization (Vojtěch Koňařík)
 * Various fixes to SPM logic (Jonas Zaugg)

# 1.25.0

 * Raise minimum system version to 10.9 (Kornel)
 * Allow an appcast to prevent the new version from being installed automatically (#1688) (pierswalter)
 * UI fixes
   * Show check for updates button only when automatic updates are enabled (Lorena Rangel)
   * Don't initialize the webview if we don't show release notes (Tobias Fonfara)
   * Keep skip button available if there is a minimum autoupdate version (Zorg)
   * Fix automatic updates checkbox state when not showing release notes (Zorg)
   * Fix edge cases with hiding alert buttons and using "Install Later" (Zorg)
   * Re-add setting WebUIDelegate fix for legacy web view (Zorg)
   * Adopt WKWebView for 1.x (Zorg)
   * Add auto layout constraints to SUAutomaticUpdateAlert window (Zorg)
   * Made buttons wide enough to display the full text. Made window wide enough to display the widened buttons (el, fi, ru, uk) (Piers Uso Walter)
   * Remove max size from update alert. (George Nachman)
 * Integrate generate_keys export/import options changes from #1730 (Zorg)
 * Add `—release-notes-url-prefix` support to localized release notes (Adam Tharani)
 * Translations
   * Enable base internalization for alert xibs (Zorg)
   * Capitalizes french translation of "Vous utilisez actuellement..." ("You are currently running...") (Micah Moore)
   * Fix and cleanup Hebrew, Catalan, Finnish localizations (Zorg)
   * Remove non-english localizations from TestApplication (Zorg)
 * Fix catch-22 Swift Package Manager binary target (Jonas Zaugg)
 * Add explicit dependencies to fix Xcode linking errors (Kornel)
 * Add phased rollouts feature to automatic update driver too (Zorg)
 * Remove hiding skip button and retitling later button (Zorg)
 * Deprecate installUpdatesIfAvailable (Zorg)
 * Apply custom headers to app download (Geraint White)

 # 1.24.0

 * Prioritize UserDefaults when fetching value for EnableAutomaticChecks (Nicolas Bosi)
 * Improve best appcast item selection handling (Ian Bytchek)
 * Create lockfile for improved compatibility with 3rd party updaters (CoreCode)
 * Enable SPM support via binary target (Jonas Zaugg)
 * `generate_appcast` improvements:
     * follow symbolic links (Denis Dzyubenko)
     * update the release notes element’s URL when required (Brad Andalman)
     * Added output filename option to generate_appcast (Brad Andalman)
     * Added —release-notes-url-prefix to generate_appcast (Brad Andalman)
     * Command line option to provide a download url prefix is now parsed and set on each archive item (Dominik H)
     * Added help command line option (Dominik H)
 * Locale updates:
     * Update zh_CN localization (柳东原 · Dongyuan Liu)
     * Update Sparkle.strings (Vojtěch Koňařík)
     * Update Croatian (#1603) (milotype)
     * Update SUUpdateAlert.xib (DanielFirlej)
 * fix: read SUAutomaticallyUpdate from Info.plist (Trevor DeVore)
 * Fix casting Boolean to BOOL on Apple Silicon (Kevin Wojniak)
 * Use build matrix to test on multiple Xcode versions (Rajiv Shah)
 * Set UpdateAlert and AutomaticUpdateAlert window's fullscreen collectionBehavior to NSWindowCollectionBehaviorFullScreenAuxiliary to allow them to present on top of the Main App's window if it's fullscreen. (Micah Moore)
 * Fixed error about "about:blank" release notes (Louis Pontoise)
 * Support App Store URL scheme in release note webview (Bi11)
 * Use the SHA-256 hash of the archive as the cache path (Nate Weaver)
 * Don't return an optional from the FileHandle method (Nate Weaver)
 * Added URL+Hashing (Nate Weaver)

# 1.23.0

 * Support generating appcast with localizations (#1499) (Alik Vovkotrub)
 * Support versions with git commit SHA (#1504) (Alec Larson)
 * Hide "Skip..." and "Remind..." buttons when they're not relevant (#1480) (Kenneth Johnsen)
 * Preserve Finder tags while updating apps (#1512) (CoreCode)
 * Read-only update alert dialog formatting improvements (#1515) (Quinn Taylor)
 * Check if `SUBundleName` is set before normalizing (Jake Fenton)
 * `NSInteger` cast warning on Xcode 11 (Marga Keuvelaar)
 * Correct appcast file extension (Tom Vos)
 * Update Sparkle.strings (Emir Sarı)
 * Fix spelling (#1508) (Frank Chiarulli Jr)

# 1.22.0

 * Enabled "Hardened Runtime" build option for Apple's notarization requirement
 * Add delegate methods to suppress update alerts (George Nachman)
 * Improved error when running from translocated location (Michael Buckley)
 * Add phased rollout feature (#1381) (Fabian Jäger)
 * Ignore non-standard permissions in delta updates instead of failing the build (Kornel Lesiński)
 * Notify user when installed version is newer than the latest in the appcast (CoreCode)
 * Reset timers after computer sleep (CoreCode)
 * Block-based alternatives to `NSInvocation`-based delegate methods (Fabian Jäger)
 * Add delegate `userDidSkipThisVersion` (BobZombie, Leo Natan, bono yusuke)
 * Pass item to updaterShouldShowUpdateAlertForScheduledUpdate delegate method (George Nachman)
 * Support providing private key as argument in `generate_appcast` (Yakuhzi, marchyang)
 * Separate the ed25519 sources into a new static library (Tony Arnold)
 * Disambiguate signing error messages (Nate Weaver)
 * Use `XMLNode.Options.nodePrettyPrint` in `generate_appcast` instead of trying to add whitespace manually (fumoboy007)
 * Annotate SUHost for nullability (Michael Buckley)
 * Use SUAVAILABLE macro (Christiaan Hofman)
 * Fix warnings when using modules (nivekkagicom)
 * Correction of Czech localization inconsistency (#1403) (vojtakonarik)
 * BR locale fix (BR Lingo)
 * Update Japanese localization (fujisoft)
 * French Sparkle.strings (Jean-Baptiste)

# 1.21.3

 * Losslessly reduced the size of PNG (Barijaona Ramaholimihaso)
 * Catch exceptions from subcommands (Julian Mayer)
 * `generate_appcast` can sign any bundles instead of just apps (Nate Weaver)
 * Check that effectiveAppearance is being observed before calling removeObserver (Pierluigi)

# 1.21.2

 * Allow EdDSA for delta updates, too (Kornel)
 * Warning fixes (Brian Bergstrand)
 * Improvements to release notes view context menu and dark mode (Bi11)

# 1.21.0

 * Added support EdDSA (ed25519) signatures (Kornel)
    * DSA signatures are considered outdated, and Apple's `Security.framework` only supports weaker SHA-1-based DSA.
    * Both old DSA and new EdDSA are still supported (and one app can use both), but new applications should use EdDSA only, and we recommend migrating away from DSA signatures.
 * `generate_keys` is now a Swift tool that stores EdDSA private keys in the Keychain
    * Existing apps can continue using their old DSA keys, but we've dropped support for generation of old DSA keys
 * `sign_update` is now a Swift tool that signs using EdDSA from private keys in the Keychain
    * The old DSA-based signing script has been moved to `bin/old_dsa_scripts`
    * The old DSA-based signing script has been fixed to work on pre-10.13 systems (Thomas Tempelmann)
 * `generate_appcast` has been updated to support EdDSA signatures
    * It can sign both DSA (if `dsa_priv.pem` file is specified) and EdDSA (from Keychain)
    * The tool now uses Caches directory and doesn't generate unnecessary delta files
 * Fixed verification of delta updates on filesystems that change permissions of symlinks
 * Fixed `NSURLSession` leak (Michael Ehrmann)

# 1.20.0

 * `generate_appcast` option to read private key directly from the keychain (Tamás Lustyik)
 * Add delegate callbacks for finished download and extraction related events (Csaba Horony)
 * Don't check for updates if Do Not Disturb is on (Kornel)
 * Expose `CodesigningVerifier`, add codesign info API (sunuslee)
 * Threading fixes:
   * Fix potential hang with `dispatch_sync` to main thread (Brian Bergstrand)
   * Fix closeCheckingWindow called from background thread (Alexey Martemyanov)
 * Improve 'read-only' error message (#1192) (Adrian Thomas)
 * New Spanish localisation (Ken Arroyo Ohori)
 * Updated Finnish language resources (Jason Pollack)
 * Hungarian localization (Csaba Horony)
 * Log more information about authentication requests (Kornel)
 * Explicitly specify types to silence "Messaging unqualified id" warning that's new in Xcode 10. Removed __has_feature(objc_generics) check and use generisc to help silence the warnings. (Kent Sutherland)
 * Fix binary delta creation on network drives (sagecook)
 * Fix compilation issues on Xcode 10 with new build system (Leo Natan)

# 1.19.0

 * Refactoring of downloader code to avoid deprecated methods (Deadpikle)
 * Changes to which methods run on the main thread.
   Note: some delegate methods may be called on non-main thread now. (Kornel)
 * Update Japanese localization (1024jp)
 * Update Sparkle.strings (Stefan Paychère)
 * Fix Sparkle clients polling too frequently (Jonathan Bullard)
 * Handle SecTransformExecute errors (Kornel)
 * Silence Touch Bar availability warnings on Xcode 9 by using API_AVAILABLE. Disable gnu-zero-variadic-macro-arguments to prevent warnings from use of API_AVAILABLE. (Kent Sutherland)
 * 10.11 SDK compatibility (David Fuhrmann)

# 1.18.1

 * Add optional updaterDidRelaunchApplication: method on SUUpdaterDelegate (#1115) (App Tyrant)
 * Implemented sparkle:os attribute as documented (Memphiz)
 * Additional termination detection in case kpoll fails. (fujisoft)
 * Included bin files in CocoaPods installation (Keith Smiley)
 * Updated Dutch localization (Eitot)
 * Updated German localization (Eitot)
 * Updated Japanese translation (1024jp)
 * Updated Portuguese translation (Victor)
 * Updated to Xcode 9/Swift 4

 # 1.18.0

 * Name of the host app is used in authorization prompt (the `SPARKLE_RELAUNCH_TOOL_NAME` setting is now obsolete)
 * More detailed progress bar for package installers (Kornel Lesiński)
 * Disabled the keyboard shortcut for the install button for scheduled updates to avoid accidental installs. (George Nachman)
 * generate_appcast tool adds release notes if there's an .html file with the same base name as the archive (Brett Walker)
 * Added `sparkle:shortVersionString` to the enclosure, #1032 (Brett Walker)
 * Fixed Japanese localization (1024jp)
 * Fixed escaping of system profile URLs
 * Added more logging in various failure cases (Kornel Lesiński)
 * Better error message for quarantined apps that can't be updated

# 1.17.0

 * Added Touch Bar support (Bi11)
 * Upgraded SULog to use logging APIs that Apple provides built-in (Zorg)
 * Skip buttons are disabled if the update is marked as critical (Kornel Lesiński)
 * Background updates ask OS for lower-priority networking (Kornel Lesiński)
 * Refactorings to sync with upcoming 2.0
   * Added kqueue based termination listener (Zorg)
   * Added AppKit prevention guards to modules that shouldn't import it (Zorg)
   * Added Obj-C generics where applicable (Zorg)
   * Made SUBundleIcon & SUApplicationInfo take SUHost, not NSBundle (Zorg)
   * Improved -[SUHost objectForInfoDictionaryKey:] (Zorg)
   * Detect and fail if any two-way dependencies exist in the project (Zorg)
 * generate_appcast:
   * fixed handling of multiple directories in an archive
   * percent encode the filename used in the delta url (Brett Walker)
 * Update Sparkle.strings (BR Lingo)
 * Improved handling of non-ASCII names in delta archives (Kornel Lesiński)
 * Don't touch Info.plist unless git version changes (Václav Slavík)

# 1.16.0

 * Guided package installs are now the default for updating packages (Zorg)
   - `pkg` installers won't show any UI. If you require the old behavior of showing a full installer window, rename the `*.pkg` file to `*.sparkle_interactive.pkg`
 * Previous version of the app is now deleted instead of staying in the trash (Zorg)
 * Added `generate_appcast` helper tool (Kornel Lesiński)
 * Made manual check resume pending automatic updates instead of starting a new update (Kornel Lesiński)
 * Started using `length` value from RSS if HTTP doesn't give one (Zorg)
 * Hidden automatic updates checkbox for information only updates (Bi11)
 * Added progressbar for DMG and binary delta extraction (Kornel Lesiński)
 * Fixed showing of download status if we attempt a 2nd download (Zorg)
 * Refactorings to sync with upcoming 2.0
    * Decoupled and simplified installation code using protocols (Zorg)
    * Added nullability annotations (Zorg)
    * Allowed delegate methods that return an object to return nil (Zorg)
    * Decreased responsibility of SUHost and moved code into other components (Zorg)
    * Removed Sparkle.pch and many file #includes (Zorg)

# 1.15.0

* A new icon! Thanks to 1024jp
* Show alert when an update is sent over insecure HTTP with no DSA key (Zorg)
  - If you can't use HTTPS, you must at least sign updates with a DSA key.
* Improved binary delta implementation (Zorg)
* Added improved -validateMenuItem: as a method in SUUpdater.h for public use (Zorg)
* Removed reachability preflight check (Zorg)
* Clear update caches directory before downloading new update (Zorg)
* Check the bundle's parent directory for writability too (Zorg)
* Don't follow symbolic links for file operations (Zorg)
* Don't bring up an authorized dialog during cleanup (Zorg)
* Made Sparkle look for the highest compatible version regardless of timestamps (Zorg)
* Fixed compatibility with 10.7
  * Fixed crash on 10.7 - subscript operator not available (kleuter)
  * Fixed warnings caused by -Wpartial-availability (Zorg)
* Fixed german l10n. (Sebastian Volland)
* Error code for download errors (Kornel Lesiński)
* Update last update check date when the update driver finishes (Zorg)
* Scale app icon up if needed in Software Update window (Nicholas Riley)
* Don't register for termination notifications more than once (Zorg)
* Don't terminate the app if we're already terminating (Zorg)
* Removed SUEnableAutomaticChecksKeyOld and SUCheckAtStartup constants (Eitot)
* Updated Sparkle framework headers to use modules if modules are available (B. Kevin Hardman)
* Fixed warnings, fixed uses of SULocalizedString (Jerry Krinock)
* Improved signing verifier to take any host and s/application/bundle/ (Zorg)
* Improved Spotlight updates after delta extraction (Zorg)

# 1.14.0 (Mar 11, 2016)

* Disable javascript by default and make it opt-in (Zorg)
* URL-encoding of appcast URLs is preserved (Kornel Lesiński)
* Delegate is asked for fallback updates if delta update fails (Kornel Lesiński)
* Fixed crash on 10.7 - subscript operator not available (kleuter)
* Fixed check of feed URL before delegate had a chance to set it (Kornel Lesiński)
* Re-added support for password-protected dmg images (Andrew K. Boyd)
* Added warning about ATS blocking (Kornel Lesiński)
* Translation fixes for pt-BR. (vitu)
* Add some Japanese lozalized strings (1024jp)
* Made test app available in all languages #695 (LIU Dongyuan / 柳东原)
* Czech localizations update (Frantisek Erben)
* Removed a test resource from the framework bundle (Karl Moskowski)
* Test if the updated app is the frontmost one (Zorg)
* UI Tests for the Test Application (Zorg)

# 1.13.1 (Jan 31, 2016)

Important security fixes:

* Prevent inclusion of local files via file:// XML entities
* Disable redirects to non-HTTP URLs in release notes webview

# 1.13.0 (Dec 18, 2015)

* Changed framework's bundle ID from `org.andymatuschak.Sparkle` to `org.sparkle-project.Sparkle`.

# 1.12.0 (Dec 13, 2015)

* Rewritten file operations for updating an app (Zorg)
  - Ensuring atomic move operations, robust error handling.
  - Faster.
  - Using modern APIs where possible (no FSPathMakeRef, FSGetCatalogInfo, FSFindFolder, etc.)
  - Strong documentation, easier to read code.
* Automatic updates won't be installed if the system is about to shut off (Zorg)
* Deprecated serving over HTTP without DSA (Zorg)
  - Note that Apple has deprecated insecure HTTP in macOS 10.11
* Improved Autoupdate application (Zorg)
    * Do all the installation work after the runloop is set up
    * TerminationListener only does termination listening now
    * Handle cases where host path is not installation path and host path is not desired executable path
    * Don't show Autoupdate dock icon if we shouldn't show UI
    * Update modification & access time for new update
* Added installUpdatesIfAvailable (Ian Langworth)
* Removed extensions from shell scripts (Jake Petroules)
* Rewritten test app so it works again, and from a local web server (Zorg)
* Replaced use of Python with built-in web server (Kevin Wojniak)
* Set LD_RUNPATH_SEARCH_PATHS in Podspec (Jake Petroules)
* Don't install automatic updates if the system might shut off (Zorg)
* Don't show Autoupdate dock icon if we shouldn't show UI (Zorg)
* Updated layout constraints when removing release notes (Zorg)
* Improved BinaryDelta error handling & logging (Zorg)
* Refactored quarantine removal (Zorg)
* Fixed German localization (1024jp)
* Updated zh_CN translation (LIU Dongyuan / 柳东原)
* Updated Mac models list until July 2015 (Gabriel Ulici)
* Updated Polish translation (Kornel Lesiński)
* Updated Xcode project languages for which we have translations (Jake Petroules)
* Updated XIB files (Kornel Lesiński)
* Use NSByteCountFormatter if available (Jake Petroules)
* Declared protocols on SUUpdateAlert for the 10.11 SDK (Daniel Jalkut)
* Silenced warning about casting away const-ness and -Wassign-enum (Daniel Jalkut)
* Added script to generate a report comparing the Sparkle.strings files (Kevin Wojniak)
* Check for empty strings (as well as nil) in SUHost's -name method (Karl Moskowski)
* Don't follow symlinks for checking file existence (Zorg)
* Unit tests in Swift (Zorg, Jake Petroules)
* Fixed framework imports (Felix Schulze)
* Fixed issues with copying files from different mounted drives (Zorg)
* Disallowed automatic updates when user can't write to the bundle (Zorg)
* Set the task working directories instead of changing the process working directory (Kevin Wojniak)

# 1.11.1 (Nov 9, 2015)

* Don't install automatic updates when system is about to shut down

# 1.11.0 (Aug 24, 2015)

* Big improvements to code signing and DSA verification
   - Sparkle now checks not only whether an update is correctly signed, but also whether the updated version will be able to verify future updates. Updates now must either use DSA keys correctly, or not try to use them at all. Same goes for Apple Code Signing.
   - Rely on code signing and the DSA key in the new app instead of appcast. If the new app has a public DSA key, then the appcast item must have a DSA signature for the app, even if the app is code signed. (Zorg)
* More verbose error message when DSA keys don't match (Kornel Lesiński)
* Added delegate methods for pre-download and immediately post-failed-download (Isaac Greenspan)
* Fix Lucida Grande is always used for release notes (LIU Dongyuan / 柳东原)
* Only remove quarantine with setResourceValue: when it's present. Fixes "Unable to quarantine: 93" messages from showing up in the console. (Zorg)
* Fixed const and nullability warnings (Jake Petroules, Kornel Lesiński)
* Replaced deprecated NSRunAlertPanel/alertWithMessageText (Kevin Wojniak)
* Imported the Foundation umbrella header in all the public headers (C.W. Betts)
* pt-BR localization update (Victor Figueiredo)
* Reject unsupported code-signing xattrs in binary delta (Zorg)
* Fixed crash while applying delta update (antonc27)
* Added logging of appcast/download URL on error (Kornel Lesiński)
* More robust reading of Autoupdate.app path from Sparkle bundle

# 1.10.0 (Apr 26, 2015)

* Massive improvements to the BinaryDelta tool (Zorg)
  - Ability to track file permissions (Zorg)
  - Nicely formatted log output (Zorg)
  - Numerous bug fixes in handling of symlinks, empty directories, case-insensitive names, etc. (Zorg)
  - Refactored and modernized code (Zorg)
  - libxar is no longer weak-linked (C.W. Betts)
* Double-check the code signature of the the app after installation (Isaac Wankerl)
* Added headless guided package installation (Graham Miln)
* Added ability to inject custom HTTP headers in appcast request (Mattias Gunneras)
* Changes to make unarching more reliable (Zorg, Kornel Lesiński)
* Have Sparkle build a framework module (C.W. Betts)
* Stdout used for non error outputs (JDuquennoy)
* French locale update (Kent Sutherland)

# 1.9.0 (Jan 26, 2015)

* Added SUUpdater delegate method for failures. (Benjamin Gordon)
* Make the error definitions public (C.W. Betts)
* Add support for lzma compressed tarballs (Kyle Fuller)
* Back to SKIP_INSTALL=YES by default (Tony Arnold)
* Properly set install names and rpaths for targets (Jake Petroules)
* Use Library/Caches rather than app support directory (Kornel Lesiński)
* Check for a modal window being onscreen before trying to put up the Sparkle prompt (Alf Watt)
* Fixed crashes on 10.7 (Chris Campbell, Ger Teunis)
* Fixed Sparkle tags parsing (Tamás Lustyik)
* SULog code cleanups (Kevin Wojniak)
* Make sure CFBundleVersion is a semantic version number. (Jake Petroules)
* Replace typedef enums with typedef NS_ENUM to make Swift happier (C.W. Betts)
* Fix warnings under Xcode 6.1 relating the SUUpdateAlert XIB (Tony Arnold)
* Prefer string constants to strings (Jake Petroules)
* Use Info.plist keys instead of macros (Jake Petroules)
* Only export public symbols. (Jake Petroules)
* BinaryDelta: avoid crash with bad paths (Jake Petroules)
* Fixing Swedish translations (Erik Vikström)
* Turkish localization fixes (Emir)
* Proofing of Ukrainian localization (Vera Tkachenko)

# 1.8.0 (Jul 26, 2014)

* New SUDSAVerifier based on up-to-date macOS APIs (Zachary Waldowski)
* Detailed error log for failed signature checks (Kornel Lesiński)
* Converted Sparkle to ARC (C.W. Betts)
* Converted ivars to properties. (Jake Petroules)
* Cocoapod support (Xhacker Liu)
* Quarantine removal on macOS 10.10 (C.W. Betts)
* Updated Japanese localization (1024jp)
* Added Greek localization

# 1.7.1 (Jul 2, 2014)

* Removed option to install unverified updates (Kornel Lesiński)
* Added detailed log when code signing verification fails (Sam Deane)
* Restored original Sparkle icon. (Jake Petroules)
* Switched SUUpdateAlert.xib to AutoLayout (Kornel Lesiński)
* Replace references to andymatuschak.org with sparkle-project.org. (Jake Petroules)
* Several code cleanups, modernizations, fixed warnings and improved code formatting (Jake Petroules)
* Make the repository significantly more organized. (Jake Petroules)
* Xcode project: set organization name and class prefix. (Jake Petroules)
* Link to Foundation and AppKit instead of Cocoa. (Jake Petroules)
* Use new operatingSystemVersion API when available. (Jake Petroules)
* Add .clang-format configuration file for source code formatting. (Jake Petroules)
* Add a target to build Sparkle API documentation using Doxygen. (Jake Petroules)

# 1.7.0

* Dropped support for macOS 10.6. Sparkle now supports 10.7 and newer (including 10.10 Yosemite) on 64-bit Intel Macs (the last 32-bit Mac was released in 2006).
* Removed use of deprecated functions (Zachary Waldowski)
* Switched to modern Obj-C runtime and new literals syntax
* Removed pre-10.7 code. (C.W. Betts)
* Use more Blocks/libdispatch code. (C.W. Betts)
* Cleaned up and improved security of `generate_keys`/`sign_update` scripts

# 1.6.1

* Removed archive password prompt (Kornel Lesiński)
* (Re)fixes bug where URLs are naively double escaped (Andrew Madsen)
* Fixed typo that caused crashes in BinaryDelta (Tamas Lustyik)
* SUStandardVersionComparator.h is public (Vincent CARLIER)
* Remove pre-10.6-specific code. (C.W. Betts)
* Objective C 2 getters and setters. (C.W. Betts)
* Define correct dependencies on locale scripts (Antonin Hildebrand)

# 1.6.0

* Cleaned up and deleted redundant strings files (Kornel Lesiński)
* Modern Objective C syntax, properties where possible. (C.W. Betts)
* Make SUAppcastDelegate a formal protocol. (C.W. Betts)
* Fixed default font in release notes WebView (Kornel Lesiński)
* Configurable name for finish_installation.app (Kornel Lesiński)
* Removed code for 10.4 (Kornel Lesiński)
* Convert all strings files to UTF-8 (UTF-16 must die) (Kornel Lesiński)
* Removing GC target (Matt Thomas)
* finish_installation.app and pkg files will not removed when we use *.pkg installer and restart system in the installer (Takayama Fumihiko)
* Select Korean and Slovak for Sparkle.strings localization (Shon Frazier)
* Updated the Romanian translation (Gabe)
* pt-BR localization polishing (BR Lingo)
* update zh_CN (61)
* Shut up some warnings & make build with newer Xcode (Uli Kusterer)
* Less unsafety with format strings (Uli Kusterer)
* New icon (Rick Fillion)
* fixed a 'content rectangle not entirely onscreen' warning (Simone Manganelli)
* updated sends system profile to use info.plist if user defaults key isn't present (Jamie Pinkham)
* Support for notifications on some updater events (Doug Russell)
* Allow the delegate to trigger a silent install and relaunch (Matt Stevens)
* Support silent relaunches (Matt Stevens)
* Increment the sudden termination counter if installing on quit (Matt Stevens)
* Prompts the user to update after a week (rather than a day) if he doesn't quit the app (Andy Matuschak)
* Adding <sparkle:tags> appcast item element, <sparkle:criticalUpdate /> tag (Andy Matuschak)
* We have this check box that says "Automatically download and install updates in the future." But we only download them automatically. We still ask permission again before installing them. (Andy Matuschak)

# 1.5.0-beta6

* Important Changes
 * Sparkle now requires DSA signatures on your updates. Check the documentation for more information on how to set that up if you don't already sign your updates. You can bypass this requirement if you deliver both your appcast and your updates over SSL.
* Sparkle will no longer display release notes located at file:// URLs, since Javascript on such a page would be able to read files on your file system.
* For security reasons, Sparkle will refuse to install updates which appear to "downgrade" the app.
* SUUpdater now implements new keys: "automaticallyDownloadsUpdates", "lastUpdateCheckDate", and "sendsSystemProfile."
* Fixed a bug that could prevent SUProbingUpdateDriver from working.
* Fixed a bug that prevented the updaterWillRelaunchApplication: delegate method from getting called.
* Fixed displaying release notes transmitted "loose" in the <description> key.
* Fixed Sparkle compilation on 10.4 systems.
* Fixed a bug that could cause window confusion if an app changed its LSUIElement at runtime.
* Added support for Sparkle 1.1's behavior of disabling updates when the check interval is 0.
* Sparkle can now handle appending parameters to URLs which already have parameters.
* If an update's sparkle:shortVersionString is the same as the host's CFBundleShortVersionString, the sparkle:version and CFBundleVersion will be presented in parentheticals.

# 1.5.0-beta5

* Important Changes!
 * Made every Sparkle class private except for SUUpdater, SUAppcast, SUAppcastItem, and the SUVersionComparisonProtocol.
 * There is now a single SUUpdater singleton for every host bundle; instead of -[SUUpdater setHostBundle], you can use +[SUUpdater updaterForBundle].
 * Redefined the (entire) delegate protocol accordingly.
 * Renamed -[SUUpdater updatePreferencesChanged] to -[SUUpdater resetUpdateCycle]. This provides better semantics for non-apps, which need to start the update cycle manually.
 * -[SUUpdater checkForUpdatesWithDriver] is private. If you were using SUProbingUpdateDriver, you can now use -[SUUpdater checkForUpdateInformation] for a similar effect.
 * All the user defaults keys are now private; instead, SUUpdater is KVC-compliant for automaticallyChecksForUpdates, updateCheckInterval, and feedURL.
* Reduced the size of the English-only framework by 25%.
* System profiling information is now only submitted to the server once per week; this will help normalize your statistics across users with different interval preferences.
* The feedParametersForUpdater: delegate method now requires "displayKey" and "displayVersion" keys so that it can inform the user what's being sent.
* Added a delegate method called pathToRelaunchForUpdater: which can be used for plugins to provide the path which should be used when relaunching the client after installing an update.
* Added support for xml:lang to pick localized nodes in appcasts (for release notes, etc).
* Fixed a bug which would cause the "checking for updates" window to not disappear in certain extraordinary error conditions.
* Fixed a DSA signature checking bug for .tar.gz archives.
* Sparkle now refuses to update on any read-only volume, not just dmgs.
* Sparkle will clean up the host app's name and version before sending it as the user agent string; some non-ASCII characters were causing problems.
* Added an Italian localization courtesy Michele Longhi.
* Added a Swedish localization courtesy Daniel Bergman.
* Fixes to the French localization courtesy Ronald Leroux and Yann Ricqueberg.
* Fixes to the German localization courtesy Sven-S. Porst.
* Fixes to the Russian localization courtesy Alexander Bykov and Anton Sotkov.
* Fixed a number of issues related to archive format detection: I reverted back to extensions from UTIs.
* Focus behavior fixes for LSUIElement apps.
* The status window progress bar now animates even when indeterminate.
* Major refactorings to improve functionality for non-app bundles.

# 1.5.0-beta4

* Fixed a critical bug which prevented non-.dmgs from unarchiving properly.
* Added reporting of 64-bit capability to the profiling system.

# 1.5.0-beta3

* Added a new delegate method to SUUpdater.h to allow delegates to specify custom version comparators.
* Added a German localization, courtesy the Camino localizer team: Dominik Tobschall, Tobias Stohr, and Friedemann Bochow.
* Bug fixes:
 * Fixed a serious bug which could cause a server to be DDoS'd (or the host app to crash!) if an appcast fails to be parsed.
 * Fixed .tbz extraction if the archive was made with Stuffit.
 * Fixed support for .tar.bz2 and .tar.gz; Sparkle has to assume the archive is a tar when it sees "bz2" and "gz"; don't use those without tarring.
 * Fixed a typo which caused the shouldPromptForPermissionToCheckForUpdatesToHostBundle: method to not work in 1.5b2.
 * Fixed .zip extraction on Tiger (Apple changed the UTI between releases)
 * Fixed a crasher on Tiger.
 * Fixed display of the default app icon when the host app doesn't have an icon.
 * Sparkle now displays a sensible progress string and uses an indeterminate progress bar when the server doesn't report a file size.
 * Fixed some memory leaks.

 # 1.5.0-beta2

 * Compatibility Issues:
 * Most of the delegate method selectors have changed to scale better. See SUUpdater.h for changes; you'll likely have to make changes if you implement any delegate methods.
 * If you're using .tar.gz or .tar.bz2 archives, name them ".tbz" or ".tgz" instead; Sparkle now uses UTIs for archive detection, and it's not  smart about double extensions.
 * I'm no longer supporting 10.3. This may or may not work on Panther—probably not.
 * Sparkle's no longer built for ppc64 by default. If you want to ship that, feel free to build your own, but this saves a few hundred k.
* Enhancements:
 * Sparkle now detects if the preferences for automatic update checks or the time interval change mid-cycle.
 * If your product is a non-.app, you need to clue Sparkle in on the change by calling [[SUUpdater sharedUpdater] updatePreferencesChanged].
 * Added a cancel to the "checking for updates..." dialog.
 * Sparkle now cleans up all its litter in /tmp.
 * Made SUUpdater's delegate an IBOutlet so you can hook it up in IB.
* Bug fixes:
 * Sparkle no longer crashes on non-GC hosts when the user cancels an update's downloads.
 * Sparkle no longer gets stuck in an inconsistent state or crashes when it can't parse the appcast on scheduled updates.
 * Added the sharedUpdater method to SUUpdater, as it should have been.
 * Fixed a bug where the "checking for updates..." window wouldn't go away if an error occurs while checking for updates.
 * Made the dual-mode build configuration actually use the .xcconfig which builds it with GC support. (oops!)
 * Fixed relaunching for prefpanes.
 * Sparkle no longer fails to install updates on Snow Leopard (though there's still an issue with trashing the old version of the app, but it seems to be a 10.6 bug)
 * Sparkle now handles redirects correctly under Tiger.
 * Fixed the installation path for non-.app bundles.
 * Fixed a bug which could crash Sparkle under non-English locales.
 * Fixed a weird race condition which could cause the relaunch tool to never notice that its target relaunched.
 * Fixed a bug where if the host app is inactive when an update occurs, the update alert sometimes doesn't become key.
 * Minor textual fixes.
* Localizations:
 * Dutch: Maarten Van Coile
 * French: Yann Ricquebourg
 * Spanish: Ernesto Gomez Cereijo

 # 1.5.0-beta1

 * The most important things to know:
 * The 10.3 support is untested at best; sketchy at worst. Test with it thoroughly before you use it.
 * Sparkle now asks for permission to update on second launch; don't be surprised at that. You can change that behavior with a delegate method; read SUUpdater.h for more info.
 * We no longer distinguish between "check on startup" and "scheduled updates"; everything is scheduled, with the default being every day.
 * The test application is using the new profiling features, but that's only for demonstration: these are off by default. More on this later.
 * There are no localizations yet.
* New features:
 * Sparkle now supports .pkgs. Just name the .pkg the name of the app and put in the update archive.
 * Sparkle now sends optional demographic profiling information; set SUEnableSystemProfiling to YES in your Info.plist and check out the GET data sent to your webserver when fetching the appcast. More on this in the documentation. The test application has this on so you can see the behavior.
 * Sparkle now supports updating non-.apps. Just call -setHostBundle: on the global SUUpdater to let it know what you're trying to update.
 * Sparkle now supports garbage collection in the host app. Use "Sparkle-with-GC.framework" for that, but be aware it's 10.5-only.
 * Sparkle is now 64-bit compatible, compiling both ppc64 and x86_64.
 * Sparkle now supports a sparkle:minimumSystemVersion key you can set on appcast items. It does what you think it does.
 * Sparkle now checks to see if the host app is running from a disk image and refuses to update if it is. (10.4+ only)
 * Added support for entities in enclosure paths.
 * The file size output is now formatted prettily.
 * Sparkle now gives visual indication that it's checking for updates when the update's user initiated. ie: it pops up a status controller saying "checking for updates..."
 * Added support for an SUPublicDSAKeyFile, so people don't have to copy/paste their entire key into their Info.plist. Set this key in your Info.plist to the filename of the key in your Resources directory.
 * Added an actually maintainable codebase.
* Changes:
 * Sparkle version comparison is now dramatically less stupid and verified by a bunch of unit tests. If something doesn't work the way you think it should, add a test to SUVersionComparisonTest.m
 * Added a minimum to the check interval so that developers don't accidentally release their apps into the wild with 60-second test check intervals and have DOS-attack-like results. It's an hour now for release mode; feel free to change it.
 * The relaunching process now uses a separate helper app, which is a much more robust method.
 * Changed CFBundleShortVersionString behavior: Sparkle no longer uses Apple's about box style of displaying ShortVersionString (CFBundleVersion) when the latter is available.
 * No more MD5 checking. Use DSA: it's actually secure.
 * The abomination that was SUStatusChecker is dead. Use SUProbingUpdateDriver instead.
* Bugfixes:
 * Fixed a huge bug with fully-automatic updating: before, if the user chose to relaunch later, the app would be running from the trash for a while. Now the buttons are "install and relaunch" or "install later."
 * Sparkle forces Spotlight to reindex the updated app so that it won't keep pointing to the one in the trash.
 * Sparkle trims whitespace from around DSA signatures; this could cause crashes before.
 * Fixed a bug where the user choosing to skip a version would inhibit future automatic updates until the next launch.
 * Fixed a bug that could occur when the app has a localized CFBundleName.
 * .dmgs now work on Leopard.
 * The status controller's button now sizes appropriately to the localization.
 * Sparkle now works correctly with LSUIElement apps: it focuses them before displaying the update alert.
 * Sparkle now deletes failed partial downloads.
 * The update alert no longer floats above everything in the app.
 * Fixed varied and sundry memory leaks.
* A ton of other things that I've forgotten or were too small to mention!

# 1.1

* Optimized framework size: now only 1.4mb with all localizations and 384kb with only English (an English-only version is in the Extras folder).
* Added a new SUStatusChecker class for programmatically determining if a new version is available (see the docs); thanks, Evan Schoenberg!
* Added support for apps using SIGCHLD; thanks, Augie Fackler!
* Added a zh_CN update from JT Lee
* Added a Polish update from Piotr Chylinski
* Fixed DMG support for images with /Applications symlinks.
* Fixed a really stupid interval-checking bug that could cause repeated hits to the appcast.
* Fixed a bug where the check interval would be inconsistent if a value of 0 was stored in the user defaults.

# 1.0

* Additions:
 * Added real version comparison courtesy Kevin Ballard: Sparkle now knows that 0.89 < 1.0a3 < 1.0.
 * Added many localizations courtesy David Kocher's localization team.
 * Added a much better installation mechanism courtesy Allan Odgaard.
 * Added a user agent string to the RSS fetch request.
 * Added support for CFBundleShortVersionString in addition to CFBundleVersion, and support for a sparkle:shortVersionString attribute on the enclosure.
 * Added support for CFBundleDisplayName if available.
* Changes:
 * Automatic updating is now allowed by default, but only if DSA signing is on.
 * Pressing Escape or closing the update alert now reminds the user later.
 * Now when there's a stored check interval, Sparkle doesn't check immediately on startup the first time the app is launched because the user hasn't consented to it yet.
 * The update alert now remembers its size and floats.
* Bug Fixes:
 * Fixed installation of DMGs with multiple files enclosed.
 * Fixed a nasty memory leak.
 * Fixed a bug wherein having no value for allowing automatic updates would display a checkbox for the updates but would not honor it.
 * Fixed a bug in zip extraction that occurred in Panther.
 * Fixed release notes caching.
 * Fixed a bug wherein Sparkle refused to authenticate the installation if the user had cancelled authentication previously in that session.
 * Fixed a weird bug that would cause a second help menu to appear on first launch.
 * Fixed a bug that could occur when changing the scheduled check interval.
 * Fixed a bug wherein the host app could crash if the user clicked Remind Me Later before the release notes finished loading.
 * Fixed a bug wherein the behavior was undefined if the user manually initiated a check when an automatic one was already taking place.
 * Fixed wrapping on the description field in the update alert.

# 1.0-beta3

* Fixed a nasty crasher that occurred often when the user was not connected to the internet.

# 1.0-beta2

* Major Improvements:
 * Fully automatic updating! (see the Documentation: this is beta and off by default)
 * Added support for DSA signatures (see the Documentation).
 * Added support for MD5 sum verification.
 * Added Security.framework-based authentication for installing to privileged directories.
 * Huge refactoring of the codebase: there's now a Sparkle Xcode project, Sparkle is now a framework, and everything is modular / abstracted. And no more code-generated interface.
* Minor Improvements:
 * A SUUpdaterWillRestartNotification is sent out before restarting now.
 * Added key equivalents to alert panel buttons.
 * Error handling is much prettier now: technical messages are not presented to the user anymore.
 * There's now a test app for developers to see what Sparkle's like before using it.
 * Wrote new, pretty, extremely thorough documentation.
* Bug Fixes:
 * Relaunch behavior is much improved and shouldn't fail in huge apps anymore.
 * Fixed a bug wherein a failing tar command could crash the host app.
 * Sparkle now looks at InfoPlist.strings in addition to Info.plist.
 * Fixed some stupid typos.

# 1.0-beta1

* Major New Features:
 * Sparkle now supports scheduled periodic updates—read the Readme for information on how to use it.
 * Sparkle now supports WebKit-based release notes (for CSS and full HTML), which it displays in the main update alert, not a separate panel. The Readme has much more information. Sparkle will, of course, fall back on NSTextView if the host app does not include WebKit.
* Minor New Features:
 * Added support for .zip update archives.
 * Added support for .dmg update archives.
 * Implemented Remind Me Later to replace simple update cancellation.
 * Implemented Skip This Version functionality.
 * Added support for multiple feeds via the user defaults SUFeedURL key taking precedent over the one in Info.plist.
 * Added support for Sparkle's custom XML namespace, which is optional but may prove useful. See the Readme for more information.
* Bug Fixes:
 * Sparkle will no longer enter an inconsistent state if the user tries to update again while one is already in progress.
 * Sparkle now uses CFBundleName to determine the application's name instead of the app's filename.
 * Sparkle no longer crashes if the user cancels during extraction.
 * Lots of code refactoring.

# 0.1

* Initial Release
