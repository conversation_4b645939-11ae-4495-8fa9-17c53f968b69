#ifndef __IAPPUPDATEINTERFACE__H__
#define __IAPPUPDATEINTERFACE__H__

#ifdef WIN32
    #define PW_OS_WIN	
#elif defined __ANDROID__
    #define PW_OS_ANDROID
#elif defined OHOS 
    #define PW_OS_OHOS
#elif defined __linux__
    #define PW_OS_LINUX
#elif defined __APPLE__
    #ifndef PW_OS_IOS
        #include <TargetConditionals.h>
        #if (TARGET_OS_IOS || TARGET_OS_SIMULATOR)		// only support iOS phone and simulator.
            #if TARGET_OS_MACCATALYST
                #ifndef PW_OS_MAC
                    #define PW_OS_MAC
                #endif
            #else 
                #define PW_OS_IOS                
            #endif 
        #endif

        #if TARGET_OS_OSX
            #ifndef PW_OS_MAC
                #define PW_OS_MAC
            #endif
        #endif
    #endif
#elif (defined(__ORBIS__)|defined( __PROSPERO__))
#define PW_OS_PS 1
#else
#error current platform unsupported!
#endif

#ifdef PW_OS_WIN
	#define STDCALL __stdcall
#else
    #define STDCALL
#endif

#if (defined(PW_OS_WIN) | defined(PW_OS_PS))
    #ifdef PATCHERAPI_EXPORTS
        #define APPUPDATEAPI __declspec(dllexport)
    #else
        #define APPUPDATEAPI __declspec(dllimport)
    #endif
#else
	#define APPUPDATEAPI __attribute__((visibility ("default")))
#endif

#include <stdint.h>

#define  PW_PATCHER_GAMENAME_LEN 128
#define  PW_PATCHER_PATH_LEN 512
#define  PW_PATCHER_VERSION_LEN 64
#define  PW_PATCHER_UID_LEN  64
#define  PW_PATCHER_DEVICEID_LEN 128
#define  PW_PATCHER_APPID_LEN 128
#define  PW_PATCHER_URL_LEN   256
#define  PW_PATCHER_BACKUP_URLS_LEN (256*3)
#define  PW_PATCHER_URL_SUBPATH 128
#define  PW_PATCHER_TAGNAME_LEN 256

#pragma pack(push, 1)

typedef struct _VersionInfo {
	char newestResVersion[PW_PATCHER_VERSION_LEN];
	char newestAppVersion[PW_PATCHER_VERSION_LEN];
    char minResVersion[PW_PATCHER_VERSION_LEN];             // 最新版本资源支持的建议最低升级版本
	bool needUpdateResource;
	bool needUpdateApp;
	char versionDescrition[1024];                           // 存储描述信息
} VersionInfo;

typedef struct _InitInfo {

	bool openDebugLog;          
	bool openErrorLog;

    uint8_t logKeepDays;                              // SDK生成的日志的保存时间，单位：天

    char gameName[PW_PATCHER_GAMENAME_LEN];             // 必须使用英文名
	char updateUrl[PW_PATCHER_URL_LEN];					// 主更新源
	char backupUpdateUrl[PW_PATCHER_BACKUP_URLS_LEN];	// 备用更新源，可能是以;分隔的多个源
	char appVersion[PW_PATCHER_VERSION_LEN];			// 本地App版本
	char resourceVersion[PW_PATCHER_VERSION_LEN];		// 本地资源版本
	char uid[PW_PATCHER_UID_LEN];                       // reserved
	char deviceId[PW_PATCHER_DEVICEID_LEN];             // reserved
	char appID[PW_PATCHER_APPID_LEN];                   // reserved
    char serverListUrl[PW_PATCHER_BACKUP_URLS_LEN];     // serverlist url 用；分隔多个
    bool testResource;                                  // 测试资源
    char testResversion[PW_PATCHER_VERSION_LEN];
    char mediaId[PW_PATCHER_DEVICEID_LEN];              // mid
}InitInfo;

typedef struct _PathInfo {
	char resPath[PW_PATCHER_PATH_LEN];			        // 游戏资源目录
    char patcherPath[PW_PATCHER_PATH_LEN];              // Patcher工作目录
    char apkPath[PW_PATCHER_PATH_LEN];					// reserved
    char serverListPath[PW_PATCHER_PATH_LEN];           // serverList Path
    char logPath[PW_PATCHER_PATH_LEN];                  // LogDir
}PathInfo;


enum PatcherTagStatus
{
    PatcherTag_UnKnown = 0,
    PatcherTag_NeedUpdate = 1,
    PatcherTag_Completed = 2,
};

enum PatcherTagTaskStatus
{
    PatcherTagTask_NULL = -1,
    PatcherTagTask_Created = 0,
    PatcherTagTask_Check = 1,
    PatcherTagTask_WaitConfirm = 2,
    PatcherTagTask_Download = 3,
    PatcherTagTask_Fix = 4,
    PatcherTagTask_Finish = 5,
};


typedef struct _PatcherResrouceInfo {
    char tag[PW_PATCHER_TAGNAME_LEN];
    int64_t totalSize;
    int resStatus;          // PatcherTagStatus 
    int taskStatus;         // PatcherTagTaskStatus
    int64_t currentSize;    // 当前已完成, -1 为未知
    char localVersion[PW_PATCHER_VERSION_LEN];
    char remoteVersion[PW_PATCHER_VERSION_LEN];
    int64_t needSpace;
} PatcherResrouceInfo;

#pragma pack(pop)


/* 版本检查结果 */
enum PatcherVerCheck : uint32_t
{
    kNoUpdate,                          // 不需要更新
    kAppUpdate,                         // 需要更新App
    kResUpdate,                         // 可以进行资源更新
    kException,                         // 出现异常，不可继续更新。VersionInfo结构体中的versionDescrition存储异常原因
};

/*  状态定义
*   除了初始状态，其他状态都会触发OnStateStart和OnStateEnd回调函数，
*   并且部分状态在未发生错误情况下还会触发状态特有的回调函数。
*/
enum PatcherState : uint32_t
{
    kStateInit = 0,                         // 初始状态；辅助，该状态不触发任何回调函数
    kStateVersionCheck = 1,                 // 版本检查；触发OnVersionCheck
    kStateLocalResCheck = 2,                // 本地资源校验；
    kStateResRetrieve = 3,                  // 检索需更新的资源；触发OnPreDownload
    kStateResDownload = 4,                  // 下载资源包（包括补丁包）；触发OnDownload
    kStateResFix = 5,                       // 资源处理（更新，移动，替换，删除等）；触发OnFixResource    

    kStateUpdateFinished = 10,
    
    kStateTagResDownload = 11,
    kStateTagResDownloadFinished = 12,
    kStatePreReleaseDownload = 13,
    kStatePreReleaseDownloadFinished = 14,

    kStateUnInit = 100,
    kStateError = 101,
};

enum PatcherFileOper: int32_t
{
    PatcherFileOper_Null = 0,
    PatcherFileOper_Add = 1,
    PatcherFileOper_Update = 2,
    PatcherFileOper_Delete = 3,
};

#include "patcher_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/* 版本检查结果回调 */
typedef void (*OnVersionCheck)(PatcherVerCheck result, const VersionInfo* versionInfo);

/* 需要下载的字节数, 更新需要的磁盘空间, 当前磁盘剩余空间 */
typedef void (*OnPreDownload)(uint64_t will_download_bytes, uint64_t update_need_space_bytes, uint64_t free_diskspace_bytes);

/* 下载信息  totalsize: 下载字节总量；进度progress: 四位小数；speed: 单位byte/s */
typedef void (*OnDownload)(uint64_t totalsize, double progress, uint64_t speed);

/* 资源文件处理进度 */
typedef void (*OnFixResource)(uint32_t totalcount, uint32_t remaincount, PatcherFileOper file_oper, const char* current_fixed_file);

/* 资源单个文件开始处理回调 */
typedef void (*OnFixResourceFileStart)(int64_t totalResouceByteSize, int64_t totalFixedResourceSize, int64_t CurrentResourceTotalSize, PatcherFileOper file_oper, const char* current_file);

/* 状态触发回调 */
typedef void (*OnStateStart)(PatcherState state);

/* 状态结束回调 */
typedef void (*OnStateEnd)(PatcherState state);

/* 流程结束回调，必触发 */
typedef void (*OnUpdateFinish)(PatcherErr result);

/* 错误上报,打点 */
typedef void (*OnCheckPoint)(const char* key, const char* hint);

/* 清理资源, 结果*/
typedef void (*OnClearResCompleted)(int result, const char* desc);

/* 检查本地资源, 进度 */
typedef void (*OnLocalResCheckProgress)(int totalFileCount, int checkedFileCount, int64_t totalbytes, int64_t checkedBytes, const char* currentResName, int64_t currentFileSize, int64_t currentCheckedSize);

/* 检查需要下载的资源, 进度 */
typedef void (*OnDownloadResCheckProgress)(int totalFileCount, int checkedFileCount, int64_t totalbytes, int64_t checkedBytes, const char* currentResName, int64_t currentFileSize, int64_t currentCheckedSize);

/* 弱网回调*/
typedef void (*OnLowSpeedNotify)(const char* currentCDN, int64_t currentSpeedBytesPerSecond, const char* reverse);

/* 预下载回调*/
typedef void(*OnPreReleaseCheck)(bool hasPreRelease,  const char* preReleaseBranch, const char* preReleaseResVersion);

/* 需要下载的(必要资源,所有资源)字节数, 更新需要的(必要资源,所有资源)磁盘空间, 当前磁盘剩余空间*/
// baseBytes 基础i资源总大小
// will_download_baseBytes 需要下载的基础资源
// update_need_space_baseBytes 基础资源下载需要的空间,
// AllBytes 所有资源大小
// will_download_AllBytes 需要下载的所有资源大小
// update_need_space_AllBytes 下载所有资源需要的空间
// free_diskspace_bytes 剩余空间
typedef void (*OnPreDownloadWithTags)(uint64_t baseBytes, uint64_t will_download_baseBytes, uint64_t update_need_space_baseBytes, uint64_t AllBytes, uint64_t will_download_AllBytes,  uint64_t update_need_space_AllBytes, uint64_t free_diskspace_bytes);

// totalPackage 资源包个数
// currentPackage 已整理的个数
// totalBytes 总字节数
// currentBytes 当前整理字节数
typedef void (*OnApplyPlayAssetsProgress)(int totalPackage, int currentPackage, int64_t totalBytes, int64_t currentBytes);

#ifdef __cplusplus
}
#endif

typedef struct _PatcherCallback
{
    OnVersionCheck  versionCallback;
    OnPreDownload   predownloadlCallback;
    OnDownload      downloadCallback;
    OnFixResource   fixResCallback;
    OnStateStart    stateBeginCallback;
    OnStateEnd      stateEndCallback;
    OnUpdateFinish  finishCallback;
    OnFixResourceFileStart fixResFileStart;
    OnLocalResCheckProgress localresCheckProgress;
    OnDownloadResCheckProgress downloadresCheckProgress;
    OnLowSpeedNotify lowSpeedNotify;
    OnPreReleaseCheck preReleaseCheckCallback;
    OnPreDownloadWithTags PreDownloadWithTagsCallback;
    OnApplyPlayAssetsProgress applyPlayAssetsProgressCallback;
}PatcherCallback;

#ifdef __cplusplus
extern "C" {
#endif

/*!
* function: SDK Init
* success：0 
* failed：less then 0
*/
APPUPDATEAPI int pw_patcher_init(const InitInfo* initInfo, const PathInfo* pathInfo, const PatcherCallback* callbacks);


// init with json file.
/* {
//    "appVersion": "1.0.0",
//    "minVersion" : "0.0.1",
//    "gameResUrl" : ["https://baidu.com"] ,
//    "gameGetServerListUrl" : ["https://baidu.com"] ,
//    "gameUpdateAssetUrl" : ["https://baidu.com"]
//}

    log_level: 0
*/

APPUPDATEAPI int pw_patcher_init_fromJsonFile(const char* config_path, int log_level /*= 0*/, const PathInfo* pathInfo, const PatcherCallback* callbacks, const char* deviceId = (const char*)0, const char* mediaId = (const char*)0);

APPUPDATEAPI int pw_patcher_init_fromJsonContent(const char* config_json_content, int log_level /*= 0*/, const PathInfo* pathInfo, const PatcherCallback* callbacks, const char* deviceId = (const char*)0, const char* mediaId = (const char*)0);

/*! 
* function: 驱动回调获取结果，每执行一次，会提取当前回调队列中所有的回调
* description: 必须在pw_patcher_init完成后调用
*/

APPUPDATEAPI int pw_patcher_runCallbacks();

/*!
* function: 开始更新
* description: 该方法包括版本检查、资源计算；
    资源计算完成之后，SDK内部线程阻塞，等待外部调用pw_patcher_doUpdate来确认是否下载资源。
*/
/*!
* function: 开始更新
* description: 修改流程，不使用confirm确认，此接口默认下载base tag resource
*/
APPUPDATEAPI int pw_patcher_doUpdate();

/*!
* function: 开始更新, 强制检查所有资源
*/
APPUPDATEAPI int pw_patcher_forceUpdate();


/*!
* function 资源下载确认，在回调函数OnPreDownload产生的情况下，才需要调用此方法；
* description 该方法包括资源更新、资源合并、校验；
* parameter:
    is_continue 是否继续更新，如果为false,更新流程会立即结束，错误码返回kErrCancel
*/

APPUPDATEAPI int pw_patcher_continueConfirm(bool is_continue);


enum ContinueType
{
    Continue_UnKnown = -1,
    Continue_Cancel = 0,
    Continue_BaseResouce = 1,
    Continue_AllResource = 2,   
};

APPUPDATEAPI int pw_patcher_continueConfirmWithTags(ContinueType continue_type);

/*!
* function: 取消更新
* description: 该函数可取消检查更新和更新资源操作，
    取消成功后回调函数OnUpdateFinish的错误码将返回kErrCancel
*/
APPUPDATEAPI void pw_patcher_cancelUpdate();

/*!
* function: SDK反初始化
*/
APPUPDATEAPI void pw_patcher_uninit();

/*
    function 设置埋点回调
    这个回调也通过pw_patcher_runCallbacks触发
 */
APPUPDATEAPI void pw_patcher_set_checkpoint_callback(OnCheckPoint cb);
/*
 function 清理缓存, 删除所有当前配置下资源,缓存文件及配置
 执行后调用OnClearResCompleted 回调, 这个回调也通过pw_patcher_runCallbacks驱动
 */
APPUPDATEAPI void pw_patcher_clear(OnClearResCompleted callback);


APPUPDATEAPI const char* pw_patcher_get_serverlist_file();

typedef void(*OnServerListContent)(int code, const char* desc, const char* filepath);
APPUPDATEAPI void pw_patcher_refresh_serverlist(OnServerListContent cb, bool async);

// 获取下载线路列表, 用分号";" 隔开
APPUPDATEAPI const char* pw_patcher_get_cdn_list();

// 设置下载线路
APPUPDATEAPI int pw_patcher_set_cdn(const char* url);


/* tag 资源检查结果 */
typedef void(*OnTagResourceList)(int code, const char* msg, const PatcherResrouceInfo* resultArray, int resultSize, void* ctx);

/* tag 资源检查进度 */
typedef void(*OnTagResourceCheckProgress)(int checkedTagCount, int remainTagCount, int64_t totalBytes, int64_t checkedBytes, const char* currentTag, int64_t currentTagTotalBytes, int64_t curretTagHasChekedBytes, void* ctx);


#define PatcherTagUpdateTask void

/* tag 资源本地检查 */
typedef void(*OnTagResourceTaskCheckProgress)(PatcherTagUpdateTask* task, int64_t total, int64_t checkedBytes, void* ctx);

/* tag 资源准备下载进度 */
typedef void(*OnTagResourceTaskPreDownload)(PatcherTagUpdateTask* task, int64_t total, int64_t update_need_space_bytes, int64_t free_diskspace_bytes, void* ctx);

/* tag 资源准备下载进度 */
typedef void(*OnTagResourceTaskDownload)(PatcherTagUpdateTask* task, uint64_t totalsize, double progress, uint64_t speed, void* ctx);

/* tag 资源移动到指定目录 */
typedef void(*OnTagResourceTaskFixProgress)(PatcherTagUpdateTask* task, int totalfileCount, int remainFileCount, int64_t totalBytes, int64_t fixedBytes, const char* fileName, int64_t currentFileTotalBytes, int64_t curretFileFixedBytes, void* ctx);

/* tag 资源下载任务结束 */
typedef void(*OnTagResourceTaskUpdateFinish)(PatcherTagUpdateTask* task, PatcherErr result, void* ctx);


struct PatcherTagResourceUpdateCallback
{
    OnTagResourceTaskCheckProgress checkProgress;   
    OnTagResourceTaskPreDownload preDownloadCallback; // 调用pw_patcher_tag_create_updateTask后, 如果有需要下载的资源, 就会触发, 触发后可选择继续下载还是取消
    OnTagResourceTaskDownload downloadCallback;       // 下载进度
    OnTagResourceTaskFixProgress fixCallback;         // 下载任务完成后, 资源整合
    OnTagResourceTaskUpdateFinish finishCallback;     // 任务结束
};

// 获取tag列表
APPUPDATEAPI void pw_patcher_tag_get_taglist(OnTagResourceList cb, void* ctx);

// 检查tag状态
APPUPDATEAPI int pw_patcher_tag_check_resource(const char* tags, OnTagResourceList checkCallback, OnTagResourceCheckProgress checkProgress, void* ctx);

// 获取当前进行中的tag
APPUPDATEAPI void pw_patcher_tag_get_tasklist(OnTagResourceList cb, void* ctx);

// 用tagname 获取task对象
APPUPDATEAPI PatcherTagUpdateTask* pw_patcher_tag_get_task_byTagName(const char* tag);

// 任务的tag
APPUPDATEAPI const char* pw_patcher_tag_get_tagName(PatcherTagUpdateTask* task);

// 创建下载tag任务
APPUPDATEAPI PatcherTagUpdateTask* pw_patcher_tag_create_updateTask(const char* tag, PatcherTagResourceUpdateCallback* cb, bool autostart = false, int maxThreads = 0, int64_t maxBytesPerSecond = 0, void* ctx = (void*)0);

// 销毁下载tag任务
APPUPDATEAPI int pw_patcher_tag_destroy_updateTask(PatcherTagUpdateTask* task);

// 开始任务
APPUPDATEAPI int pw_patcher_tag_start_update(PatcherTagUpdateTask* task);

// 确定继续下载
APPUPDATEAPI int pw_patcher_tag_confirm_update(PatcherTagUpdateTask* task, bool doNext = true);

// 取消下载
APPUPDATEAPI int pw_patcher_tag_cancel_update(PatcherTagUpdateTask* task);


// 下载文件
typedef void(*PatcherDownloadResult)(int code, const char* url, const char* message, const char* localpath, void* ctx);
typedef void(*PatcherDownloadProgress)(const char* url, const char* localDir, const char* name, int64_t totalBytes, int64_t currentBytes, void* ctx);
APPUPDATEAPI int pw_patcher_request_downloadFile(const char* url, const char* localDir, const char* name, PatcherDownloadResult cbResult, PatcherDownloadProgress cbProgress, void* ctx);
APPUPDATEAPI int pw_patcher_request_cancel_download(const char* url, const char* localDir, const char* name);

// 预下载
typedef void(*PatcherPreReleaseResult)(PatcherErr code, const char* message, void* ctx);
typedef void(*PatcherPreReleaseProgress)(int64_t totalBytes, double progress, int64_t speed, void* ctx);
typedef void(*PatcherPreReleaseFixProgress)(int64_t totalBytes, int64_t fixedbytes, void* ctx);

APPUPDATEAPI int pw_patcher_preRelease_request(PatcherPreReleaseResult cbResult, PatcherPreReleaseProgress cbProgress, void* ctx, PatcherPreReleaseFixProgress fixProgress = nullptr);
APPUPDATEAPI int pw_patcher_preRelease_cancel();

// code:  0: 预下载完成, -1: 没有预下载的内容 1: 预下载内容未完成
// totalBytes 总大小
// needDownloadBytes 需要下载的大小
// needSpace 需要的空间
enum PreReleaseStatus 
{
    PreReleaseNone = -1,
    PreReleaseCompletd = 0,
    PreReleaseNeedUpdate = 1,
};

typedef void(*PatcherPreReleaseCheckResult)(PreReleaseStatus code, int64_t totalBytes, int64_t needDownloadBytes, int64_t needSpace, const char* message, void* ctx);
typedef void(*PatcherPreReleaseCheckProgress)(int64_t totalbytes, int64_t currentBytes, void* ctx);
APPUPDATEAPI int pw_patcher_preRelease_checkStatus(PatcherPreReleaseCheckResult cbResult, PatcherPreReleaseCheckProgress cbProgress, void* ctx);

// 不限速 <=0, 限速 >0  
APPUPDATEAPI int pw_patcher_set_max_speed(int64_t KBPerSecond);

// 后台下载开关
APPUPDATEAPI int pw_patcher_enable_backgroundtask(bool enbale);

// 是否后台下载开启 1: 开启, 0:关闭
APPUPDATEAPI int pw_patcher_is_backgroundtask_enabled();

// 刷新配置回调 code=0则成功，否则失败desc 为一些错误描述， 若成功则通过content返回所有配置，content为json格式
typedef void(*OnConfigUpdate)(int code, const char* desc, const char* content);

// 刷新配置，初始化后可调用， 调用成功返回0 否则调用失败， 成功后需等待OnConfigUpdate，得到刷新结果
// 该回调 不需要pw_patcher_runCallbacks触发。
APPUPDATEAPI int pw_patcher_refresh_config(OnConfigUpdate cb);

// 获取值配置值， 按指定key获取， 获取之前必须先调用pw_patcher_refresh_config，否则一直返回空
APPUPDATEAPI const char* pw_patcher_get_config_by_key(const char* key);

APPUPDATEAPI void pw_patcher_set_native_env(void* context);

//  
typedef void(*OnServerListContentWithContext)(int code, const char* desc, const char* filepath, void* ctx);
APPUPDATEAPI void pw_patcher_refresh_serverlist_with_context(OnServerListContentWithContext cb, bool async, void* ctx);

// servelisturl: 目标url, 可以用分号隔开多个
APPUPDATEAPI void pw_patcher_refresh_serverlist_with_url(OnServerListContentWithContext cb, bool async, const char* servelisturl, void* ctx);


typedef void(*OnConfigUpdateWithContext)(int code, const char* desc, const char* content, void* ctx);
APPUPDATEAPI int pw_patcher_refresh_config_with_context(OnConfigUpdateWithContext cb, void* ctx);

// 获取本地资源版本
APPUPDATEAPI const char* pw_patcher_get_local_resversion();

// 获取本地app版本
APPUPDATEAPI const char* pw_patcher_get_local_appversion();

// 获取当前SDK版本
APPUPDATEAPI const char* pw_patcher_version();

// Add Ignore Files
// 文件: utf-8保存的Json格式,
// ["path1","path2", "path3" ... ]
APPUPDATEAPI int pw_patcher_ignore_files_byfile(const char* ignorefilepath);

// 文件数组 
APPUPDATEAPI int pw_patcher_ignore_files_bycontent(const char** ignorefiles, int fileCount);

// 删除之前设置的忽略的文件列表
APPUPDATEAPI int pw_patcher_reset_ignore_files();


// 获取版本信息 content: {"Environment":"release","Local":"0.2.1","PreRelease":"","Remote":"0.2.2"}
// Environment为当前环境 
// "beta": 对内
// "beta test": 对内测试
// "release": 对外
// "release test": 对外测试
typedef void(*OnRequestVersionInfo)(int code, const char* desc, const char* content, void* ctx);
APPUPDATEAPI int pw_patcher_request_versioninfo(OnRequestVersionInfo cb, void* ctx);

// 获取Tag版本信息 content: {"TagName": {"Local":"1.0.0", "Remote":"1.0.1"}}
typedef void(*OnRequestTagVersionInfo)(int code, const char* desc, const char* content, void* ctx);

// 获取Tag版本信息 tagName: tag名, 传空, 查询所有可选资源版本信息
APPUPDATEAPI int pw_patcher_request_tagversioninfo(const char* tagName, OnRequestTagVersionInfo cb, void* ctx);


// 检查app是否需要更新
// code: 0-成功 其他-失败
//有更新时 content: `{"Content":"这里是文本", "Force" : true / false, "HasUpdate" : true, "Url" : "", "Version" : "1.0.0"}`
//没有更新时 content `{"Force":false, "HasUpdate" : false}`
typedef void(*OnRequestCheckAppInfo)(int code, const char* desc, const char* content, void* ctx);
APPUPDATEAPI int pw_patcher_request_check_app_need_update(const char* channelId, const char* mediaId, OnRequestCheckAppInfo cb, void* ctx);

APPUPDATEAPI void pw_patcher_enable_push_notify(bool enable);

APPUPDATEAPI void pw_patcher_set_notify_content(const char* jsonContent);


// 获取可用版本 返回值为json 格式
// `[{"BranchName":"branch1", "Version":"0.0.1"}, {"BranchName":"branch1", "Version":"0.0.2"}, {"BranchName":"branch1", "Version":"0.0.3"}]`
typedef void(*OnAvailabalVersions)(int code, const char* desc, const char* content, void* ctx);
APPUPDATEAPI void pw_patcher_request_available_versions(OnAvailabalVersions cb, void* ctx);

// 设置资源版本
// version: 资源版本, 如果空或则null
// resverse: 保留字段
APPUPDATEAPI int pw_patcher_set_version(const char* version, const char* resverse = nullptr);


enum Notify_Action{
    Notify_DownloadStart = 0,
    Notify_DownloadFinish,
};


// 设置初始资源目录对应目录
// initialDir: 初始资源所在目录
// resDir: 资源根目录相对目录
// 例如 初始资源是 /initialDir/A.res, 提交到patcher后台的资源名是 other/A.res
// 则initialDir 传"/initialDir" resDir传"other"
// 若传的是相对路径, 安卓下是apk/Asset/下寻找 windows是从exe所在目录开始的相对目录
APPUPDATEAPI int pw_patcher_set_initialres_dir_mapping(const char* initialDir, const char* resDir, const char* customVersion = nullptr);


// only for internal use.
typedef int(*_patch_callback)(int64_t filesize, int64_t current);
struct PatchInfo
{
    int64_t size;
    _patch_callback cb;
};
APPUPDATEAPI int pw_patcher_patchfile(const char* from, const char* patch, const char* to, _patch_callback cb = nullptr);

typedef void (*OnBaseResCheckProgress)(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, const char* currentResName, int64_t currentFileSize, int64_t currentCheckedSize, void* ctx);
typedef void(*OnBaseCheckResult)(int code, const char* message, PatcherTagStatus resStatus, int64_t totalBytes, int64_t needDownloadBytes, int64_t needSpace, void* ctx);
APPUPDATEAPI int pw_patcher_base_check_resource(const char* tags, OnBaseCheckResult cbResult, OnBaseResCheckProgress cbProgress, void* ctx);

typedef int (*OnCheckHashProgress)(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, const char* currentResName, int64_t currentFileSize, int64_t currentCheckedSize, void* ctx);
typedef void (*OnCheckHashResult)(int code, const char* message, PatcherTagStatus resStatus, int64_t totalBytes, int64_t needDownloadBytes, int64_t needSpace, int totalcout, int needupdatecount, void* ctx);
// tags： "ALL" 所有tag， 传"" 基础资源， 其他：指定的tag
APPUPDATEAPI int pw_patcher_check_resource_hash(const char* tags, OnCheckHashResult cbResult, OnCheckHashProgress cbProgress, void* ctx);


APPUPDATEAPI int pw_patcher_doupdate_withtags(const char* tags, bool clearResource);

APPUPDATEAPI int64_t pw_patcher_get_freespace();

// 本地写文件限制, <=0:不限速
APPUPDATEAPI int pw_patcher_set_write_limit(int64_t bytesPerSecond);

// 本地写文件限制, 每写 writeTime 后等待waitTime, 设置0,取消限制
APPUPDATEAPI int pw_patcher_set_write_limit_by_interval(int64_t writeTimeMS, int64_t waitTimeMS);

// 校验本地资源, 并更新
APPUPDATEAPI int pw_patcher_checkhash_and_doupdate_withtags(const char* tags);

// 获取静默静默资源大小 <0 错误(PatcherApiResult), >= 0 实际大小
APPUPDATEAPI int64_t pw_patcher_get_play_assets_size();

// 强制校验目录, 不存在资源列表里的文件和目录会删掉,  
// dirs可以传相对资源目录若dirs为 "" 或者 null,则只校验后台配置的目录, 可以用`;`隔开传多个目录, "path1;path2;"
APPUPDATEAPI int pw_patcher_check_dir(const char* dirs /*= NULL*/);


// result格式: {"Code":0, "Message":"...",  "Version":"1.0.0", "Result": {"Match":["path1", "path2", "path3"], "UnMatch":["path1","path2"...]}}
// code为0 成功,其他失败(无法验证文件) 
typedef void (*OnCheckDirResult)(int code, const char* message, const char* result, void* ctx);

// 检查目录 (如果当前没有初始化, 会自动用launcher的配置初始化并在结束时反初始化, 如果需要多次调用请手动初始化和反初始化sdk)
// dirs: 传空或者null不检查, 或者传资源相对目录, 可以用`;`隔开传多个目录, "path1;path2;"
// filters: 检查的文件扩展名, 默认所有文件, 或者`;`隔开多个扩展名, "txt;pak;json;" ...
// 不要调用这个接口同时在别的线程初始化sdk.
// async = false 则同步调用否则异步
// ctx 透传
APPUPDATEAPI int pw_patcher_resource_check_result(bool async, const char* dirs, const char* filters, OnCheckDirResult cb, void* ctx);

enum PatcherKeepAliveType
{
    PatcherKeepAliveType_Normal = 0,
};

APPUPDATEAPI int pw_patcher_start_keep_alive(PatcherKeepAliveType type = PatcherKeepAliveType_Normal, const char* info = nullptr);

APPUPDATEAPI int pw_patcher_stop_keep_alive();

// 
APPUPDATEAPI int pw_patcher_setup_music_file_path(const char* path, int examinState);

// info:  {"Version": "", "Branch":"", "PreReleaseVersion":"", "PreReleaseBranch":"", Tag:{
// "tag0": "", "tag1": "", "tag2": ""
// }}
typedef void (*OnRequestOnlineVersion)(int code, const char* info, void* ctx);
APPUPDATEAPI int pw_patcher_request_online_version(bool async, OnRequestOnlineVersion cb, void* ctx);

// "["branch1", "branch12", "branch3" ...]"
APPUPDATEAPI const char* pw_patcher_get_local_branchs();

typedef void (*OnDeleteBranchCacheResult)(int code, const char* branchName, void* ctx);
typedef void (*OnDeleteBranchCacheProgress)(const char* branchNamem, int totalCount, int deletedCount ,void* ctx);
APPUPDATEAPI int pw_patcher_delete_branch_cache(const char* branchName, OnDeleteBranchCacheResult cb, OnDeleteBranchCacheProgress result, void* ctx);
APPUPDATEAPI int pw_patcher_cancel_delete_branch_cache(const char* branchName);


// 获取版本信息 content: {"Environment":"release","Local":"0.2.1","PreRelease":"","Remote":"0.2.2"}
typedef void(*OnRequestBranchVersionInfo)(int code, const char* desc, const char* content, void* ctx);
APPUPDATEAPI int pw_patcher_request_branch_version(bool async, const char* configPath, const char* configContent, const char* patcherPath, OnRequestBranchVersionInfo callback, void* context);

// 设置文件异常
enum FileExceptionType
{
    FileExceptionType_DecompressError = 0,
};
APPUPDATEAPI int pw_patcher_set_file_exception(const char* path, int type = FileExceptionType_DecompressError);

enum ResolveFileExceptionType
{
    ResolveFileExceptionType_Null = -1,    
    ResolveFileExceptionType_Ignore = 0,
    ResolveFileExceptionType_Delete = 1,
};

// 设置异常文件处理方式
APPUPDATEAPI int pw_patcher_set_exception_resolve_type(int type);

// 获取上报过的异常文件, 返回json格式
// ["path1", "path2", ...]
APPUPDATEAPI const char* pw_patcher_get_exception_files();


APPUPDATEAPI const char* pw_patcher_get_branch_exception_files(const char* branchDir);

APPUPDATEAPI int pw_patcher_set_branch_exception_resolve_type(const char* branchDir, int type);

typedef void(*OnSpeedTest)(int code, const char* desc, int64_t bytesPerSecond, void* ctx);
APPUPDATEAPI int pw_patcher_start_speedtest(int time, const char* url, OnSpeedTest cb, void* ctx);

APPUPDATEAPI int pw_patcher_stop_speedtest();



// 获取资源大小tags为空或null，取所有资源大小 用分号分隔 "tag1;tag2;"
APPUPDATEAPI int64_t pw_patcher_get_total_remote_size(const char* tags);

typedef int(*pw_callback_upload_progress)(int64_t total, int64_t current, void* ctx);
typedef void(*pw_callback_upload_result)(int code, const char* url, const char* message, void* ctx);
typedef int(*pw_function_upload)(const char* name, const char* path, const char* category, pw_callback_upload_result cb, pw_callback_upload_progress progress, void* ctx);
APPUPDATEAPI int pw_patcher_set_upload_function(pw_function_upload upload_funciont);

// {"branch1":{"userdata": "path1"}, "branch2":{"userdata": "path2"}}
APPUPDATEAPI int pw_patcher_set_work_dir(const char* json);

#ifdef __cplusplus
}
#endif

#endif 



