#ifndef _PATCHER_ERR_H_
#define _PATCHER_ERR_H_

enum PatcherErr
{
    kErrOk = 0,
    kErrParam = 1,                          // 参数错误
    kErrCommon = 2,

    kErrDns = 3,                            // 域名解析失败
    kErrCntConnect = 4,                     // 无法连接服务器
    kErrTimeout = 5,                        // 连接或者传输数据超时
    kErrResume = 6,                         // 服务器不支持文件续传
    kErrNetUnknown = 7,                     // 未定义的网络错误，可查看日志

    kErrRedirection = 8,                    // 重定向错误
    kErrRequest = 9,                        // 请求错误
    kErrServer = 10,                         // 服务器内部错误

    kErrState = 11,                          // 当前状态无法执行操作
    kErrNotReady = 12,                       // 准备工作未完成
    kErrRepeatAction = 13,                   // 正在运行中，不可重复操作
    kErrAppVersion = 14,                     // App版本不匹配
    kErrResVersion = 15,                     // 资源版本问题导致无法更新
    kErrFileMd5Check = 16,                   // 文件md5检查失败
    kErrUnzip = 17,                          // 解压失败
    kErrPatch = 18,                          // 打补丁失败
    
    kErrBadPkg = 19,                         // 错误资源包
    kErrInsufficientSpace = 20,              // 本地磁盘空间不足
    kErrCancel = 21,                         // 外部取消操作
    kErrFileOperation = 22,                  // 文件操作失败
    kErrFileList = 23,                       // filelist错误                  
    kErrFileBroken = 24,                     // 文件丢失或损坏
    kErrUnknown = 25,                        // 未知错误

	kErrNoValidCDN = 26,						// 无可用CDN（该值为中间值，不用于最后的错误码），2020-04-17新增，用于自动屏蔽下载失败超过指定次数的CDN，尽量避免无用的下载
    
    kErrPreReleaseNotExist = 27,
    kErrUpdatedConfigInvalid = 28,          // 配置不正确
    kErrDiskFull = 29,                      // 磁盘已满
    kErrDeleteFileFailed = 30,              // 删除文件失败
    kErrRenameFileFailed = 31,              // 文件重命名失败
    kErrInvalidRemoteResList = 32,          // 非法文件列表
    kErrPakPatchFailed = 33,                // pak 差异更新失败
    kErrPreinstallAssetError = 34,          // 静默资源下载失败

    kErrTagNameEmpty = 40,
    kErrPrereleaseEmpty = 41,

    // 内部错误
    kErrTestModeRetry = 100,
    kErrLargeLocalVersion = 101,
};

// 接口返回
enum PatcherApiResult
{
    kResultOK = 0,
    kResultPathNotExist = -1,
    kResultParamPointerNull = -2,
    kResultResUrlEmpty = -3,
    kResultInvalidInputPath = -4,
    kResultInitLogFailed = -5,
    kResultInvalidResInfo = -6,
    kResultConfigNotExist = -7,
    kResultConfigError = -8,
    kResultUpdateNotStart = -9,
    kResultSDKAlreadyInited = -10,
    kResultSDKNotInited = -11,

    kResultUpdateRunning = -12,
    kResultClearTaskIsRunning = -13,

    kResultEmptyCDN = -14,
    kResultTaskAlreadyExist = -15,
    kResultRemoteConfigError = -16,

    kResultInvalidPath = -17,
    kResultInvalidUrl = -18,
    kResultFileExist = -19,
    kResultCancel = -20,
    kResultUnknownConfirm = -21,
    kResultFileTooLarge = -22,
    kResultWriteFileFailed = -23,
    kResultParseJsonError = -24,
    kResultInvalidServerListConfig = -25,
    kResultUpdateInterfaceAlreadyExist = -26,
    kResultInvalidUpdaterPath = -27,
    kResultTaskNotStart = -28,
    kResultUpdateInvalidVersion = -29,
    kResultInvalidAppInfoBranch = -30,
    kResultInvalidLocalVersion = -31,
    kResultPreReleaseTaskRunning = -32,
    kResultTagTaskRunning = -33,
    kResultTaskQueueBusy = -34,
    kResultNeedRelativeDir = -35,
    kResultNeedAbsoluteDir = -36,
    kResultInvalidResList = -37,
    kResultFileNotExist = -38,

	kResultIOError = -40,

    kResultInvalidHosts = -41,
    kResultInvalidCallback = -42,
    kResultInvalidChannel = -43,
    kResultInvalidPreinstallInstance = -44,

    kResultTagInvalidTask = -60,
    kResultTagTaskAlreadyStarted = -61,
    kResultTagInternErrNullParam = -62,
    kResultTagNotStarted = -63,
    kResultCDNChanged = -64,
    kResultHttpLowSpeed = -65,
    kResultCheckDirErrorConfigReq = -66,
    kResultNotFromLauncher = -67,
    kResultCheckDirLocalConfigNotExist = -68,
    kResultCheckDirParseError = -69,
    kResultCheckDirSyncFailed = -70,
    kResultCheckDirHashUnmach = -71,
    kResultLargeLocalVersion = -72,
    kResultPatchFailed = -73,
    kResultPreReleaseDeleteTaskRunning = -74,

    kResultTagNotExist = -100,

    kResultPreReleaseStarted = -200,
    kResultPreReleaseNotExist = -201,

    kResultUnsupportDirForPS4 = -280,
    kResultHttpRequestEnd = -300,
    kResultHttpFileDoneBlockHashError = -300,
    kResultHttpFileDoneNullFile = -301,
    kResultHttpFileDoneFailed = -302,
    kResultHttpFileDoneEmptyCommonFile = -303,
    kResultHttpFileDoneRenameFailed = -304,
    kResultHttpFileDoneBlockSizeError = -305,
    kResultHttpFileRequestError = -306,
    kResultHttpFileLengthError = -307,
    kResultHttpFileTimeoutWhenReciving = -308,
    kResultHttpFileCheckCompleteError = -309,
    kResultHttpFileInitError = -310,
    kResultHttpFileApiError = -311,
    kResultHttpFileApiServerReplyEmpty = -312,
    kResultHttpFileSpaceNotEnough = -313,

    kResultHttpFileNextBlock = -399,
    kResultHttpRequestStart = -399,

    kResultPreinstallWait = -600,
    kResultPreinstallAssetRequestFailedEnd = -601,
    kResultPreinstallAssetPathEmpty = -601,
    kResultPreinstallAssetUncompleteBlock = -602,
    kResultPreinstallAssetDownloadFailed = -603,
    kResultPreinstallAssetIOError = -604,
    kResultPreinstallCancel = -605,
    kResultPreinstallTimeout = -606,

    kResultPreinstallAssetRequestFailedStart = -699,

    kResultNotEnoughMemory = 700,

    kResultUnsupport = -998,
    kResultFailed = -999,

};



#endif // _PATCHER_ERR_H_

