//
//  WMMacLauncherOudManager.h
//  WMMacLauncherCore
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/19.
//

#import <Foundation/Foundation.h>
#import "WMMacPatcherDefines.h"

NS_ASSUME_NONNULL_BEGIN

@interface WMMacLauncherOudManager : NSObject
+ (instancetype)sharedInstance;

- (void)setupAppId:(NSString *)appID
            appKey:(NSString *)appKey
         channelId:(NSString *)channelId
           hostUrl:(NSString *)hostUrl
          areaType:(NSInteger)areaType;


- (void)sendFeedbackWithContent:(NSString *)message
                     appVersion:(NSString *)appVersion
                    attachments:(NSArray<NSURL *> *)attachments
                     logFileDir:(NSArray<NSString *> *)logPaths
                       progress:(void(^)(WMFeedbackStepType step, float progress))progress
                     completion:(void(^)(NSDictionary *result, NSError *error))completion;
@end

NS_ASSUME_NONNULL_END
