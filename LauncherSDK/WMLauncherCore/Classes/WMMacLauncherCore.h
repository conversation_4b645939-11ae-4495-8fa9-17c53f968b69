#include <Foundation/Foundation.h>

#import "WMMacPatcherDefines.h"

NS_ASSUME_NONNULL_BEGIN

typedef void(^WMMacLauncherProcessLaunchCompletion)(NSRunningApplication * _Nullable app, NSError * _Nullable error);

typedef void(^WMMacLauncherProcessTerminationHandler)(NSRunningApplication *terminatedApp);

typedef void(^WMMacLauncherBaseResCheckProgressCallback)(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, NSString *currentResName, int64_t currentFileSize, int64_t currentCheckedSize);

typedef void(^WMMacLauncherBaseCheckResultCallback)(int code, NSString *message, WMPatcherTagStatus resStatus, int64_t totalBytes, int64_t needDownloadBytes, int64_t needSpace);

typedef void(^WMMacLauncherCheckResHashProgressCallback)(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, NSString *currentResName, int64_t currentFileSize, int64_t currentCheckedSize);

typedef void(^WMMacLauncherCheckResHashResultCallback)(int code, NSString *message, WMPatcherTagStatus resStatus, int64_t totalBytes, int64_t needDownloadBytes, int64_t needSpace, int totalcout, int needupdatecount);

// 停止下载操作类型
typedef NS_ENUM(NSInteger, WMStopDownloadType) {
    // 取消下载
    WMStopDownloadTypeCancel = 0,
    // 暂停下载
    WMStopDownloadTypePause = 1
};
@interface WMMacLauncherCore : NSObject

#pragma mark - Patcher 相关属性和方法

// 版本检查回调，下载前触发
@property (nonatomic, copy) void(^patcherVersionCallback)(WMPatcherVerCheck result, WMPatcherVersionInfo *versionInfo);

// 下载前检查回调，用于告知是否有预下载资源，以及预下载资源的版本信息，分支信息 
@property (nonatomic, copy) void(^patcherPreReleaseCheckCallback)(BOOL hasPreRelease, NSString *preReleaseBranch, NSString *preReleaseResVersion);

// 下载进度回调，其状态为 WMPatcherStateResDownload ，下载中触发
@property (nonatomic, copy) void(^patcherDownloadCallback)(uint64_t totalsize, double progress, uint64_t speed, NSInteger index);

// 状态改变回调，下载中触发
@property (nonatomic, copy) void(^patcherStateCallback)(WMPatcherState state, BOOL isStart, NSInteger downloadIndex);

// 资源修复回调， 其状态为 WMPatcherStateResFix ，下载结束后触发。如资源合并、资源更新、资源删除 操作
@property (nonatomic, copy) void(^patcherFixResourceCallback)(uint32_t totalcount, uint32_t remaincount, WMPatcherFileOper fileOper, NSString *currentFixedFile, NSInteger uniqueIndex);

// 资源修复回调， 其状态为 WMPatcherStateResFix ，下载结束后触发。如资源合并、资源更新、资源删除 操作
@property (nonatomic, copy) void(^patcherFixResourceFileStartCallback)(int64_t totalResouceByteSize, int64_t totalFixedResourceSize, int64_t CurrentResourceTotalSize, WMPatcherFileOper file_oper, NSString *current_file, NSInteger uniqueIndex);


// 所有流程完成回调，无论成功还是失败都会触发
@property (nonatomic, copy) void(^patcherFinishCallback)(WMPatcherErr result, NSInteger index, WMStopDownloadType reason);

// Launcher自身日志存储目录
@property (nonatomic, strong) NSString *launcherLogDirPath;

// game日志存储目录
@property (nonatomic, strong, nonnull) NSString *gameLogDirPath;

+ (void)setLogEnabled:(BOOL)enabled;

// 设置回调驱动间隔时长，默认 0.8s
- (void)setCallbackInterval:(NSTimeInterval)interval;

// 需初始化前调用，设置userdata工作目录，确保patcher在对Launcher和游戏内做测试资源检查时，使用相同的路径
- (void)setUserdataWorkDirInfo:(NSDictionary *)workDirInfo;
+ (NSString *)getGameLogDataPathWithLogPath:(NSString *)logPath;

/**
 * 使用配置文件 初始化 Patcher
 * @param configPath 配置文件路径
 * @return 0 成功，其他 失败
 */
- (NSInteger)setupPatcherWithConfigPath:(NSString *)configPath pathInfo:(WMPatcherPathInfo *)pathInfo deviceId:(NSString *)deviceId mediaId:(NSString *)mediaId;

// 获取本地资源版本
- (NSString *)getLocalResVersion;

/**
 * 资源版本检查 
 */
- (void)availableVersionCheck:(void(^)(NSArray<WMPatcherAvailableVersionInfo *> * _Nullable versionInfos, NSError * _Nullable error))callback;

/**
 * 获取版本信息 content: {"Environment":"release","Local":"0.2.1","PreRelease":"","Remote":"0.2.2"}
 * Environment为当前环境 
 * "beta": 对内
 * "beta test": 对内测试
 * "release": 对外
 * "release test": 对外测试
 * @param callback 回调
 */
- (void)requestEnvironmentInfo:(void(^)(WMPatcherEnvironmentInfo * _Nullable environmentInfo, NSError * _Nullable error))callback;


/**
 * 可在未初始化Patcher时，获取不同游戏版本信息 content: {"Environment":"release","Local":"0.2.1","PreRelease":"","Remote":"0.2.2"}
 * Environment为当前环境
 * "beta": 对内
 * "beta test": 对内测试
 * "release": 对外
 * "release test": 对外测试
 * @param callback 回调
 */
- (void)requestBranchVersionInfo:(BOOL)async
                      configPath:(nullable NSString *)configPath
                   configContent:(nullable NSString *)configContent
                     patcherPath:(nullable NSString *)patcherPath
                  resultCallback:(void(^)(WMPatcherEnvironmentInfo * _Nullable environmentInfo, NSError * _Nullable error))callback;

/**
 * 资源检查 
 * @param tags 更新标签,如果为 @“” 则检查基础资源， 如果为 ALL 则检查所有资源，多个标签用逗号(,)分隔
 * @param progressCallback 资源检查进度回调
 * @param resultCallback 资源检查结果回调
 */
- (void)checkResource:(nullable NSString *)tags progressCallback:(WMMacLauncherBaseResCheckProgressCallback)progressCallback resultCallback:(WMMacLauncherBaseCheckResultCallback)resultCallback;


/**
 * 资源更新
 * @param tags 更新标签,如果为nil则下载基础资源， 如果为 ALL 则下载所有资源，多个标签用逗号(,)分隔
 * @param clearResource 是否清除资源
 * @param index 多个任务下载时，可设置index进行区分
 */
- (void)updateResource:(NSString *)tags clearResource:(BOOL)clearResource downloadIndex:(NSInteger)index;

/**
 * 反初始化 Patcher
 */
- (void)uninitPatcher;

/**
 * 取消更新
 * @param type 停止下载的原因，取消或暂停
 */
- (void)stopUpdate:(WMStopDownloadType)type;

/**
 * 修改选择指定的版本号
 */
- (void)changeSelectedVersion:(NSString *)version;

/**
 * 完整资源修复检查
 * @param tags 更新标签,如果为 @“” 则检查基础资源， 如果为 ALL 则检查所有资源，多个标签用逗号(,)分隔
 * @param progressCallback 资源检查进度回调
 * @param resultCallback 资源检查结果回调
 */
- (void)checkResourceHash:(NSString *)tags progressCallback:(WMMacLauncherCheckResHashProgressCallback)progressCallback resultCallback:(WMMacLauncherCheckResHashResultCallback)resultCallback;

/**
 * 取消资源修复检查
 */
- (void)cancelCheckResourceHash;

/**
 * 获取异常损坏的文件
 */
- (nullable NSArray<NSString *> *)getExceptionFiles;

/**
 * 设置异常损坏文件的处理方式
 */
- (void)setExceptionResolveType:(WMResolveFileExceptionType)resolveType;

/**
 * 获取取所有资源大小
 */
- (int64_t)getTotalRemoteSize:(NSString *)tags;

/**
 * 测速
 * @param time 测速时间，默认传5s
 * @param completion 测速完成回调
 */
- (void)speedTest:(int)time completion:(void(^)(int code, NSString *message, int64_t bytesPerSecond))completion;

// 获取CDN列表
- (nullable NSArray <NSString *> *)getCDNList;

// 设置CDN
- (int)setCDN:(NSString *)url;

// 设置下载速度限制 (KB/s), 不限速 <=0, 限速 >0  
- (int)setMaxSpeed:(int64_t)KBPerSecond;
#pragma mark - 进程管理相关方法
/**
 * 启动应用程序
 * @param appURL 应用程序的URL
 * @param bundleIdentifier 应用程序的Bundle Identifier
 * @param arguments 透传给Application的命令行参数
 * @param completion 启动完成的回调
 * @param terminationHandler 应用程序终止时的回调
 */
- (void)launchApplicationWithURL:(NSURL *)appURL
                bundleIdentifier:(NSString *)bundleIdentifier
                       arguments:(nullable NSArray<NSString *> *)arguments
                      completion:(nullable WMMacLauncherProcessLaunchCompletion)completion
              terminationHandler:(nullable WMMacLauncherProcessTerminationHandler)terminationHandler;

/**
 * 根据Bundle Identifier搜索应用程序
 * @param bundleIdentifier 应用程序的Bundle Identifier
 * @param searchPath 搜索路径，如果为nil则在系统范围内搜索
 * @return 找到的应用程序URL数组
 */
- (NSArray<NSURL *> *)searchApplicationWithBundleIdentifier:(NSString *)bundleIdentifier
                                               searchPath:(nullable NSString *)searchPath;

/**
 * 检查应用程序是否正在运行
 * @param bundleIdentifier 应用程序的Bundle Identifie
 * @note bundleIdentifier=nil,则判断是否有通过launchApplicationWithURL:completion:terminationHandler:启动运行的App
 * @return 正在运行App路径
 */
+ (nullable NSString *)isApplicationRunning:(nullable NSString *)bundleIdentifier;

/**
 * 添加正在运行的应用程序
 * @param bundleIdentifier 应用程序的Bundle Identifier
 * @param terminationHandler 应用程序终止时的回调
 */
- (void)addRunningApplication:(NSString *)bundleIdentifier terminationHandler:(nullable WMMacLauncherProcessTerminationHandler)terminationHandler;

/**
 * 终止所有匹配的应用程序
 * @param bundleIdentifier 应用程序的Bundle Identifier
 * @return 成功终止的应用程序数量
 */
- (NSInteger)terminateApplicationWithBundleIdentifier:(NSString *)bundleIdentifier;


/**
 * 终止所有的通过launchApplicationWithURL:completion:terminationHandler:接口启动的应用程序
 * @return 成功终止的应用程序数量
 */
- (NSInteger)terminateRunningApplication;

/**
 * 删除所有应用被终止的通知回调
 */
- (void)removeAllTerminationHandlers;

#pragma mark - Feedback 对象存储功能模块
/**
 * 初始化对象存储功能模块
 * @param areaType 0:大陆地区，使用腾讯云 1:私有云 2:海外地区，使用亚马逊云
 */
- (void)setupOudAppId:(NSString *)appID appKey:(NSString *)appKey channelId:(NSString *)channelId hostUrl:(NSString *)hostUrl areaType:(NSInteger)areaType;

/**
 * @param message 意见反馈填写的文本内容
 * @param attachments 选择上传的文本附件路径
 * @param progress 上传进度
 * @param completion 完成回调
 */
- (void)sendFeedbackWithContent:(nullable NSString *)message
                     appVersion:(nullable NSString *)appVersion
                    attachments:(nullable NSArray<NSURL *> *)attachments
                       progress:(void(^ __nullable)(WMFeedbackStepType step, float progress))progress
                     completion:(void(^ __nullable)(NSDictionary * _Nullable result, NSError * _Nullable error))completion;

@end

NS_ASSUME_NONNULL_END
