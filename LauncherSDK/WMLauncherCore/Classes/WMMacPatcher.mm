#import "WMMacPatcher.h"
#import "WMMacLauncherLogger.h"
//#include "iappupdateinterface.h"
#import <WMLauncherDependency/iappupdateinterface.h>
#import <WMCategories/WMFoundationMacro.h>
#import <WMCategories/NSObject+WMJson.h>

// 添加常量定义
static NSString * const kWMMacPatcherErrorDomain = @"WMMacPatcher";
static const double kDefaultCallbackInterval = 0.8;
static const int kDefaultLogLevel = -1; //日志等级

// [NSString stringWithUTF8String:cString];

static NSString *safeStringWithUTF8String(const char* cString) {
    if (cString) {
        return [NSString stringWithUTF8String:cString];
    }
    return @"";
}

static NSString *PatcherVerCheckToString(PatcherVerCheck result) {
    switch (result) {
        case kNoUpdate:
            return @"不需要更新(kNoUpdate)";
        case kAppUpdate:
            return @"需要更新App(kAppUpdate)";
        case kResUpdate:
            return @"可以进行资源更新(kResUpdate)";
        case kException:
            return @"出现异常，不可继续更新(kException)";
        default:
            return @"未知";
    }
}

// 字节数转换为容量大小（如 TB,GB,MB,KB,B）
static NSString* ByteToCapacity(uint64_t bytes) {
    // 定义容量单位常量
    static const uint64_t KB = 1024;
    static const uint64_t MB = KB * 1024;
    static const uint64_t GB = MB * 1024;
    static const uint64_t TB = GB * 1024;
    
    double formattedSize;
    NSString *unit;
    
    // 使用 static 避免重复创建
    static NSNumberFormatter *formatter;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        formatter = [[NSNumberFormatter alloc] init];
        formatter.numberStyle = NSNumberFormatterDecimalStyle;
        formatter.maximumFractionDigits = 2;
        formatter.minimumFractionDigits = 2;
    });

    // 从大到小判断容量范围
    if (bytes >= TB) {
        formattedSize = bytes / (double)TB;
        unit = @"TB";
    } else if (bytes >= GB) {
        formattedSize = bytes / (double)GB;
        unit = @"GB"; 
    } else if (bytes >= MB) {
        formattedSize = bytes / (double)MB;
        unit = @"MB";
    } else if (bytes >= KB) {
        formattedSize = bytes / (double)KB;
        unit = @"KB";
    } else {
        formattedSize = bytes;
        unit = @"B";
    }

    return [NSString stringWithFormat:@"%@%@", 
            [formatter stringFromNumber:@(formattedSize)], 
            unit];
}

// progress: 0.000058 -> 0.06%，保留两位小数
static NSString* ProgressToString(double progress) {
    // 使用 static 避免重复创建
    static NSNumberFormatter *formatter;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        formatter = [[NSNumberFormatter alloc] init];
        // 使用小数样式 
        formatter.numberStyle = NSNumberFormatterDecimalStyle;
        // 保留一位小数
        formatter.maximumFractionDigits = 1;
        formatter.minimumFractionDigits = 1;
    });
    
    // 将小数转换为百分比
    double percentage = progress * 100.0;
    
    // 格式化为字符串,添加百分号
    return [NSString stringWithFormat:@"%@%%", 
            [formatter stringFromNumber:@(percentage)]];
}   

// speed: 1024 -> 1KB/s， 需要转换成合适的单位  
static NSString* SpeedToString(uint64_t speed) {
    return [NSString stringWithFormat:@"%@/s", ByteToCapacity(speed)];
}

/*
enum PatcherState : uint32_t
{
    kStateInit = 0,                         // 初始状态；辅助，该状态不触发任何回调函数
    kStateVersionCheck = 1,                 // 版本检查；触发OnVersionCheck
    kStateLocalResCheck = 2,                // 本地资源校验；
    kStateResRetrieve = 3,                  // 检索需更新的资源；触发OnPreDownload
    kStateResDownload = 4,                  // 下载资源包（包括补丁包）；触发OnDownload
    kStateResFix = 5,                       // 资源处理（更新，移动，替换，删除等）；触发OnFixResource    

    kStateUpdateFinished = 10,
    
    kStateTagResDownload = 11,
    kStateTagResDownloadFinished = 12,
    kStatePreReleaseDownload = 13,
    kStatePreReleaseDownloadFinished = 14,

    kStateUnInit = 100,
    kStateError = 101,
};
*/
static NSString *PatcherStateToString(PatcherState state) {
        switch (state) {
        case kStateInit:
            return @"初始化(kStateInit)";
        case kStateVersionCheck:
            return @"版本检查(kStateVersionCheck)";
        case kStateLocalResCheck:
            return @"本地资源校验(kStateLocalResCheck)"; 
        case kStateResRetrieve:
            return @"检索需更新的资源(kStateResRetrieve)";
        case kStateResDownload:
            return @"下载资源包(kStateResDownload)";
        case kStateResFix:
            return @"资源处理(kStateResFix)";
        case kStateUpdateFinished:
            return @"更新完成(kStateUpdateFinished)";
        case kStateTagResDownload:
            return @"标签资源下载(kStateTagResDownload)";
        case kStateTagResDownloadFinished:
            return @"标签资源下载完成(kStateTagResDownloadFinished)";
        case kStatePreReleaseDownload:
            return @"预下载资源下载(kStatePreReleaseDownload)";
        case kStatePreReleaseDownloadFinished:
            return @"预下载资源下载完成(kStatePreReleaseDownloadFinished)";
        case kStateUnInit:
            return @"未初始化(kStateUnInit)";
        case kStateError:
            return @"错误(kStateError)";
        default:
            return @"未知";
    }
    
}

/*
 PatcherFileOper_Null = 0,
    PatcherFileOper_Add = 1,
    PatcherFileOper_Update = 2,
    PatcherFileOper_Delete = 3,
    */
static NSString *FileOperToString(PatcherFileOper file_oper) {
    switch (file_oper) {
        case PatcherFileOper_Null:
            return @"空(PatcherFileOper_Null)";
        case PatcherFileOper_Add:
            return @"新增(PatcherFileOper_Add)";
        case PatcherFileOper_Update:
            return @"更新(PatcherFileOper_Update)";
        case PatcherFileOper_Delete:
            return @"删除(PatcherFileOper_Delete)";
        default:
            return @"未知";
    }
}

static NSString *PatcherTagStatusToString(PatcherTagStatus status) {
    switch (status) {
        case PatcherTag_UnKnown:
            return @"未知(PatcherTag_UnKnown)";
        case PatcherTag_NeedUpdate:
            return @"需要更新(PatcherTag_NeedUpdate)";
        case PatcherTag_Completed:
            return @"不需要更新(PatcherTag_Completed)";
        default:
            return @"未知";
    }
}


@interface WMMacPatcher() {
    PatcherCallback _callbacks;
    dispatch_source_t _callbackTimer;  // 用于定期处理回调
}

// 自己的队列
@property (nonatomic, strong) dispatch_queue_t queue;
// callback queue
@property (nonatomic, strong) dispatch_queue_t callbackQueue;

@property (nonatomic, copy) void(^availableVersionsCallback)(int code, NSString *desc, NSString *content);

@property (nonatomic, copy) void(^environmentInfoCallback)(int code, NSString *desc, NSString *content);

@property (nonatomic, copy) void(^branchVersionInfoCallback)(int code, NSString *desc, NSString *content);

@property (nonatomic, strong) NSString *patcherResPath;

@property (nonatomic, assign) WMCheckHashStatus checkHashStatus;

// 测速回调
@property (nonatomic, copy) void(^__nullable speedTestCallback)(int code, NSString *message, int64_t bytesPerSecond);
@end

@implementation WMPatcherAvailableVersionInfo
- (NSString *)description {
    return [NSString stringWithFormat:@"WMPatcherAvailableVersionInfo {branchName: %@, version: %@}", self.branchName, self.version];
}
@end

@implementation WMPatcherEnvironmentInfo
- (NSString *)description {
    return [NSString stringWithFormat:@"WMPatcherEnvironmentInfo {environment: %@, local: %@, preRelease: %@, remote: %@}", self.environment, self.local, self.preRelease, self.remote];
}
@end

@implementation WMPatcherVersionInfo
- (NSString *)description {
    return [NSString stringWithFormat:@"WMPatcherVersionInfo {newestResVersion: %@, newestAppVersion: %@, minResVersion: %@, needUpdateResource: %@, needUpdateApp: %@, versionDescription: %@}", self.newestResVersion, self.newestAppVersion, self.minResVersion, self.needUpdateResource ? @"YES" : @"NO", self.needUpdateApp ? @"YES" : @"NO", self.versionDescription];
}
@end

@implementation WMPatcherPathInfo
- (NSString *)description {
    return [NSString stringWithFormat:@"WMPatcherPathInfo {resPath: %@, patcherPath: %@, apkPath: %@, serverListPath: %@, logPath: %@}", self.resPath, self.patcherPath, self.apkPath, self.serverListPath, self.logPath];
}
@end

@implementation WMMacPatcher

- (instancetype)init {
    self = [super init];
    if (self) {
        _queue = dispatch_queue_create("com.wmmac.patcher.queue", DISPATCH_QUEUE_SERIAL);
        _callbackQueue = dispatch_queue_create("com.wmmac.patcher.callback.queue", DISPATCH_QUEUE_SERIAL);
        _callbackInterval = 0.8;
    }
    return self;
}

+ (instancetype)sharedInstance {
    static WMMacPatcher *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[WMMacPatcher alloc] init];
    });
    return instance;
}

- (void)dealloc {
    [self uninit];
}

- (void)dispatchBlock:(void(^)(void))block {
    if (block) {
        dispatch_async(self.queue, block);
    }
}

#pragma mark - Timer

- (void)startCallbackTimer {
    if (_callbackTimer) return;
    
    _callbackTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, self.callbackQueue);
    // 0.8 s
    double interval = self.callbackInterval > 0 ? self.callbackInterval : 0.8;
    dispatch_source_set_timer(_callbackTimer, dispatch_time(DISPATCH_TIME_NOW, 0), interval * NSEC_PER_SEC, 0);
    dispatch_source_set_event_handler(_callbackTimer, ^{
        pw_patcher_runCallbacks();
    });
    dispatch_resume(_callbackTimer);
}

- (void)stopCallbackTimer {
    if (_callbackTimer) {
        dispatch_source_cancel(_callbackTimer);
        _callbackTimer = nil;
    }
}

/// 给文件执行权限
- (void)modifyPatcherDownloadResPathPerm:(NSString *)targetPath {
    NSString *resDirPath = targetPath;
    if (!resDirPath) {
        resDirPath = self.patcherResPath;
    }
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error = nil;
    NSArray *files = [fileManager contentsOfDirectoryAtPath:resDirPath error:&error];
    
    for (NSString *fileName in files) {
        NSString *filePath = [resDirPath stringByAppendingPathComponent:fileName];
        
        // 检查文件是否存在
        BOOL isDirectory;
        if ([fileManager fileExistsAtPath:filePath isDirectory:&isDirectory]) {
            NSDictionary *dictionary = [fileManager attributesOfItemAtPath:filePath error:&error];
            if (error) {
                ML_LOG(@"modify perm: path: %@, attr: %@, error: %@", filePath,
                          [dictionary debugDescription],
                          error.description);
            }

            if (isDirectory) {
                [self modifyPatcherDownloadResPathPerm:filePath];
            } else {
                // 添加可执行权限 (0755 表示所有者有读、写、执行权限，其他用户有读、执行权限)
                NSDictionary *attributes = @{NSFilePosixPermissions: @(0755)};
                [fileManager setAttributes:attributes ofItemAtPath:filePath error:&error];
                if (error) {
                    ML_LOG(@"modify perm: error: %@", error.description);
                }
            }
        }
    }
}
#pragma mark - Callback Functions

static void onVersionCheck(PatcherVerCheck result, const VersionInfo* versionInfo) {
    ML_LOG(@"接收版本检查回调(versionCallback): 版本检查结果: %@", PatcherVerCheckToString(result));
    WMPatcherVersionInfo *info = [[WMPatcherVersionInfo alloc] init];
    if (versionInfo) {
        info.newestResVersion = safeStringWithUTF8String(versionInfo->newestResVersion);
        info.newestAppVersion = safeStringWithUTF8String(versionInfo->newestAppVersion);
        info.minResVersion = safeStringWithUTF8String(versionInfo->minResVersion);
        info.versionDescription = safeStringWithUTF8String(versionInfo->versionDescrition);
        info.needUpdateResource = versionInfo->needUpdateResource;
        info.needUpdateApp = versionInfo->needUpdateApp;
        ML_LOG(@"接收版本检查回调(versionCallback): 版本信息: %@", info);
    }
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.versionCallback) {
        [patcher dispatchBlock:^{
            patcher.versionCallback((WMPatcherVerCheck)result, info);
        }];
    }
}

static void onPreDownload(uint64_t will_download_bytes, uint64_t update_need_space_bytes, uint64_t free_diskspace_bytes) {
    ML_LOG(@"接收预下载回调(preDownloadCallback): 将要下载的字节数: %llu, 需要空间字节数: %llu, 剩余空间字节数: %llu", will_download_bytes, update_need_space_bytes, free_diskspace_bytes);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.preDownloadCallback) {
        [patcher dispatchBlock:^{
            patcher.preDownloadCallback(will_download_bytes, update_need_space_bytes, free_diskspace_bytes);
        }];
    }
}

static void onDownload(uint64_t totalsize, double progress, uint64_t speed) {
    ML_LOG(@"下载回调(downloadCallback): 总字节数: %@, 进度: %@, 速度: %@", ByteToCapacity(totalsize), ProgressToString(progress), SpeedToString(speed));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.downloadCallback) {
        [patcher dispatchBlock:^{
            patcher.downloadCallback(totalsize, progress, speed);
        }];
    }
}

static void onFixResource(uint32_t totalcount, uint32_t remaincount, PatcherFileOper file_oper, const char* current_fixed_file) {
    ML_LOG(@"修复资源回调(fixResourceCallback): 总文件数: %u, 剩余文件数: %u, 文件操作: %@, 当前修复文件: %s", totalcount, remaincount, FileOperToString(file_oper), current_fixed_file);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.fixResourceCallback) {
        NSString *currentFixedFileStr = safeStringWithUTF8String(current_fixed_file);
        [patcher dispatchBlock:^{
            patcher.fixResourceCallback(totalcount, remaincount, (int)file_oper, currentFixedFileStr);
        }];
    }
}

static void onStateStart(PatcherState state) {
    ML_LOG(@"------------ 状态: %@  开始 ------------", PatcherStateToString(state));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.stateCallback) {
        dispatch_async(dispatch_get_main_queue(), ^{
            patcher.stateCallback((WMPatcherState)state, YES);
        });
    }
}

static void onStateEnd(PatcherState state) {
    ML_LOG(@"------------ 状态: %@  结束 ------------", PatcherStateToString(state));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.stateCallback) {
        [patcher dispatchBlock:^{
            patcher.stateCallback((WMPatcherState)state, NO);
        }];
    }
}

static void onUpdateFinish(PatcherErr result) {
    ML_LOG(@"所有流程完成回调(finishCallback): 结果: %d", (int)result);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (result == kErrOk) {
        //下载完成，给下载后资源目录赋权限
        [patcher modifyPatcherDownloadResPathPerm:nil];
    }
    if (patcher.finishCallback) {
        [patcher dispatchBlock:^{
            patcher.finishCallback((int)result);
        }];
    }
}

static void onFixResourceFileStart(int64_t totalResouceByteSize, int64_t totalFixedResourceSize, int64_t CurrentResourceTotalSize, PatcherFileOper file_oper, const char* current_file) {
    ML_LOG(@"修复资源回调(fixResourceCallback): 总资源字节数: %@, 总修复资源字节数: %@, 当前资源总字节数: %@, 文件操作: %@, 当前修复文件: %s", ByteToCapacity(totalResouceByteSize), ByteToCapacity(totalFixedResourceSize), ByteToCapacity(CurrentResourceTotalSize), FileOperToString(file_oper), current_file);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.fixResourceFileStartCallback) {
        NSString *currentFileStr = safeStringWithUTF8String(current_file);
        [patcher dispatchBlock:^{
            patcher.fixResourceFileStartCallback(totalResouceByteSize, totalFixedResourceSize, CurrentResourceTotalSize, (int)file_oper, currentFileStr);
        }];
    }
}

static void onLowSpeedNotify(const char* currentCDN, int64_t currentSpeedBytesPerSecond, const char* reverse) {
    ML_LOG(@"低速通知回调(lowSpeedNotifyCallback): 当前CDN: %@, 当前速度字节数: %@, 反转: %s", currentCDN, SpeedToString(currentSpeedBytesPerSecond), reverse);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.lowSpeedNotifyCallback) {
        NSString *currentCDNStr = safeStringWithUTF8String(currentCDN);
        NSString *reverseStr = safeStringWithUTF8String(reverse);
        [patcher dispatchBlock:^{
            patcher.lowSpeedNotifyCallback(currentCDNStr, currentSpeedBytesPerSecond, reverseStr);
        }];
    }
}

static void onPreReleaseCheck(bool hasPreRelease, const char* preReleaseBranch, const char* preReleaseResVersion) {
    ML_LOG(@"预下载检查回调(preReleaseCheckCallback): 是否有预下载: %d, 预下载分支: %s, 预下载资源版本: %s", hasPreRelease, preReleaseBranch, preReleaseResVersion);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.preReleaseCheckCallback) {
        NSString *preReleaseBranchStr = safeStringWithUTF8String(preReleaseBranch);
        NSString *preReleaseResVersionStr = safeStringWithUTF8String(preReleaseResVersion);  
        [patcher dispatchBlock:^{       
            patcher.preReleaseCheckCallback(hasPreRelease, preReleaseBranchStr, preReleaseResVersionStr);
        }];
    }
}

static void onPreDownloadWithTags(uint64_t baseBytes, uint64_t will_download_baseBytes, uint64_t update_need_space_baseBytes, uint64_t AllBytes, uint64_t will_download_AllBytes,  uint64_t update_need_space_AllBytes, uint64_t free_diskspace_bytes) {
    ML_LOG(@"需要下载回调(preDownloadWithTagsCallback): 基础字节数: %@, 将要下载的基础字节数: %@, 需要空间的基础字节数: %@, 总字节数: %@, 将要下载的总字节数: %@, 需要空间的总字节数: %@, 剩余空间字节数: %@", ByteToCapacity(baseBytes), ByteToCapacity(will_download_baseBytes), ByteToCapacity(update_need_space_baseBytes), ByteToCapacity(AllBytes), ByteToCapacity(will_download_AllBytes), ByteToCapacity(update_need_space_AllBytes), ByteToCapacity(free_diskspace_bytes));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.preDownloadWithTagsCallback) {
        [patcher dispatchBlock:^{
            patcher.preDownloadWithTagsCallback(baseBytes, will_download_baseBytes, update_need_space_baseBytes, AllBytes, will_download_AllBytes, update_need_space_AllBytes, free_diskspace_bytes);
        }];
    }
}

static void onApplyPlayAssetsProgress(int totalPackage, int currentPackage, int64_t totalBytes, int64_t currentBytes) {
    ML_LOG(@"预下载进度回调(applyPlayAssetsProgressCallback): 总包数: %d, 已整理: %d, 总字节数: %@, 当前整理字节数: %@", totalPackage, currentPackage, ByteToCapacity(totalBytes), ByteToCapacity(currentBytes));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.applyPlayAssetsProgressCallback) {
        [patcher dispatchBlock:^{
            patcher.applyPlayAssetsProgressCallback(totalPackage, currentPackage, totalBytes, currentBytes);
        }];
    }
}

static void onCheckPoint(const char* key, const char* hint) {
    ML_LOG(@"打点回调(checkPointCallback): 打点key: %s, 打点提示: %s", key, hint);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.checkPointCallback) {
        NSString *keyStr = safeStringWithUTF8String(key);
        NSString *hintStr = safeStringWithUTF8String(hint);
        [patcher dispatchBlock:^{
            patcher.checkPointCallback(keyStr, hintStr);
        }];
    }
}

static void onLocalResCheckProgress(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, const char* currentResName, int64_t currentFileSize, int64_t currentCheckedSize) {
    ML_LOG(@"本地资源检查进度回调(localResCheckProgressCallback): 总文件数: %d, 已检查文件数: %d, 总字节数: %@, 已检查字节数: %@, 当前资源名: %s, 当前文件大小: %@, 当前已检查字节数: %@", totalFileCount, checkedFileCount, ByteToCapacity(totalBytes), ByteToCapacity(checkedBytes), currentResName, ByteToCapacity(currentFileSize), ByteToCapacity(currentCheckedSize));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.localResCheckProgressCallback) {
        NSString *currentResNameStr = safeStringWithUTF8String(currentResName);        
        [patcher dispatchBlock:^{
            patcher.localResCheckProgressCallback(totalFileCount, checkedFileCount, totalBytes, checkedBytes, currentResNameStr, currentFileSize, currentCheckedSize);
        }];
    }
}


static void onDownloadResCheckProgress(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, const char* currentResName, int64_t currentFileSize, int64_t currentCheckedSize) {
    ML_LOG(@"下载进度回调(downloadResCheckProgressCallback): 总文件数: %d, 已检查文件数: %d, 总字节数: %@, 已检查字节数: %@, 当前资源名: %s, 当前文件大小: %@, 当前已检查字节数: %@", totalFileCount, checkedFileCount, ByteToCapacity(totalBytes), ByteToCapacity(checkedBytes), currentResName, ByteToCapacity(currentFileSize), ByteToCapacity(currentCheckedSize));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.downloadResCheckProgressCallback) {
        NSString *currentResNameStr = safeStringWithUTF8String(currentResName);
        [patcher dispatchBlock:^{
            patcher.downloadResCheckProgressCallback(totalFileCount, checkedFileCount, totalBytes, checkedBytes, currentResNameStr, currentFileSize, currentCheckedSize);
        }];
    }
}

static void onServerListRefresh(int code, const char *desc, const char *filepath) {
    ML_LOG(@"服务器列表回调(serverListCallback): 状态码: %d, 描述: %s, 文件路径: %s", code, desc, filepath);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.serverListCallback) {
        NSString *descStr = safeStringWithUTF8String(desc);
        NSString *fileStr = safeStringWithUTF8String(filepath);
        [patcher dispatchBlock:^{
            patcher.serverListCallback(code, descStr, fileStr);
        }];
    }
}

static void onBaseResCheckProgress(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, const char* currentResName, int64_t currentFileSize, int64_t currentCheckedSize, void* ctx) {
    ML_LOG(@"基础资源检查进度回调(baseResCheckProgressCallback): 总文件数: %d, 已检查文件数: %d, 总字节数: %@, 已检查字节数: %@, 当前资源名: %s, 当前文件大小: %@, 当前已检查字节数: %@", totalFileCount, checkedFileCount, ByteToCapacity(totalBytes), ByteToCapacity(checkedBytes), currentResName, ByteToCapacity(currentFileSize), ByteToCapacity(currentCheckedSize));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.baseResCheckProgressCallback) {
        NSString *currentResNameStr = safeStringWithUTF8String(currentResName);
        [patcher dispatchBlock:^{
            patcher.baseResCheckProgressCallback(totalFileCount, checkedFileCount, totalBytes, checkedBytes, currentResNameStr, currentFileSize, currentCheckedSize);
        }];
    }
}

static void onBaseCheckResult(int code, const char* message, PatcherTagStatus resStatus, int64_t totalBytes, int64_t needDownloadBytes, int64_t needSpace, void* ctx) {
    ML_LOG(@"基础资源检查结果回调(baseCheckResultCallback): 状态码: %d, 消息: %s, 资源状态: %@, 总字节数: %@, 需要下载字节数: %@, 需要空间字节数: %@", code, message, PatcherTagStatusToString(resStatus), ByteToCapacity(totalBytes), ByteToCapacity(needDownloadBytes), ByteToCapacity(needSpace));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.baseCheckResultCallback) {
        NSString *messageStr = safeStringWithUTF8String(message);
        [patcher dispatchBlock:^{
            patcher.baseCheckResultCallback(code, messageStr, (WMPatcherTagStatus)resStatus, totalBytes, needDownloadBytes, needSpace);
        }];
    }
}

static void onAvailableVersions(int code, const char* desc, const char* content, void* ctx) {
    ML_LOG(@"可用版本回调(availableVersionsCallback): 状态码: %d, 描述: %s, 内容: %s", code, desc, content);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.availableVersionsCallback) {
        NSString *descStr = safeStringWithUTF8String(desc);
        NSString *contentStr = safeStringWithUTF8String(content);
        [patcher dispatchBlock:^{
            patcher.availableVersionsCallback(code, descStr, contentStr);
        }];
    }
}

static void onEnvironmentInfo(int code, const char* desc, const char* content, void* ctx) {
    ML_LOG(@"版本信息回调(environmentInfoCallback): 状态码: %d, 描述: %s, 内容: %s", code, desc, content);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.environmentInfoCallback) {
        NSString *descStr = safeStringWithUTF8String(desc);
        NSString *contentStr = safeStringWithUTF8String(content);
        [patcher dispatchBlock:^{
            patcher.environmentInfoCallback(code, descStr, contentStr);
        }];
    }
}

static void onBranchVersionInfo(int code, const char* desc, const char* content, void* ctx) {
    ML_LOG(@"多游戏版本信息回调(environmentInfoCallback): 状态码: %d, 描述: %s, 内容: %s", code, desc, content);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.branchVersionInfoCallback) {
        NSString *descStr = safeStringWithUTF8String(desc);
        NSString *contentStr = safeStringWithUTF8String(content);
        [patcher dispatchBlock:^{
            patcher.branchVersionInfoCallback(code, descStr, contentStr);
        }];
    }
}

static int onCheckHashProgress(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, const char* currentResName, int64_t currentFileSize, int64_t currentCheckedSize, void* ctx) {
    ML_LOG(@"检查资源hash进度回调(baseResCheckProgressCallback): 总文件数: %d, 已检查文件数: %d, 总字节数: %@, 已检查字节数: %@, 当前资源名: %s, 当前文件大小: %@, 当前已检查字节数: %@", totalFileCount, checkedFileCount, ByteToCapacity(totalBytes), ByteToCapacity(checkedBytes), currentResName, ByteToCapacity(currentFileSize), ByteToCapacity(currentCheckedSize));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.checkResHashProgressCallback) {
        NSString *currentResNameStr = safeStringWithUTF8String(currentResName);
        [patcher dispatchBlock:^{
            patcher.checkResHashProgressCallback(totalFileCount, checkedFileCount, totalBytes, checkedBytes, currentResNameStr, currentFileSize, currentCheckedSize);
        }];
    }
    return (int)patcher.checkHashStatus;
}

static void onCheckHashResult(int code, const char* message, PatcherTagStatus resStatus, int64_t totalBytes, int64_t needDownloadBytes, int64_t needSpace, int totalcout, int needupdatecount, void* ctx) {
    ML_LOG(@"基础资源检查结果回调(baseCheckResultCallback): 状态码: %d, 消息: %s, 资源状态: %@, 总字节数: %@, 需要下载字节数: %@, 需要空间字节数: %@, 总个数: %d, 需要修复的个数: %d", code, message, PatcherTagStatusToString(resStatus), ByteToCapacity(totalBytes), ByteToCapacity(needDownloadBytes), ByteToCapacity(needSpace), totalcout, needupdatecount);
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.checkResHashResultCallback) {
        NSString *messageStr = safeStringWithUTF8String(message);
        [patcher dispatchBlock:^{
            patcher.checkResHashResultCallback(code, messageStr, (WMPatcherTagStatus)resStatus, totalBytes, needDownloadBytes, needSpace, totalcout, needupdatecount);
        }];
    }
}

static void onSpeedTestResult(int code, const char* message, int64_t bytesPerSecond, void* ctx) {
    ML_LOG(@"测速结果回调(baseCheckResultCallback): 状态码: %d, 消息: %s, 下载速度: %@/s ", code, message, ByteToCapacity(bytesPerSecond));
    WMMacPatcher *patcher = [WMMacPatcher sharedInstance];
    if (patcher.speedTestCallback) {
        NSString *messageStr = safeStringWithUTF8String(message);
        [patcher dispatchBlock:^{
            patcher.speedTestCallback(code, messageStr, bytesPerSecond);
            patcher.speedTestCallback = nil;
        }];
    }
}

#pragma mark - Public Methods

- (int)setupPatcherWithConfigPath:(NSString *)configPath pathInfo:(WMPatcherPathInfo *)pathInfo deviceId:(NSString *)deviceId mediaId:(NSString *)mediaId {
    
    memset(&_callbacks, 0, sizeof(_callbacks));
    
    _callbacks.versionCallback = onVersionCheck;
    _callbacks.predownloadlCallback = onPreDownload;
    _callbacks.downloadCallback = onDownload;
    _callbacks.fixResCallback = onFixResource;
    _callbacks.stateBeginCallback = onStateStart;
    _callbacks.stateEndCallback = onStateEnd;
    _callbacks.finishCallback = onUpdateFinish;

    _callbacks.fixResFileStart = onFixResourceFileStart;
    _callbacks.localresCheckProgress = onLocalResCheckProgress;
    _callbacks.downloadresCheckProgress = onDownloadResCheckProgress;
    _callbacks.lowSpeedNotify = onLowSpeedNotify;
    _callbacks.preReleaseCheckCallback = onPreReleaseCheck;
    _callbacks.PreDownloadWithTagsCallback = onPreDownloadWithTags;
    _callbacks.applyPlayAssetsProgressCallback = onApplyPlayAssetsProgress;

    PathInfo cPathInfo = {};
    if (pathInfo.resPath) {
        strncpy(cPathInfo.resPath, pathInfo.resPath.UTF8String, MIN(strlen(pathInfo.resPath.UTF8String), PW_PATCHER_PATH_LEN-1));
        self.patcherResPath = pathInfo.resPath;
    }
    if (pathInfo.patcherPath) {
        strncpy(cPathInfo.patcherPath, pathInfo.patcherPath.UTF8String, MIN(strlen(pathInfo.patcherPath.UTF8String), PW_PATCHER_PATH_LEN-1));
    }
    if (pathInfo.apkPath) {
        strncpy(cPathInfo.apkPath, pathInfo.apkPath.UTF8String, MIN(strlen(pathInfo.apkPath.UTF8String), PW_PATCHER_PATH_LEN-1));
    }
    if (pathInfo.serverListPath) {
        strncpy(cPathInfo.serverListPath, pathInfo.serverListPath.UTF8String, MIN(strlen(pathInfo.serverListPath.UTF8String), PW_PATCHER_PATH_LEN-1));
    }
    if (pathInfo.logPath) {
        strncpy(cPathInfo.logPath, pathInfo.logPath.UTF8String, MIN(strlen(pathInfo.logPath.UTF8String), PW_PATCHER_PATH_LEN-1));
    }

    /**
     enum LOG_LEVEL {
         P_LOG_TRACE = 0,
         P_LOG_DEBUG,
         P_LOG_INFO,
         P_LOG_WARN,
         P_LOG_ERROR,
         P_LOG_SLIENT,
     };
     */
    int result = pw_patcher_init_fromJsonFile(configPath.UTF8String, kDefaultLogLevel, &cPathInfo, &_callbacks, deviceId.UTF8String, mediaId.UTF8String);
    if (result == 0) {
        [self startCallbackTimer];
    } else {
        [self stopCallbackTimer];
        NSLog(@"WMMacPatcher init failed: %d", result); 
    }
    
    //Patcher内部已支持自动打点，不再单独调用
//    pw_patcher_set_checkpoint_callback(onCheckPoint);
    return result;
}

// // 获取可用版本 返回值为json 格式
// `[{"BranchName":"branch1", "Version":"0.0.1"}, {"BranchName":"branch1", "Version":"0.0.2"}, {"BranchName":"branch1", "Version":"0.0.3"}]`
// typedef void(*OnAvailabalVersions)(int code, const char* desc, const char* content, void* ctx);

- (void)availableVersionCheck:(void(^)(NSArray<WMPatcherAvailableVersionInfo *> *versionInfos, NSError *error))callback {
    // 资源版本检查
    if (!callback) {
        return;
    }
    @wm_weakify(self)
    self.availableVersionsCallback = ^(int code, NSString *desc, NSString *content) {
        @wm_strongify(self)
        NSError *error = nil;
        if (code != 0) {
            error = [self errorWithCode:code description:[NSString stringWithFormat:@"%@(%@)",content?:@"",desc?:@""]];
            callback(nil, error);
            return;
        }
        NSData *data = [content dataUsingEncoding:NSUTF8StringEncoding];    
        if (!data) {
            callback(nil, [self errorWithCode:code description:[NSString stringWithFormat:@"%@(%@)",content?:@"",@"Invalid JSON format"]]);
            return;
        }
        // Json 解析
        NSArray<NSDictionary *> *jsonArray = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
        if (error) {
            callback(nil, error);
            return;
        }
        // 如果 JsonArray 不是数组，则返回错误
        if (![jsonArray isKindOfClass:[NSArray class]]) {
            callback(nil, [self errorWithCode:code description:[NSString stringWithFormat:@"%@(%@)",content?:@"",@"Invalid JSON format"]]);
            return;
        }
        // 解析
        NSMutableArray<WMPatcherAvailableVersionInfo *> *versionInfos = [NSMutableArray array];
        for (NSDictionary *json in jsonArray) {
            WMPatcherAvailableVersionInfo *versionInfo = [WMPatcherAvailableVersionInfo new];
            versionInfo.branchName = json[@"BranchName"] ?: @"";
            versionInfo.version = json[@"Version"] ?: @"";
            [versionInfos addObject:versionInfo];
        }
        callback(versionInfos, error);
    };
    pw_patcher_request_available_versions(onAvailableVersions, NULL);
}

- (void)changeVersion:(NSString *)version
{
    pw_patcher_set_version([version cStringUsingEncoding:NSUTF8StringEncoding]);
}

// 版本信息
- (void)requestEnvironmentInfo:(void(^)(WMPatcherEnvironmentInfo *environmentInfo, NSError *error))callback {
    if (!callback) {
        return;
    }
    @wm_weakify(self)
    self.environmentInfoCallback = ^(int code, NSString *desc, NSString *content) {
        @wm_strongify(self)
        NSError *error = nil;
        if (code != 0) {
            error = [self errorWithCode:code description:[NSString stringWithFormat:@"%@(%@)",content?:@"",desc?:@""]];
            callback(nil, error);
            return;
        }
        // Json 解析    
        NSData *data = [content dataUsingEncoding:NSUTF8StringEncoding];
        if (!data) {
            callback(nil, [self errorWithCode:code description:[NSString stringWithFormat:@"%@(%@)",content?:@"",@"Invalid JSON format"]]);
            return;
        }

        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
        if (error) {
            callback(nil, error);
            return;
        }
        WMPatcherEnvironmentInfo *environmentInfo = [WMPatcherEnvironmentInfo new];
        environmentInfo.environment = json[@"Environment"] ?: @"";
        environmentInfo.local = json[@"Local"] ?: @"";
        environmentInfo.preRelease = json[@"PreRelease"] ?: @"";
        environmentInfo.remote = json[@"Remote"] ?: @"";
        callback(environmentInfo, error);
    };
    pw_patcher_request_versioninfo(&onEnvironmentInfo, NULL);
}


- (void)requestBranchVersionInfo:(BOOL)async
                      configPath:(nullable NSString *)configPath
                   configContent:(nullable NSString *)configContent
                     patcherPath:(nullable NSString *)patcherPath
                  resultCallback:(void(^)(WMPatcherEnvironmentInfo * _Nullable environmentInfo, NSError * _Nullable error))callback
{
    if (!callback) {
        return;
    }
    @wm_weakify(self)
    self.branchVersionInfoCallback = ^(int code, NSString *desc, NSString *content) {
        @wm_strongify(self)
        NSError *error = nil;
        if (code != 0) {
            error = [self errorWithCode:code description:[NSString stringWithFormat:@"%@(%@)",content?:@"",desc?:@""]];
            callback(nil, error);
            return;
        }
        // Json 解析
        NSData *data = [content dataUsingEncoding:NSUTF8StringEncoding];
        if (!data) {
            callback(nil, [self errorWithCode:code description:[NSString stringWithFormat:@"%@(%@)",content?:@"",@"Invalid JSON format"]]);
            return;
        }

        NSDictionary *json = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
        if (error) {
            callback(nil, error);
            return;
        }
        WMPatcherEnvironmentInfo *environmentInfo = [WMPatcherEnvironmentInfo new];
        environmentInfo.environment = json[@"Environment"] ?: @"";
        environmentInfo.local = json[@"Local"] ?: @"";
        environmentInfo.preRelease = json[@"PreRelease"] ?: @"";
        environmentInfo.remote = json[@"Remote"] ?: @"";
        callback(environmentInfo, error);
    };
    pw_patcher_request_branch_version(async, configPath.UTF8String, configContent.UTF8String, patcherPath.UTF8String, &onBranchVersionInfo, NULL);
}

// APPUPDATEAPI int pw_patcher_base_check_resource(const char* tags, OnBaseCheckResult cbResult, OnBaseResCheckProgress cbProgress, void* ctx);

- (int)baseCheckResource:(NSString *)tags {
    return pw_patcher_base_check_resource(tags.UTF8String, onBaseCheckResult, onBaseResCheckProgress, nil);
}

// int pw_patcher_doupdate_withtags(const char* tags, bool clearResource);

- (int)doUpdateWithTags:(NSString *)tags clearResource:(BOOL)clearResource {
    return pw_patcher_doupdate_withtags(tags.UTF8String, clearResource);
}   

- (void)cancelUpdate {
    pw_patcher_cancelUpdate();
}

- (void)uninit {
    [self stopCallbackTimer];
    pw_patcher_uninit();

    self.patcherResPath = nil;
    self.versionCallback = nil;
    self.preDownloadCallback = nil;
    self.downloadCallback = nil;
    self.fixResourceCallback = nil;
    self.stateCallback = nil;
    self.finishCallback = nil;

    self.localResCheckProgressCallback = nil;
    self.downloadResCheckProgressCallback = nil;
    self.fixResourceFileStartCallback = nil;
    self.lowSpeedNotifyCallback = nil;
    self.preReleaseCheckCallback = nil;
    self.preDownloadWithTagsCallback = nil;
    self.applyPlayAssetsProgressCallback = nil;

    self.checkPointCallback = nil;
    self.serverListCallback = nil;

    self.baseResCheckProgressCallback = nil;
    self.baseCheckResultCallback = nil;
}

- (NSString *)getServerListFile {
    const char *file = pw_patcher_get_serverlist_file();
    return [self safeStringWithUTF8String:file];
}

- (void)refreshServerList:(void(^)(int code, NSString *desc, NSString *filepath))callback async:(BOOL)async {
    self.serverListCallback = callback;
    pw_patcher_refresh_serverlist(onServerListRefresh, async);
}

- (NSString *)getCDNList {
    const char *list = pw_patcher_get_cdn_list();
    return [self safeStringWithUTF8String:list];
}

- (int)setCDN:(NSString *)url {
    if (!url) return -1;
    return pw_patcher_set_cdn(url.UTF8String);
}

- (int)setMaxSpeed:(int64_t)KBPerSecond {
    return pw_patcher_set_max_speed(KBPerSecond);
}

- (NSString *)getLocalResVersion {
    const char *version = pw_patcher_get_local_resversion();
    return [self safeStringWithUTF8String:version];
}

- (NSString *)getLocalAppVersion {
    const char *version = pw_patcher_get_local_appversion();
    return [self safeStringWithUTF8String:version];
}

- (NSString *)getVersion {
    const char *version = pw_patcher_version();
    return [self safeStringWithUTF8String:version];
}


- (NSError *)errorWithCode:(int)code description:(NSString *)desc {
    return [NSError errorWithDomain:kWMMacPatcherErrorDomain 
                             code:code 
                         userInfo:@{NSLocalizedDescriptionKey: desc ?: @"Unknown error"}];
}

// 添加工具方法
- (NSString *)safeStringWithUTF8String:(const char *)cString {
    return safeStringWithUTF8String(cString);
}

- (int)checkResourceHash:(NSString *)tags {
    self.checkHashStatus = WMCheckHashStatusNormal;
    return pw_patcher_check_resource_hash(tags.UTF8String, onCheckHashResult, onCheckHashProgress, nil);
}

- (void)cancelCheckResourceHash {
    self.checkHashStatus = WMCheckHashStatusCancel;
}

- (NSArray<NSString *> *)getExceptionFiles
{
    const char* exceptionFiles = pw_patcher_get_exception_files();
    NSString *exceptionFilesStr = safeStringWithUTF8String(exceptionFiles);
    NSData *data = [exceptionFilesStr dataUsingEncoding:NSUTF8StringEncoding];
    if (!data) {
        return nil;
    }
    NSError *error = nil;
    NSArray<NSString *> *jsonArray = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
    if (error) {
        return nil;
    }

    if (![jsonArray isKindOfClass:[NSArray class]]) {
        return nil;
    }
    return  jsonArray;
}

- (void)setExceptionResolveType:(WMResolveFileExceptionType)resolveType
{
    pw_patcher_set_exception_resolve_type((int)resolveType);
}

// 获取取所有资源大小
- (int64_t)getTotalRemoteSize:(NSString *)tags
{
    return pw_patcher_get_total_remote_size("");
}
// 测速
- (void)speedTest:(int)time completion:(void(^)(int code, NSString *message, int64_t bytesPerSecond))completion
{
    [WMMacPatcher sharedInstance].speedTestCallback = completion;
    pw_patcher_start_speedtest(time, "", onSpeedTestResult, nil);
}

- (void)setUserdataWorkDirInfo:(NSDictionary *)workDirInfo
{
    NSString *workDirJsonString = [workDirInfo wm_jsonStringEncoded];
    if(workDirJsonString) {
        pw_patcher_set_work_dir(workDirJsonString.UTF8String);
    }
}

+ (int64_t)getDeviceFreespace
{
    return pw_patcher_get_freespace();
}

+ (NSString*)byteToCapacity:(uint64_t)bytes
{
    return ByteToCapacity(bytes);
}

+ (NSString*)progressToString:(double)progress
{
    return ProgressToString(progress);
}

+ (NSString*)speedToString:(uint64_t)speed
{
    return SpeedToString(speed);
}
@end
