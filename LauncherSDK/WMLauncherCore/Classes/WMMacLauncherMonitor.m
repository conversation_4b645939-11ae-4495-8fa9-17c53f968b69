//
//  WMMacLauncherMonitor.m
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/12.
//

#import "WMMacLauncherMonitor.h"
#import <CrashSDK/CrashSDK.h>
#import <WPAnalysisSDK/LHAnalysis.h>
#import "WMMacLauncherLogger.h"

@interface WMMacLauncherMonitor()
@property (atomic, strong) NSString *eventOSType;
@end


@implementation WMMacLauncherMonitor
static WMMacLauncherMonitor *sharedInstance = nil;
+ (WMMacLauncherMonitor *)sharedInstance
{
    static dispatch_once_t pred;
    dispatch_once(&pred, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {

    }
    return self;
}

- (void)changeEventOSType:(NSString *)osType
{
    if (osType && osType.length > 0) {
        self.eventOSType = osType;
    } else {
        self.eventOSType = nil;
    }
}

// 初始化crashSDK
+ (void)setupCrashAppId:(NSString *)appID
                 appKey:(NSString *)appKey
              isOversea:(BOOL)isOversea
{
    CrashConfig *config = [[CrashConfig alloc] init];
    if(isOversea) {
        config.area = CrashAreaOverseas;//海外
    } else {
        config.area = CrashAreaMainland;//大陆
    }
    [[CrashController sharedInstance] startWithAppId:appID appKey:appKey config:config];
}

// 初始化统计SDK
+ (void)setupAnalysisAppId:(NSString *)appID
                 channelId:(NSString *)channelId
{
//    [LHAnalysis logEnable:true];
    [LHAnalysis startWithOneAppId:appID channelId:channelId];
}

+ (void)setupAnalysisMediaId:(nullable NSString *)mediaid
{
    LHAnalysis.sharedInstance.mediaID = mediaid;
}
       
+ (void)event:(NSString *)eventId attributes:(NSDictionary *)attributes
{
    NSMutableDictionary *allAttributes = [NSMutableDictionary dictionaryWithDictionary:attributes];
    if (WMMacLauncherMonitor.sharedInstance.eventOSType) {
        [allAttributes setObject:WMMacLauncherMonitor.sharedInstance.eventOSType forKey:@"ostype"];
    }
    
    [LHAnalysis event:eventId attributes:allAttributes taskId:nil];
}
@end
