//
//  WMMacPatcher.h
//  WMMacLauncherCore
//
//  Created by pwrd on 2024/11/1.
//

#import <Foundation/Foundation.h>
#import "WMMacPatcherDefines.h"

NS_ASSUME_NONNULL_BEGIN

@interface WMMacPatcher : NSObject

// 版本检查结果
@property (nonatomic, copy) void(^__nullable versionCallback)(WMPatcherVerCheck result, WMPatcherVersionInfo *versionInfo);
// 需要下载的字节数, 更新需要的磁盘空间, 当前磁盘剩余空间
@property (nonatomic, copy) void(^__nullable preDownloadCallback)(uint64_t willDownloadBytes, uint64_t updateNeedSpaceBytes, uint64_t freeDiskspaceBytes);
// 下载回调，下载信息  totalsize: 下载字节总量；进度progress: 四位小数；speed: 单位byte/s
@property (nonatomic, copy) void(^__nullable downloadCallback)(uint64_t totalsize, double progress, uint64_t speed);
// 修复资源回调，资源文件处理进度
@property (nonatomic, copy) void(^__nullable fixResourceCallback)(uint32_t totalcount, uint32_t remaincount, int fileOper, NSString *currentFixedFile);
// 状态回调，状态开始和结束
@property (nonatomic, copy) void(^__nullable stateCallback)(WMPatcherState state, BOOL isStart);
// 流程结束回调，必触发
@property (nonatomic, copy) void(^__nullable finishCallback)(int result);

// 本地资源检查进度
@property (nonatomic, copy) void(^__nullable localResCheckProgressCallback)(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, NSString *currentResName, int64_t currentFileSize, int64_t currentCheckedSize);
// 需要下载的资源检查进度
@property (nonatomic, copy) void(^__nullable downloadResCheckProgressCallback)(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, NSString *currentResName, int64_t currentFileSize, int64_t currentCheckedSize);

// 资源修复开始回调
@property (nonatomic, copy) void(^__nullable fixResourceFileStartCallback)(int64_t totalResouceByteSize, int64_t totalFixedResourceSize, int64_t CurrentResourceTotalSize, int file_oper, NSString *current_file);

// 低速通知回调，下载中触发
@property (nonatomic, copy) void(^__nullable lowSpeedNotifyCallback)(NSString *currentCDN, int64_t currentSpeedBytesPerSecond, NSString *reverse);

// 预下载检查回调，下载前触发，用于告知是否有预下载资源，以及预下载资源的版本信息，分支信息 
@property (nonatomic, copy) void(^__nullable preReleaseCheckCallback)(BOOL hasPreRelease, NSString *preReleaseBranch, NSString *preReleaseResVersion);

// 预下载资源检查进度
@property (nonatomic, copy) void(^__nullable preDownloadWithTagsCallback)(uint64_t baseBytes, uint64_t will_download_baseBytes, uint64_t update_need_space_baseBytes, uint64_t AllBytes, uint64_t will_download_AllBytes,  uint64_t update_need_space_AllBytes, uint64_t free_diskspace_bytes);

// 预下载资源检查进度
@property (nonatomic, copy) void(^__nullable applyPlayAssetsProgressCallback)(int totalPackage, int currentPackage, int64_t totalBytes, int64_t currentBytes);

// 错误上报,打点
@property (nonatomic, copy) void(^__nullable checkPointCallback)(NSString *key, NSString *hint);
// 获取服务器列表
@property (nonatomic, copy) void(^__nullable serverListCallback)(int code, NSString *desc, NSString *filepath);

// 基础资源检查进度
@property (nonatomic, copy) void(^__nullable baseResCheckProgressCallback)(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, NSString *currentResName, int64_t currentFileSize, int64_t currentCheckedSize);
// 基础资源检查结果
@property (nonatomic, copy) void(^__nullable baseCheckResultCallback)(int code, NSString *message, WMPatcherTagStatus resStatus, int64_t totalBytes, int64_t needDownloadBytes, int64_t needSpace);

// 资源修复hash检查进度
@property (nonatomic, copy) void(^__nullable checkResHashProgressCallback)(int totalFileCount, int checkedFileCount, int64_t totalBytes, int64_t checkedBytes, NSString *currentResName, int64_t currentFileSize, int64_t currentCheckedSize);
// 资源修复hash检查结果
@property (nonatomic, copy) void(^__nullable checkResHashResultCallback)(int code, NSString *message, WMPatcherTagStatus resStatus, int64_t totalBytes, int64_t needDownloadBytes, int64_t needSpace, int totalcout, int needupdatecount);

// 回调驱动间隔时长，默认 0.8s
@property (nonatomic, assign) NSTimeInterval callbackInterval;

- (instancetype)init NS_UNAVAILABLE;

+ (instancetype)new NS_UNAVAILABLE;

+ (instancetype)sharedInstance;

- (int)setupPatcherWithConfigPath:(NSString *)configPath pathInfo:(WMPatcherPathInfo *)pathInfo deviceId:(NSString *)deviceId mediaId:(NSString *)mediaId;

// 获取可用版本信息
- (void)availableVersionCheck:(void(^)(NSArray<WMPatcherAvailableVersionInfo *> *versionInfos, NSError *error))callback;

// 获取版本信息
- (void)requestEnvironmentInfo:(void(^)(WMPatcherEnvironmentInfo *environmentInfo, NSError *error))callback;

// 获取不同游戏版本的版本信息
- (void)requestBranchVersionInfo:(BOOL)async
                      configPath:(nullable NSString *)configPath
                   configContent:(nullable NSString *)configContent
                     patcherPath:(nullable NSString *)patcherPath
                  resultCallback:(void(^)(WMPatcherEnvironmentInfo * _Nullable environmentInfo, NSError * _Nullable error))callback;

// 设置指定版本
- (void)changeVersion:(NSString *)version;

// 资源检查
- (int)baseCheckResource:(NSString *)tags;

// 下载资源
- (int)doUpdateWithTags:(NSString *)tags clearResource:(BOOL)clearResource;

// 取消更新 
- (void)cancelUpdate;

// 反初始化SDK
- (void)uninit;

// 获取服务器列表文件
- (NSString *)getServerListFile;

// 刷新服务器列表
- (void)refreshServerList:(void(^)(int code, NSString *desc, NSString *filepath))callback async:(BOOL)async;

// 获取CDN列表
- (NSString *)getCDNList;

// 设置CDN
- (int)setCDN:(NSString *)url;

// 设置下载速度限制 (KB/s)
- (int)setMaxSpeed:(int64_t)KBPerSecond;

// 获取本地资源版本
- (NSString *)getLocalResVersion;

// 获取本地app版本  
- (NSString *)getLocalAppVersion;

// 获取SDK版本
- (NSString *)getVersion;

// 资源修复检查
- (int)checkResourceHash:(NSString *)tags;

// 取消资源修复检查
- (void)cancelCheckResourceHash;

// 获取异常损坏的文件
- (NSArray<NSString *> *)getExceptionFiles;

// 设置异常损坏文件的处理方式
- (void)setExceptionResolveType:(WMResolveFileExceptionType)resolveType;

// 获取取所有资源大小
- (int64_t)getTotalRemoteSize:(NSString *)tags;

// 测速方法
- (void)speedTest:(int)time completion:(void(^)(int code, NSString *message, int64_t bytesPerSecond))completion;

// 初始化前调用，设置userdata工作目录，确保patcher在对Launcher和游戏内做测试资源检查时，使用相同的路径
- (void)setUserdataWorkDirInfo:(NSDictionary *)workDirInfo;

// 获取剩余磁盘大小
+ (int64_t)getDeviceFreespace;

// 字节数转换为容量大小（如 TB,GB,MB,KB,B）
+ (NSString *)byteToCapacity:(uint64_t)bytes;

// progress: 0.000058 -> 0.06%，保留两位小数
+ (NSString *)progressToString:(double)progress;

// speed: 1024 -> 1KB/s， 需要转换成合适的单位
+ (NSString *)speedToString:(uint64_t)speed;
@end

NS_ASSUME_NONNULL_END
