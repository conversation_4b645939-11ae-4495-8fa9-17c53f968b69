//
//  WMMacLauncherMonitor.h
//  WMMacLauncherUI
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/12/12.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface WMMacLauncherMonitor : NSObject

+ (WMMacLauncherMonitor *)sharedInstance;

- (void)changeEventOSType:(nullable NSString *)osType;

// 初始化crashSDK
+ (void)setupCrashAppId:(NSString *)appID
                 appKey:(NSString *)appKey
              isOversea:(BOOL)isOversea;

// 初始化统计SDK
+ (void)setupAnalysisAppId:(NSString *)appID
                 channelId:(NSString *)channelId;
+ (void)setupAnalysisMediaId:(nullable NSString *)mediaid;
// 统计打点接口
+ (void)event:(NSString *)eventId
   attributes:(nullable NSDictionary *)attributes;
@end

NS_ASSUME_NONNULL_END
