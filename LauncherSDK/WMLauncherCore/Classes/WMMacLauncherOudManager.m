//
//  WMMacLauncherOudManager.m
//  WMMacLauncherCore
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/19.
//

#import "WMMacLauncherOudManager.h"
#import <WMWebOud/WMWebOud-macOS-umbrella.h>
#import <WPAnalysisSDK/LHAnalysis.h>
#import <WMCategories/WMFoundation.h>
#import <WMZipUtilities/WMZipUtilities-macOS-umbrella.h>
#import "WMMacLauncherLogger.h"
#import <WMAFNetworking/WMAFNetworking-macOS-umbrella.h>
#import <WMDevice/WMDevice.h>

static NSString *const kWMWebCOSPutAssetURL = @"api/asset/put";
typedef void(^WMMacZipProcessCompletion)(WMZCompressResult * _Nullable result, float progress);

@interface WMMacLauncherOudManager()<WMZCompressDelegate>
@property (nonatomic, assign) BOOL oudInitState;
@property (nonatomic, strong) WMWebOudConfig *config;
@property (nonatomic, copy) NSString *uid;
@property (nonatomic, copy) WMMacZipProcessCompletion zipProcessCompletion;
@property (nonatomic, assign) float feedbackProgress;
@property (nonatomic, assign) NSInteger areaType; // 0:大陆地区，使用腾讯云 1:私有云 2:海外地区，使用亚马逊云
@end

@implementation WMMacLauncherOudManager
static WMMacLauncherOudManager *sharedInstance = nil;

+ (WMMacLauncherOudManager *)sharedInstance
{
    static dispatch_once_t pred;
    dispatch_once(&pred, ^{
        sharedInstance = [[self alloc] init];
    });
    return sharedInstance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        _uid = [LHAnalysis uniqueDeviceId]; //使用ndid当uid
        _oudInitState = NO;
        _feedbackProgress = 0.0;
    }
    return self;
}

- (void)setupAppId:(NSString *)appID appKey:(NSString *)appKey channelId:(NSString *)channelId hostUrl:(NSString *)hostUrl areaType:(NSInteger)areaType
{
    if(_oudInitState) {
        return;
    }
    _oudInitState = YES;
#if DEBUG
    [WMWebOudManager sharedInstance].showLog = YES;
    [WMWebOudManager sharedInstance].showCOSLog = YES;
    [WMWebOudManager sharedInstance].showCheckLog = YES;
#endif
    
    // MARK: - 文件服务器SDK
    WMWebOudConfig *config = [[WMWebOudConfig alloc] init];
    config.appId = appID;
    config.appKey = appKey;
    config.channelId = channelId;
    config.baseURL = hostUrl;
    self.config = config;
    self.areaType = areaType;
    [WMWebOudManager setupWithConfig:config];
    
}

- (void)sendFeedbackWithContent:(NSString *)message
                     appVersion:(NSString *)appVersion
                    attachments:(NSArray<NSURL *> *)attachments
                     logFileDir:(NSArray<NSString *> *)logPaths
                       progress:(void(^)(WMFeedbackStepType step, float progress))progressCallback
                     completion:(void(^)(NSDictionary *result, NSError *error))completion {
    @wm_weakify(self)
    _feedbackProgress = 0.0;
    [self zipAttachmentsAndLauncherLog:logPaths attachments:attachments progress:^(float progress) {
        @wm_strongify(self)
        if (progressCallback) {
            self.feedbackProgress = progress*(1/3.0); //步骤一，压缩文件
            progressCallback(WMFeedbackStepTypeZip, self.feedbackProgress);
        }
    } completion:^(NSString *zipPath, NSError *error) {
        @wm_strongify(self)
        self.zipProcessCompletion = nil;
        if(error) {
            if (completion) {
                completion(nil, error);
            }
        } else {
            [self uploadZipFilePath:zipPath progress:progressCallback completion:^(NSString *url, NSError *error) {
                @wm_strongify(self)
                // 不管成功失败，都删除zip文件
                [self cleanZipFile:zipPath];
                // 步骤三 上报老虎服务器
                float currentProgress = self.feedbackProgress;
                if (progressCallback) {
                    // 模拟上报进度
                    currentProgress = self.feedbackProgress + 0.5*(1/3.0);
                    progressCallback(WMFeedbackStepTypeConfirm, currentProgress);
                }
                if (!error) {
                    [self putAssetWithUrlString:url message:message appVersion:appVersion success:^(NSURLSessionDataTask *task, id  _Nullable responseObject) {
                        if (progressCallback) {
                            progressCallback(WMFeedbackStepTypeConfirm, 1.0);
                        }
                        
                        if (completion) {
                            completion(responseObject, nil);
                        }
                    } failure:^(NSURLSessionDataTask * _Nullable task, NSError *error) {
                        if (completion) {
                            completion(nil, error);
                        }
                    }];
                } else {
                    if (completion) {
                        completion(nil, error);
                    }
                }
            }];
        }
    }];
    
}

- (void)uploadZipFilePath:(NSString *)zipPath
                 progress:(void(^)(WMFeedbackStepType step, float progress))progress
               completion:(void(^)(NSString *url, NSError *error))completion
{
    NSURL *zipURLPath = [NSURL fileURLWithPath:zipPath];
    NSString *extension = [zipURLPath pathExtension];
    NSString *md5String = [[zipURLPath absoluteString] wm_md5String];
    NSString *fileName = [NSString stringWithFormat:@"/object/%@/file/launchLogFile/%@.%@", self.config.appId, md5String, extension];
    
    /// @Discussion param需添加的key字段如下:
    /// @Discussion appId 应用ID, NSString (1.2.0新增)
    /// @Discussion uid 用户ID, NSString
    /// @Discussion objectType, NSNumber
    /// @Discussion businessType, NSString
    /// @Discussion fileName 资源名称(如果有后缀名, 需要带上后缀名) xxx.zip, NSString
    /// @Discussion isSupportEncrypt 是否启用加密上传逻辑, BOOL, YES 启动; NO 不启用, 默认不启用
    ///
    NSMutableDictionary *param = [NSMutableDictionary dictionary];
    [param wm_setSafeObject:self.uid forKey:@"uid"];
    [param wm_setSafeObject:@(0) forKey:@"objectType"];
    [param wm_setSafeObject:@"LaunchlogFile" forKey:@"businessType"];
    [param wm_setSafeObject:@(self.areaType) forKey:@"cloudType"];
    
    [[WMWebOudManager sharedInstance] updateCredentialWithParam:param completion:^{
        NSMutableDictionary *uploadParam = [NSMutableDictionary dictionaryWithDictionary:param];
        [uploadParam wm_setSafeObject:fileName forKey:@"fileName"];
        [uploadParam wm_setSafeObject:self.config.appId forKey:@"appId"];
        
        __block float currentProgress = self.feedbackProgress;
        [[WMWebOudManager sharedInstance] uploadFile:zipURLPath
                                               param:uploadParam
                                            progress:^(float progressValue) {
            if (progress) {
                currentProgress = self.feedbackProgress + progressValue*(1/3.0); //步骤二，上传zip文件
                progress(WMFeedbackStepTypeUpload ,currentProgress);
            }
        } completion:^(NSString * _Nonnull url, BOOL needUpload) {
            self.feedbackProgress = currentProgress;
            if (completion) {
                completion(url, nil);
            }
        } failure:^(NSError * _Nonnull error) {
            if (completion) {
                completion(nil, error);
            }
        }];
    } failure:^(NSError * _Nonnull error) {
        
        if (completion) {
            completion(nil, error);
        }
    }];
}

- (void)zipAttachmentsAndLauncherLog:(NSArray <NSString *> *)logPaths
                         attachments:(NSArray <NSURL*> *)attachmentURLs
                            progress:(void(^)(float progress))progressCallback
                          completion:(void(^)(NSString *zipPath, NSError *error))completion {
    
    NSString *fileName = [NSString stringWithFormat:@"feedback_%@.%@.zip", self.uid, [NSDate date]];
    NSString *tmpPath = [NSTemporaryDirectory() stringByAppendingPathComponent:[NSBundle mainBundle].bundleIdentifier];
    NSString *zipPath = [tmpPath stringByAppendingPathComponent:fileName];
    
    WMZCompressRequest *request = [[WMZCompressRequest alloc] initWithDestinationPath:zipPath];
    for (NSString *logPath in logPaths) {
        [request addEntriesInDirectory:logPath
                           filterBlock:^BOOL(NSString *filePath) {
            return [filePath.lastPathComponent hasPrefix:@"."]; // 忽略隐藏文件
        } compressionSelectionBlock:^(NSString * _Nonnull filePath, WMZCompressionMethod * _Nonnull compressionMethodOut, WMZCompressionLevel * _Nonnull compressionLevelOut) {
            ML_LOG(@"compression filePath= %@",filePath);
        }];
    }
    
    for (NSURL *entry in attachmentURLs) {
        WMZFileZipEntry *fileZipEntry = [[WMZFileZipEntry alloc] initWithFilePath:entry.path];
        [request addEntry:fileZipEntry];
    }
    WMZCompressOperation *operation = [[WMZCompressOperation alloc] initWithRequest:request delegate:self];
    self.zipProcessCompletion = ^(WMZCompressResult * _Nullable result, float progress) {
        if (result) {
            if (result.didSucceed) {
                if(completion) {
                    completion(result.destinationPath, nil);
                }
            } else {
                if(completion) {
                    completion(nil, result.operationError);
                }
            }
        } else {
            if(progressCallback) {
                progressCallback(progress);
            }
        }
        
    };
    [operation start];
}

- (void)cleanZipFile:(NSString *)filePath {
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error = nil;
    if ([fileManager fileExistsAtPath:filePath]) {
        [fileManager removeItemAtPath:filePath error:&error];
    }
}

#pragma mark - WMZCompressDelegate
- (void)compressOperation:(WMZCompressOperation *)op
    didCompleteWithResult:(WMZCompressResult *)result
{
    if (self.zipProcessCompletion) {
        self.zipProcessCompletion(result, 1.0);
    }
}

- (void)compressOperation:(WMZCompressOperation *)op
        didUpdateProgress:(float)progress
{
    if (self.zipProcessCompletion) {
        self.zipProcessCompletion(nil, progress);
    }
}
#pragma mark - 服务器接口上报
- (void)putAssetWithUrlString:(NSString *)urlString
                      message:(NSString *)message
                   appVersion:(NSString *)appVersion
                      success:(nullable void (^)(NSURLSessionDataTask *task, id _Nullable responseObject))success
                      failure:(nullable void (^)(NSURLSessionDataTask *_Nullable task, NSError *error))failure
{
    [self putAssetWithUrlString:urlString serverId:@"1" roleId:@"1" uuid:[self uuidString] uid:self.uid businessType:@"LaunchlogFile" message:message appVersion:appVersion success:success failure:failure];
}

- (void)putAssetWithUrlString:(NSString *)urlString
                     serverId:(NSString *)serverId
                       roleId:(NSString *)roleId
                         uuid:(NSString *)uuid
                          uid:(NSString *)uid
                 businessType:(NSString *)businessType
                      message:(NSString *)message
                   appVersion:(NSString *)appVersion
                      success:(nullable void (^)(NSURLSessionDataTask *task, id _Nullable responseObject))success
                      failure:(nullable void (^)(NSURLSessionDataTask *_Nullable task, NSError *error))failure {
    
    NSString *appKey = self.config.appKey ?: @"";
    NSString *URLString = [self.serverHost stringByAppendingPathComponent:kWMWebCOSPutAssetURL];
    
    NSMutableDictionary *extendInfo = [NSMutableDictionary dictionary];
    extendInfo[@"APPVersion"] = appVersion ?:@"";
    extendInfo[@"appId"] = self.config.appId ?: @"";
    extendInfo[@"content"] = message ?: @"";
    extendInfo[@"launcherVersion"] = [WMDevice sharedInstance].appVersion ?:@"";
    extendInfo[@"ndid"] = [LHAnalysis uniqueDeviceId] ?:@"";
    NSString *osInfo = [NSString stringWithFormat:@"%@ %@",[WMDevice sharedInstance].deviceModel?:@"", [WMDevice sharedInstance].iosVersion?:@""];
    extendInfo[@"os"] = osInfo ?:@"";
    NSString *extendString = [extendInfo wm_jsonStringEncoded];
    
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"channel"] = @"MacOS";
    params[@"channelId"] = self.config.channelId?: @"";
    params[@"mediaId"] = @"";
    params[@"appId"] = self.config.appId ?: @"";
    params[@"url"] = urlString ?: @"";
    params[@"serverId"] = serverId ?: @"";
    params[@"roleId"] = roleId ?: @"";
    params[@"uuid"] = uuid ?: @"";
    params[@"uid"] = uid ?: @"";
    params[@"businessType"] = businessType ?: @"";
    params[@"extend"] = extendString?: @"";

    NSMutableDictionary *signParams = [self params:params s:appKey];
    [self.sessionManager POST:URLString parameters:signParams progress:nil success:^(NSURLSessionDataTask * _Nonnull task, id  _Nullable responseObject) {
        NSMutableDictionary *responseDic;
        if([responseObject isKindOfClass:[NSDictionary class]]) {
            responseDic = [NSMutableDictionary dictionaryWithDictionary:responseObject];
        } else {
            responseDic = [NSMutableDictionary dictionaryWithDictionary:@{@"code" : @(0)}];
        }
        [responseDic setObject:uuid ?: @"" forKey:@"uuid"];
        if (success) {
            success(task,responseDic);
        }
    } failure:failure];
}

- (NSString *)uuidString {
    NSUUID *uuid = [NSUUID UUID];
    NSString *uuidString = [[uuid UUIDString] stringByReplacingOccurrencesOfString:@"-" withString:@""];
    
    return [uuidString lowercaseString];
}

// MARK: -
- (NSMutableDictionary *)params:(NSDictionary *)params s:(NSString *)key {
    NSMutableDictionary *signDic = [NSMutableDictionary dictionary];
    NSString *timeStamp = [NSString stringWithFormat:@"%lld", (long long) ([[NSDate date] timeIntervalSince1970] * 1000)];
    [signDic wm_setSafeObject:timeStamp forKey:@"t"];
    if (params) {
        [signDic addEntriesFromDictionary:params];
    }
    NSMutableString *srcStr = [NSMutableString string];
    NSArray *allKeys = signDic.allKeys;
    NSArray *sortedKeys = [allKeys sortedArrayUsingSelector:@selector(compare:)];
    for (NSString *k in sortedKeys) {
        id value = signDic[k];
        NSString *valueStr = @"";
        if ([value isKindOfClass:[NSString class]]) {
            valueStr = value;
        } else if ([value isKindOfClass:[NSNumber class]]) {
            //为了防止有人重写description方法
            valueStr = [value stringValue];
        } else {
            //除了NSString NSNumber之外的, 不用考虑, 入参之前都会转为这两种类型
        }
        [srcStr appendFormat:@"%@", valueStr];
    }
    [srcStr appendString:key];
    NSString *sign = [[srcStr copy] wm_md5String];
    [signDic wm_setSafeObject:sign forKey:@"sign"];
    return signDic;
}

// MARK: -
- (WMAFHTTPSessionManager *)sessionManager {
    WMAFHTTPSessionManager *networkManager = [WMAFHTTPSessionManager manager];
    WMAFSecurityPolicy *securityPolicy = [WMAFSecurityPolicy policyWithPinningMode:AFSSLPinningModeNone];
#ifdef DEBUG
    securityPolicy.allowInvalidCertificates = YES;
    securityPolicy.validatesDomainName = NO;
#else
    securityPolicy.allowInvalidCertificates = NO;
    securityPolicy.validatesDomainName = YES;
#endif
    networkManager.securityPolicy = securityPolicy;
    networkManager.requestSerializer.cachePolicy = NSURLRequestReloadIgnoringLocalCacheData;
    return networkManager;
}

- (NSString *)serverHost {
    return self.config.baseURL;
}
@end
