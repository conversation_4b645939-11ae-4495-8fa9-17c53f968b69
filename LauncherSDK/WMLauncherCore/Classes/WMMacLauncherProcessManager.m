#import "WMMacLauncherProcessManager.h"
#import "WMMacLauncherLogger.h"

@interface WMMacLauncherProcessManager ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, WMMacLauncherProcessTerminationHandler> *terminationHandlers;
@property (nonatomic, strong) NSMutableSet<NSString *> *observedBundleIdentifiers;

@end

@implementation WMMacLauncherProcessManager

- (instancetype)init {
    self = [super init];
    if (self) {
        _terminationHandlers = [NSMutableDictionary dictionary];
        _observedBundleIdentifiers = [NSMutableSet set];
        
        // 注册应用程序终止通知
        [[NSWorkspace sharedWorkspace].notificationCenter addObserver:self
                                                             selector:@selector(applicationDidTerminate:)
                                                                 name:NSWorkspaceDidTerminateApplicationNotification
                                                               object:nil];
        
        // 到前台时再主动检查一次
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(applicationWillBecomeActive:)
                                                     name:NSApplicationWillBecomeActiveNotification
                                                   object:nil];
    }
    return self;
}

- (void)dealloc {
    [[NSWorkspace sharedWorkspace].notificationCenter removeObserver:self];
}

#pragma mark - Public Methods

- (void)launchApplicationWithURL:(NSURL *)appURL
                bundleIdentifier:(NSString *)bundleIdentifier
                     environment:(NSDictionary<NSString *, NSString *> *)environment
                       arguments:(NSArray<NSString *> *)arguments
                      completion:(WMMacLauncherProcessLaunchCompletion)completion
              terminationHandler:(WMMacLauncherProcessTerminationHandler)terminationHandler {
    
    NSWorkspaceOpenConfiguration *config = [NSWorkspaceOpenConfiguration configuration];
    config.activates = YES;  // 启动后激活应用程序
    config.createsNewApplicationInstance = NO;
    config.allowsRunningApplicationSubstitution = NO; //当前已运行同bundleId但不同路径的App了，是否使用正在运行的App，默认YES，NO时会强制启动指定appURL的应用
    config.environment = environment; //透传的环境变量
    config.arguments = arguments;
    
    [self launchApplicationWithURL:appURL bundleIdentifier:bundleIdentifier config:config completion:completion terminationHandler:terminationHandler];
}


- (void)launchApplicationWithURL:(NSURL *)appURL
                bundleIdentifier:(NSString *)bundleIdentifier
                      config:(NSWorkspaceOpenConfiguration *)config
                      completion:(WMMacLauncherProcessLaunchCompletion)completion
              terminationHandler:(WMMacLauncherProcessTerminationHandler)terminationHandler {
    [[NSWorkspace sharedWorkspace] openApplicationAtURL:appURL
                                          configuration:config
                                      completionHandler:^(NSRunningApplication * _Nullable app, NSError * _Nullable error) {
        // 在主线程中调用回调
        dispatch_async(dispatch_get_main_queue(), ^{
            if (app && !error) {
                //下面这行再次调用activateWithOptions，会导致以下错误 [SetFrontProcessWithInfo]: CPS: Rejecting expired request for pid 55309. Time of the request being originated (237175592806125) is earlier than the time of the last activation (238054788475000).
//                [app activateWithOptions:(NSApplicationActivateAllWindows | NSApplicationActivateIgnoringOtherApps)];
                
                // 保存终止处理程序
                if (terminationHandler) {
                    NSString *bundleId =  bundleIdentifier ?: app.bundleIdentifier;
                    self.terminationHandlers[bundleId] = terminationHandler;
                    [self.observedBundleIdentifiers addObject:bundleId];
                }
            }
            
            if (completion) {
                completion(app, error);
            }
        });
    }];
}

- (void)addRunningApplication:(NSString *)bundleIdentifier terminationHandler:(WMMacLauncherProcessTerminationHandler)terminationHandler
{
    if (bundleIdentifier.length == 0) {
        return;
    }
    
    if ([self isApplicationRunning:bundleIdentifier] && ![self.observedBundleIdentifiers containsObject:bundleIdentifier]) {
        [self.observedBundleIdentifiers addObject:bundleIdentifier];
        self.terminationHandlers[bundleIdentifier] = terminationHandler;
    }
}

- (NSArray<NSURL *> *)searchApplicationWithBundleIdentifier:(NSString *)bundleIdentifier
                                                 searchPath:(nullable NSString *)searchPath {
    NSMutableArray<NSURL *> *results = [NSMutableArray array];
    
    if (@available(macOS 12.0, *)) {
        // 获取所有匹配的应用程序URL
        NSArray<NSURL *> *appURLs = [[NSWorkspace sharedWorkspace] URLsForApplicationsWithBundleIdentifier:bundleIdentifier];
        
        if (searchPath) {
            // 如果指定了搜索路径，只返回该路径下的应用程序
            for (NSURL *appURL in appURLs) {
                if ([[appURL.path lowercaseString] hasPrefix:[searchPath lowercaseString]]) {
                    [results addObject:appURL];
                }
            }
        } else {
            [results addObjectsFromArray:appURLs];
        }
    } else {
        // macOS 12.0以下的版本使用替代方法
        NSURL *appURL = [[NSWorkspace sharedWorkspace] URLForApplicationWithBundleIdentifier:bundleIdentifier];
        if (appURL) {
            if (!searchPath || [[appURL.path lowercaseString] hasPrefix:[searchPath lowercaseString]]) {
                [results addObject:appURL];
            }
        }
    }
    
    // 偶先路径和bid都正确时，依然找不到的情况，根据searchPath比对bundleIdentifier再获取一次
    if (results.count == 0) {
        if ([[NSFileManager defaultManager] fileExistsAtPath:searchPath]) {
            NSBundle *bundle = [NSBundle bundleWithPath:searchPath];
            NSString *appBundle = [bundle bundleIdentifier];
            if([bundleIdentifier isEqualToString:appBundle]) {
                [results addObject:[NSURL fileURLWithPath:searchPath]];
            }
        }
    }
    return results;
}

+ (NSString *)isApplicationRunning:(NSString *)bundleIdentifier {
    NSArray<NSRunningApplication *> *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];
    NSString *bundleId = bundleIdentifier;
    if (bundleId.length == 0) {
        return nil;
    }
    
    for (NSRunningApplication *app in runningApps) {
        ML_LOG(@"app.bundleIdentifier = %@, executableURL=%@",app.bundleIdentifier,app.executableURL);
        if ([app.bundleIdentifier isEqualToString:bundleId]) {
            return [app.bundleURL absoluteString];
        }
    }
    
    return nil;
}

- (NSString *)isApplicationRunning:(NSString *)bundleIdentifier {
    return [WMMacLauncherProcessManager isApplicationRunning:bundleIdentifier];
}

- (NSInteger)terminateApplicationWithBundleIdentifier:(NSString *)bundleIdentifier {
    NSArray<NSRunningApplication *> *runningApps = [[NSWorkspace sharedWorkspace] runningApplications];
    NSInteger terminatedCount = 0;
    
    for (NSRunningApplication *app in runningApps) {
        if ([app.bundleIdentifier isEqualToString:bundleIdentifier]) {
            if ([app terminate]) {
                terminatedCount++;
            }
        }
    }
    
    return terminatedCount;
}

- (NSInteger)terminateRunningApplication {
    
    NSArray *runningApps = [[NSArray alloc] initWithArray:[self.observedBundleIdentifiers allObjects] copyItems:YES];;
    NSInteger observedCount = self.observedBundleIdentifiers.count;
    
    for (NSString *bundleId in runningApps) {
        [self terminateApplicationWithBundleIdentifier:bundleId];
    }
    
    return observedCount;
}

- (void)removeAllTerminationHandlers
{
    if (self.terminationHandlers) {
        [self.terminationHandlers removeAllObjects];
    }
}

#pragma mark - Notifications

- (void)applicationDidTerminate:(NSNotification *)notification {
    NSRunningApplication *terminatedApp = notification.userInfo[NSWorkspaceApplicationKey];
    
    if (!terminatedApp) {
        return;
    }
    
    // 检查是否是我们正在观察的应用程序
    if ([self.observedBundleIdentifiers containsObject:terminatedApp.bundleIdentifier]) {
        // 获取并调用终止处理程序
        if ([[self.terminationHandlers allKeys] containsObject:terminatedApp.bundleIdentifier]) {
            WMMacLauncherProcessTerminationHandler handler = self.terminationHandlers[terminatedApp.bundleIdentifier];
            if (handler) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    handler(terminatedApp);
                });
            }
            // 清理
            [self.terminationHandlers removeObjectForKey:terminatedApp.bundleIdentifier];
        }
        [self.observedBundleIdentifiers removeObject:terminatedApp.bundleIdentifier];
    }
}

- (void)applicationWillBecomeActive:(NSNotification *)notification {
    if ([self.observedBundleIdentifiers allObjects].count == 0) {
        return;
    }
    
    NSArray *observedBundleIds = [[NSArray alloc] initWithArray:[self.observedBundleIdentifiers allObjects] copyItems:YES];;
    
    for (NSString *bundleId in observedBundleIds) {
        if (![self isApplicationRunning:bundleId] && [[self.terminationHandlers allKeys] containsObject:bundleId]) {
            WMMacLauncherProcessTerminationHandler handler = self.terminationHandlers[bundleId];
            if (handler) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    handler(nil);
                });
            }
            // 清理
            [self.terminationHandlers removeObjectForKey:bundleId];
            [self.observedBundleIdentifiers removeObject:bundleId];
        }
    }
    
}
@end
