//
//  WMMacPatcherDefines.h
//  WMMacLauncherCore
//
//  Created by pwrd on 2024/11/1.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

// 一般流程如下：
// 1. 触发 版本检查（WMPatcherStateVersionCheck），本地资源与服务器资源对比，决定是否需要更新
// 2. （如果需要更新） 触发 检索需更新的资源（WMPatcherStateResRetrieve），检索需要更新的资源
// 3. 触发 资源下载（WMPatcherStateResDownload），下载资源
// 4. 触发 资源修复（WMPatcherStateResFix），修复资源，比如（文件块合成大文件）

typedef NS_ENUM(NSInteger, WMPatcherState) {
    WMPatcherStateInit = 0, //  初始状态；辅助，该状态不触发任何回调函数
    WMPatcherStateVersionCheck = 1, 
    WMPatcherStateLocalResCheck = 2,
    WMPatcherStateResRetrieve = 3,
    WMPatcherStateResDownload = 4,
    WMPatcherStateResFix = 5,
    WMPatcherStateUpdateFinished = 10,
    WMPatcherStateTagResDownload = 11,
    WMPatcherStateTagResDownloadFinished = 12,
    WMPatcherStatePreReleaseDownload = 13,
    WMPatcherStatePreReleaseDownloadFinished = 14,
    WMPatcherStateUnInit = 100,
    WMPatcherStateError = 101
};

typedef NS_ENUM(NSInteger, WMPatcherVerCheck) {
    // 不需要更新   
    WMPatcherVerCheckNoUpdate = 0,
    // 需要更新应用
    WMPatcherVerCheckAppUpdate = 1,
    // 需要更新资源
    WMPatcherVerCheckResUpdate = 2,
    // 异常，具体原因 WMPatcherVersionInfo.versionDescription 中描述
    WMPatcherVerCheckException = 3
};
    
typedef NS_ENUM(NSInteger, WMPatcherTagStatus) {
    // 未知状态
    WMPatcherTagStatusUnknown = 0,
    // 需要更新
    WMPatcherTagStatusNeedUpdate = 1,
    // 已完成(不需要更新)
    WMPatcherTagStatusCompleted = 2,
};

// 资源操作类型
typedef NS_ENUM(NSInteger, WMPatcherFileOper) {
    // 未知操作
    WMPatcherFileOperUnkown = 0,
    // 新增
    WMPatcherFileOperAdd = 1,
    // 更新
    WMPatcherFileOperUpdate = 2,
    // 删除
    WMPatcherFileOperDelete = 3,
};

// 资源下载错误码
// 只声明了patcher_err.h中PatcherErr部分错误码，其他不需要单独处理的，直接查看patcher_err.h
typedef NS_ENUM(NSInteger, WMPatcherErr) {
    WMPatcherErrOk = 0,
    WMPatcherErrInsufficientSpace = 20,      // 本地磁盘空间不足
    WMPatcherErrCancel = 21,                 // 外部取消操作
    WMPatcherErrDiskFull = 29                // 磁盘已满
};
    
// 资源操作类型
typedef NS_ENUM(NSInteger, WMFeedbackStepType) {
    // 文件压缩
    WMFeedbackStepTypeZip = 0,
    // 文件上传
    WMFeedbackStepTypeUpload = 1,
    // 到老虎服务器关联确认
    WMFeedbackStepTypeConfirm = 2
};

// 资源修复状态
typedef NS_ENUM(NSInteger, WMCheckHashStatus)
{
    // 取消检查
    WMCheckHashStatusCancel = -1,
    // 正常继续
    WMCheckHashStatusNormal = 0
};

// 对异常损坏文件的解决方式
typedef NS_ENUM(NSInteger, WMResolveFileExceptionType)
{
    // 忽略
    WMResolveFileExceptionTypeIgnore = 0,
    // 删除，重新下载
    WMResolveFileExceptionTypeDelete = 1,
};

// 可用版本信息
@interface WMPatcherAvailableVersionInfo : NSObject
@property (nonatomic, copy) NSString *branchName;
@property (nonatomic, copy) NSString *version;
@end

// 环境信息
@interface WMPatcherEnvironmentInfo : NSObject
@property (nonatomic, copy) NSString *environment;
@property (nonatomic, copy) NSString *local;
@property (nonatomic, copy) NSString *preRelease;
@property (nonatomic, copy) NSString *remote;
@end

// 版本信息
@interface WMPatcherVersionInfo : NSObject
@property (nonatomic, copy) NSString *newestResVersion;
@property (nonatomic, copy) NSString *newestAppVersion;
@property (nonatomic, copy) NSString *minResVersion;
@property (nonatomic, assign) BOOL needUpdateResource;
@property (nonatomic, assign) BOOL needUpdateApp;
@property (nonatomic, copy) NSString *versionDescription;
@end

// 路径信息
@interface WMPatcherPathInfo : NSObject
@property (nonatomic, copy) NSString *cacheBasePath;        //patcher缓存基础路径
@property (nonatomic, copy) NSString *resPath;
@property (nonatomic, copy) NSString *patcherPath;
@property (nonatomic, copy) NSString *apkPath;
@property (nonatomic, copy) NSString *serverListPath;
@property (nonatomic, copy) NSString *logPath;
@end

NS_ASSUME_NONNULL_END 
