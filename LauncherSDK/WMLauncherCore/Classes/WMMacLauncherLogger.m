#import "WMMacLauncherLogger.h"

#if DEBUG
static BOOL _isLogEnabled = YES;
#else
static BOOL _isLogEnabled = NO;
#endif

void WMMacLauncherLog(const char *func,  int lineNumber, NSString *format, ...) {
    if (!_isLogEnabled) {
        return;
    }
    va_list args;
    va_start(args, format);
    NSLogv([NSString stringWithFormat:@"[WMMacLauncher] %s(%d): %@", func, lineNumber, format], args);
    va_end(args);
}   

@implementation WMMacLauncherLogger

+ (void)setLogEnabled:(BOOL)enabled {
    _isLogEnabled = enabled;
}

+ (BOOL)isLogEnabled {
    return _isLogEnabled;
}

@end
