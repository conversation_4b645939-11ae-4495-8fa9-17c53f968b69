#import "WMMacLauncherCore.h"
#import "WMMacPatcher.h"
#import "WMMacLauncherProcessManager.h"
#import "WMMacLauncherLogger.h"
#import <WPAnalysisSDK/LHAnalysis.h>
#import <WMCategories/WMFoundationMacro.h>
#import "WMMacLauncherOudManager.h"
#import "WMMacLauncherMonitor.h"

#pragma mark - 以下值需要在启动游戏前，通过设置环境变量的方式共享给游戏中的Launcher
//patcher初始化配置文件(1271_ZXMac_MacOS.json),需要把配置文件拷贝到游戏进程能访问的位置，文件名固定为PatcherConfig.json
#define LAUNCHER_CONFIG_ @"_PATCHER_CONFIG_PATH_"
//PathInfo.resPath
#define LAUNCHER_PATCHER_RES_PATH_ @"_PATCHER_RES_PATH_"
//PathInfo.patcherPath
#define LAUNCHER_PATCHER_SDK_PATH_ @"_PATCHER_SDK_PATH_"
//userdata目录，游戏内Patcher会创建Log/Patcher/Game目录，将日志放到这里，目录路径任意
#define LAUNCHER_PATCHER_USERDATA_PATH_ @"_PATCHER_USERDATA_PATH_"
//日志路径， pc端多端的时候发现有时候默认目录和多端的名字会重,游戏会写日志到这个目录下
#define LAUNCHER_PATCHER_LOG_PATH_ @"_PATCHER_LOG_PATH_"

enum {
    // has init
    kFlagHasInit = 1 << 0,
    // download resource
    kFlagIsDownloading = 1 << 1,

};


@interface WMMacLauncherCore()

@property (nonatomic, strong) NSString *configPath;
@property (nonatomic, strong) WMPatcherPathInfo *patcherPathInfo;
@property (nonatomic, copy) NSString *deviceId;
@property (nonatomic, copy) NSString *mediaId;

@property (nonatomic, strong) WMMacPatcher *patcher;
@property (nonatomic, strong) WMMacLauncherProcessManager *processManager;

// 操作锁
@property (nonatomic, strong) NSLock *operationLock;

// flag
@property (nonatomic, assign) uint32_t flag;

@property (nonatomic, assign) BOOL isDownloading;
@property (nonatomic, assign) NSInteger downloadingIndex;
@property (nonatomic, assign) WMStopDownloadType stopUpdateReason;
@end

@implementation WMMacLauncherCore

#pragma mark - Lifecycle

- (instancetype)init {
    self = [super init];
    if (self) {
        _patcher = [WMMacPatcher sharedInstance];
        _processManager = [[WMMacLauncherProcessManager alloc] init];
        _operationLock = [[NSLock alloc] init];
        _downloadingIndex = -1;
    }
    return self;
}

- (void)dealloc {
    [self uninitPatcher];
}

#pragma mark - Patcher Methods

+ (void)setLogEnabled:(BOOL)enabled {
    [WMMacLauncherLogger setLogEnabled:enabled];
}

- (NSString *)currentPatcherConfigPath {
    return self.configPath;
}

- (void)setCallbackInterval:(NSTimeInterval)interval {
    self.patcher.callbackInterval = interval;
}


- (void)setUserdataWorkDirInfo:(NSDictionary *)workDirInfo
{
    [self.patcher setUserdataWorkDirInfo:workDirInfo];
}

- (NSInteger)setupPatcherWithConfigPath:(NSString *)configPath pathInfo:(WMPatcherPathInfo *)pathInfo deviceId:(NSString *)deviceId mediaId:(NSString *)mediaId {
    [self.operationLock lock];
    // 从配置文件读取并解析配置
    if (self.flag & kFlagHasInit) {
        ML_LOG(@"patcher已经初始化");
        [self.operationLock unlock];
        return 0;
    }


    NSError *error = nil;
    NSData *configData = [NSData dataWithContentsOfFile:configPath options:0 error:&error];
    if (error) {
        ML_LOG(@"读取配置文件失败: %@", error);
        [self.operationLock unlock];
        return -1;
    }
    
    __unused NSDictionary *config = [NSJSONSerialization JSONObjectWithData:configData options:0 error:&error];
    if (error) {
        ML_LOG(@"解析配置文件失败: %@", error);
        [self.operationLock unlock];
        return -2;
    }

    if (!pathInfo) {
        ML_LOG(@"路径信息为空");
        [self.operationLock unlock];
        return -3;
    }

    self.configPath = configPath;
    self.patcherPathInfo = pathInfo;
    self.deviceId = deviceId.length > 0 ? deviceId : [LHAnalysis uniqueDeviceId];
    self.mediaId = mediaId ? mediaId : @"";

    // weak reference   
    @wm_weakify(self)
    self.patcher.versionCallback = ^(WMPatcherVerCheck result, WMPatcherVersionInfo *versionInfo) {
        // 主线程
        @wm_strongify(self)
        if (self.patcherVersionCallback) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.patcherVersionCallback(result, versionInfo);
            });
        }
    };

    self.patcher.downloadCallback = ^(uint64_t totalsize, double progress, uint64_t speed) {
        @wm_strongify(self)
        self.isDownloading = true;
        if (self.patcherDownloadCallback) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.patcherDownloadCallback(totalsize, progress, speed, self.downloadingIndex);
            });
        }
    };

    self.patcher.stateCallback = ^(WMPatcherState state, BOOL isStart) {
        @wm_strongify(self)
        if (self.patcherStateCallback) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.patcherStateCallback(state, isStart, self.downloadingIndex);
            });
        }
    };

    self.patcher.finishCallback = ^(int result) {
        @wm_strongify(self)
        self.isDownloading = false;
        if (self.patcherFinishCallback) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.patcherFinishCallback(result, self.downloadingIndex, self.stopUpdateReason);
            });
        }
    };

    self.patcher.preReleaseCheckCallback = ^(BOOL hasPreRelease, NSString *preReleaseBranch, NSString *preReleaseResVersion) {
        @wm_strongify(self)
        if (self.patcherPreReleaseCheckCallback) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.patcherPreReleaseCheckCallback(hasPreRelease, preReleaseBranch, preReleaseResVersion);
            });
        }
    };

    self.patcher.fixResourceCallback = ^(uint32_t totalcount, uint32_t remaincount, int fileOper, NSString *currentFixedFile) {
        @wm_strongify(self)
        if (self.patcherFixResourceCallback) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.patcherFixResourceCallback(totalcount, remaincount, (WMPatcherFileOper)fileOper, currentFixedFile, self.downloadingIndex);
            });
        }
    };

    self.patcher.fixResourceFileStartCallback = ^(int64_t totalResouceByteSize, int64_t totalFixedResourceSize, int64_t CurrentResourceTotalSize, int file_oper, NSString *current_file) {
        @wm_strongify(self)
        if (self.patcherFixResourceFileStartCallback) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.patcherFixResourceFileStartCallback(totalResouceByteSize, totalFixedResourceSize, CurrentResourceTotalSize, (WMPatcherFileOper)file_oper, current_file, self.downloadingIndex);
            });
        }
    };
    self.patcher.preDownloadCallback = ^(uint64_t willDownloadBytes, uint64_t updateNeedSpaceBytes, uint64_t freeDiskspaceBytes) {
        // unused
    };  

    self.patcher.checkPointCallback = ^(NSString *key, NSString *hint) {
        NSData *jsonData = [hint dataUsingEncoding:NSUTF8StringEncoding];
        // 2. 解析成 NSDictionary
        NSError *error;
        NSDictionary *hintDic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                                 options:0
                                                                   error:&error];
        [WMMacLauncherMonitor event:key attributes:hintDic];
        
    };

    self.patcher.serverListCallback = ^(int code, NSString *desc, NSString *filepath) {
        // unused
    };

    NSInteger result = [self.patcher setupPatcherWithConfigPath:self.configPath pathInfo:self.patcherPathInfo deviceId:self.deviceId mediaId:self.mediaId];
    if (result != 0) {
        ML_LOG(@"初始化patcher失败: %ld", (long)result);
    }
    self.flag |= kFlagHasInit;
    [self.operationLock unlock];
    return result;
}

- (NSString *)getLocalResVersion {
    return [self.patcher getLocalResVersion];
}

- (void)availableVersionCheck:(void(^)(NSArray<WMPatcherAvailableVersionInfo *> *versionInfos, NSError *error))callback {
    [self.patcher availableVersionCheck:callback];
}

- (void)requestEnvironmentInfo:(void(^)(WMPatcherEnvironmentInfo *environmentInfo, NSError *error))callback {
    [self.patcher requestEnvironmentInfo:callback];
}

- (void)requestBranchVersionInfo:(BOOL)async
                      configPath:(nullable NSString *)configPath
                   configContent:(nullable NSString *)configContent
                     patcherPath:(nullable NSString *)patcherPath
                  resultCallback:(void(^)(WMPatcherEnvironmentInfo * _Nullable environmentInfo, NSError * _Nullable error))callback {
    [self.patcher requestBranchVersionInfo:async configPath:configPath configContent:configContent patcherPath:patcherPath resultCallback:callback];
}

- (void)checkResource:(NSString *)tags progressCallback:(WMMacLauncherBaseResCheckProgressCallback)progressCallback resultCallback:(WMMacLauncherBaseCheckResultCallback)resultCallback {
    self.patcher.baseResCheckProgressCallback = progressCallback;
    self.patcher.baseCheckResultCallback = resultCallback;
    [self.patcher baseCheckResource:tags ? tags : @""];
}

- (void)updateResource:(NSString *)tags clearResource:(BOOL)clearResource downloadIndex:(NSInteger)index {
    self.downloadingIndex = index;
    [self.patcher doUpdateWithTags:tags clearResource:clearResource];
}

- (void)uninitPatcher {
    [self.operationLock lock];
    self.configPath = nil;
    self.patcherPathInfo = nil;
    self.downloadingIndex = -1;
    self.flag &= ~kFlagHasInit;
    [self.patcher uninit];
    [self.operationLock unlock];
}

-(void)stopUpdate:(WMStopDownloadType)type {
    self.stopUpdateReason = type;
    if (self.isDownloading) {
        [self.patcher cancelUpdate];
    } else {
        if (self.patcherFinishCallback) {
            dispatch_async(dispatch_get_main_queue(), ^{
                self.patcherFinishCallback(WMPatcherErrCancel, self.downloadingIndex, type);
            });
        }
    }
}

- (void)changeSelectedVersion:(NSString *)version
{
    [self.patcher changeVersion:version];
}

- (void)checkResourceHash:(NSString *)tags progressCallback:(WMMacLauncherCheckResHashProgressCallback)progressCallback resultCallback:(WMMacLauncherCheckResHashResultCallback)resultCallback {
    self.patcher.checkResHashProgressCallback = progressCallback;
    self.patcher.checkResHashResultCallback = resultCallback;
    [self.patcher checkResourceHash:tags ? tags : @""];
}

- (void)cancelCheckResourceHash {
    self.stopUpdateReason = WMStopDownloadTypeCancel;
    [self.patcher cancelCheckResourceHash];
    self.patcher.checkResHashProgressCallback = nil;
    self.patcher.checkResHashResultCallback = nil;
}

- (nullable NSArray<NSString *> *)getExceptionFiles
{
    return [self.patcher getExceptionFiles];
}

- (void)setExceptionResolveType:(WMResolveFileExceptionType)resolveType
{
    [self.patcher setExceptionResolveType:resolveType];
}

// 获取取所有资源大小
- (int64_t)getTotalRemoteSize:(NSString *)tags
{
    return [self.patcher getTotalRemoteSize:tags];
}
// 测速
- (void)speedTest:(int)time completion:(void(^)(int code, NSString *message, int64_t bytesPerSecond))completion
{
    [self.patcher speedTest:time completion:completion];
}

- (NSArray *)getCDNList {
    NSString *cdnListString = [self.patcher getCDNList];
    return [cdnListString componentsSeparatedByString:@";"];
}

- (int)setCDN:(NSString *)url {
    return [self.patcher setCDN:url];
}

- (int)setMaxSpeed:(int64_t)KBPerSecond {
    return [self.patcher setMaxSpeed:KBPerSecond];
}

#pragma mark - Process Management Methods
- (NSDictionary *)getEnvForGamePatcher
{
    if(!self.configPath || !self.patcherPathInfo ) {
        return nil;
    }
    NSString *configFile = self.configPath;
    //文件名固定为PatcherConfig.json
    NSString *destinationPath = [self.patcherPathInfo.cacheBasePath stringByAppendingPathComponent:@"PatcherConfig.json"];
    NSFileManager *fileManager = [NSFileManager defaultManager];
    NSError *error = nil;
    
    if ([fileManager fileExistsAtPath:destinationPath]) {
        // 如果配置文件已存在了，先删除再拷贝，防止版本不一致
        [fileManager removeItemAtPath:destinationPath error:&error];
    }
    
    // 拷贝PatcherConfig.json
    [fileManager copyItemAtPath:configFile toPath:destinationPath error:&error];
    // 创建游戏中Patcher使用userData目录
    NSString *gameTaskPatherUserData = [self.patcherPathInfo.logPath stringByAppendingPathComponent:@"gameUserData"];
    if ([fileManager fileExistsAtPath:gameTaskPatherUserData] == NO) {
        [fileManager createDirectoryAtPath:gameTaskPatherUserData
               withIntermediateDirectories:YES
                                attributes:nil
                                     error:&error];
    }
    
    NSString *gameTaskPatherLogPath = [WMMacLauncherCore getGameLogDataPathWithLogPath:self.patcherPathInfo.logPath];
    if ([fileManager fileExistsAtPath:gameTaskPatherLogPath] == NO) {
        [fileManager createDirectoryAtPath:gameTaskPatherLogPath
               withIntermediateDirectories:YES
                                attributes:nil
                                     error:&error];
    }
    /* 路径示例
     * cacheBasePath => "~/Library/Application Support/com.laohu.Launcher/{cachePath}"
     * resPath => "{cacheBasePath}/game"
     * patchPath => "{cacheBasePath}/patcher"
     * logPath => "{cacheBasePath}/log"
     * gameTaskPatherUserData => "{logPath}/gameUserData"
     * gameTaskPatherLogPath => "{logPath}/gameLogData"
     */
    NSDictionary *allEnvDic = @{LAUNCHER_CONFIG_:self.patcherPathInfo.cacheBasePath,
                                LAUNCHER_PATCHER_RES_PATH_:self.patcherPathInfo.resPath,
                                LAUNCHER_PATCHER_SDK_PATH_:self.patcherPathInfo.patcherPath,
                                LAUNCHER_PATCHER_USERDATA_PATH_:gameTaskPatherUserData,
                                LAUNCHER_PATCHER_LOG_PATH_:gameTaskPatherLogPath};
    
    return allEnvDic;
    
}

+ (NSString *)getGameLogDataPathWithLogPath:(NSString *)logPath
{
    return [logPath stringByAppendingPathComponent:@"gameLogData"];
}

- (void)launchApplicationWithURL:(NSURL *)appURL
                bundleIdentifier:(NSString *)bundleIdentifier
                       arguments:(NSArray<NSString *> *)arguments
                    completion:(nullable WMMacLauncherProcessLaunchCompletion)completion
             terminationHandler:(nullable WMMacLauncherProcessTerminationHandler)terminationHandler {
    
    NSDictionary *gameEnv = [self getEnvForGamePatcher];
    [self.processManager launchApplicationWithURL:appURL
                                 bundleIdentifier:bundleIdentifier
                                      environment:gameEnv
                                        arguments:arguments
                                     completion:completion
                              terminationHandler:terminationHandler];
}

- (NSArray<NSURL *> *)searchApplicationWithBundleIdentifier:(NSString *)bundleIdentifier
                                               searchPath:(nullable NSString *)searchPath {
    return [self.processManager searchApplicationWithBundleIdentifier:bundleIdentifier
                                                        searchPath:searchPath];
}

+ (NSString *)isApplicationRunning:(NSString *)bundleIdentifier {
    return [WMMacLauncherProcessManager isApplicationRunning:bundleIdentifier];
}

- (void)addRunningApplication:(NSString *)bundleIdentifier terminationHandler:(WMMacLauncherProcessTerminationHandler)terminationHandler {
    return [self.processManager addRunningApplication:bundleIdentifier terminationHandler:terminationHandler];
}

- (NSInteger)terminateApplicationWithBundleIdentifier:(NSString *)bundleIdentifier {
    return [self.processManager terminateApplicationWithBundleIdentifier:bundleIdentifier];
}

- (NSInteger)terminateRunningApplication {
    return [self.processManager terminateRunningApplication];
}

- (void)removeAllTerminationHandlers
{
    [self.processManager removeAllTerminationHandlers];
}

#pragma mark - Feedback

- (void)setupOudAppId:(NSString *)appID appKey:(NSString *)appKey channelId:(NSString *)channelId hostUrl:(NSString *)hostUrl areaType:(NSInteger)areaType
{
    [[WMMacLauncherOudManager sharedInstance] setupAppId:appID appKey:appKey channelId:channelId hostUrl:hostUrl areaType:areaType];
}

- (void)sendFeedbackWithContent:(NSString *)message
                     appVersion:(nullable NSString *)appVersion
                    attachments:(NSArray<NSURL *> *)attachments
                       progress:(void(^)(WMFeedbackStepType step, float progress))progress
                     completion:(void(^)(NSDictionary *result, NSError *error))completion
{
    NSMutableArray *logFilesArray = [NSMutableArray arrayWithArray:@[self.launcherLogDirPath, self.patcherPathInfo.logPath]];
    NSString *gameLogPath = [self getLatestGameLogsToTempDirectory];
    if(gameLogPath) {
        [logFilesArray addObject:gameLogPath];
    }
    [[WMMacLauncherOudManager sharedInstance] sendFeedbackWithContent:message appVersion:appVersion attachments:attachments logFileDir:logFilesArray progress:progress completion:completion];
}

// 过滤游戏日志，因为日志可能会很大，所以只取最后修改的两条日志上报
- (NSString *)getLatestGameLogsToTempDirectory {
    if (self.gameLogDirPath) {
        // 检查目录是否存在
        if (![[NSFileManager defaultManager] fileExistsAtPath:self.gameLogDirPath]) {
            return nil;
        }
        // 创建临时目录
        NSString *tempDir = [NSTemporaryDirectory() stringByAppendingPathComponent:[NSString stringWithFormat:@"%@_%.0f", [self.gameLogDirPath lastPathComponent], [[NSDate date] timeIntervalSince1970]]];
        [[NSFileManager defaultManager] createDirectoryAtPath:tempDir withIntermediateDirectories:YES attributes:nil error:nil];
        
        // 获取源目录下所有非隐藏文件并按修改时间排序
        NSArray *files = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:self.gameLogDirPath error:nil];
        files = [files filteredArrayUsingPredicate:[NSPredicate predicateWithFormat:@"NOT self BEGINSWITH '.'"]];
        files = [files sortedArrayUsingComparator:^NSComparisonResult(NSString *file1, NSString *file2) {
            NSString *path1 = [self.gameLogDirPath stringByAppendingPathComponent:file1];
            NSString *path2 = [self.gameLogDirPath stringByAppendingPathComponent:file2];
            NSDictionary *attrs1 = [[NSFileManager defaultManager] attributesOfItemAtPath:path1 error:nil];
            NSDictionary *attrs2 = [[NSFileManager defaultManager] attributesOfItemAtPath:path2 error:nil];
            return [attrs2[NSFileModificationDate] compare:attrs1[NSFileModificationDate]];
        }];
        
        // 拷贝最后修改的两个文件
        for(int i = 0; i < MIN(2, files.count); i++) {
            NSString *srcPath = [self.gameLogDirPath stringByAppendingPathComponent:files[i]];
            NSString *dstPath = [tempDir stringByAppendingPathComponent:files[i]];
            [[NSFileManager defaultManager] copyItemAtPath:srcPath toPath:dstPath error:nil];
        }
        return tempDir;
    }
    
    return nil;
}


@end

