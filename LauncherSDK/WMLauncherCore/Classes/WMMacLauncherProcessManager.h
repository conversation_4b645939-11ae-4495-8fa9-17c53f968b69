#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^WMMacLauncherProcessLaunchCompletion)(NSRunningApplication * _Nullable app, NSError * _Nullable error);
typedef void(^WMMacLauncherProcessTerminationHandler)(NSRunningApplication * _Nullable terminatedApp);

@interface WMMacLauncherProcessManager : NSObject
/**
 * 启动应用程序
 * @param appURL 应用程序的URL
 * @param bundleIdentifier 应用程序的Bundle Identifier
 * @param environment 透传给Application的环境变量
 * @param arguments 透传给Application的命令行参数
 * @param completion 启动完成的回调
 * @param terminationHandler 应用程序终止时的回调
 */
- (void)launchApplicationWithURL:(NSURL *)appURL
                bundleIdentifier:(NSString *)bundleIdentifier
                     environment:(NSDictionary<NSString *, NSString *> *)environment
                       arguments:(NSArray<NSString *> *)arguments
                    completion:(nullable WMMacLauncherProcessLaunchCompletion)completion
             terminationHandler:(nullable WMMacLauncherProcessTerminationHandler)terminationHandler;

/**
 * 根据Bundle Identifier搜索应用程序
 * @param bundleIdentifier 应用程序的Bundle Identifier
 * @param searchPath 搜索路径，如果为nil则在系统范围内搜索
 * @return 找到的应用程序URL数组
 */
- (NSArray<NSURL *> *)searchApplicationWithBundleIdentifier:(NSString *)bundleIdentifier
                                               searchPath:(nullable NSString *)searchPath;

/**
 * 检查应用程序是否正在运行
 * @param bundleIdentifier 应用程序的Bundle Identifier
 * @return 正在运行App路径
 */
+ (nullable NSString *)isApplicationRunning:(NSString *)bundleIdentifier;

/**
 * 终止所有匹配的应用程序
 * @param bundleIdentifier 应用程序的Bundle Identifier
 * @return 成功终止的应用程序数量
 */
- (NSInteger)terminateApplicationWithBundleIdentifier:(NSString *)bundleIdentifier;

/**
 * 终止所有通过launchApplicationWithURL:completion:terminationHandler:接口启动的应用程序
 * @return 成功终止的应用程序数量
 */
- (NSInteger)terminateRunningApplication;

/**
 * 删除所有应用被终止的通知回调
 */
- (void)removeAllTerminationHandlers;

/**
 * 添加正在运行的应用程序
 * @param bundleIdentifier 应用程序的Bundle Identifier
 * @param terminationHandler 应用程序终止时的回调
 */
- (void)addRunningApplication:(NSString *)bundleIdentifier terminationHandler:(WMMacLauncherProcessTerminationHandler)terminationHandler;
@end

NS_ASSUME_NONNULL_END 
