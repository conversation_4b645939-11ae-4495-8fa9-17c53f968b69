# encoding:UTF-8
Pod::Spec.new do |s|
  s.name = 'WMLauncherCore'
  s.version = '1.0.0'
  s.summary = 'Mac启动器核心库'
  s.description = <<-DESC
  Mac启动器核心库
  DESC
  s.homepage = 'http://gitlab.sys.wanmei.com/iOS/WMLauncherCore'
  s.license = { type: 'MIT', filePath: 'LICENSE' }
  s.author = { 'Mario' => '<EMAIL>' }
  s.source = { git: 'http://gitlab.sys.wanmei.com/iOS/macos-launcher.git', tag: s.version.to_s }

  s.platform = :osx, '12.0'

  s.frameworks = 'AppKit'

  s.source_files = 'WMLauncherCore/Classes/**/*',
                   'WMLauncherCore/ThirdParty/Patcher/**/*.h'

  s.public_header_files = 'WMLauncherCore/Classes/**/*.h'

#  s.osx.vendored_libraries = 'WMMacLauncherCore/ThirdParty/Patcher/libPatcherSDK.dylib'
  s.dependency "WMLauncherDependency"
  s.dependency "WPAnalysisSDK","2.34.3"
  s.dependency "WMCategories", ">= 0.3.4"
  s.dependency "WMWebOud"
  s.dependency "WMZipUtilities"
  s.dependency "CrashSDK_macOS","*******"
  
  s.pod_target_xcconfig = { 'EXCLUDED_ARCHS[sdk=macosx*]' => 'x86_64' }

  s.user_target_xcconfig = { 'EXCLUDED_ARCHS[sdk=macosx*]' => 'x86_64'}
end
