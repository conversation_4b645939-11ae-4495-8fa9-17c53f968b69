# encoding:UTF-8
Pod::Spec.new do |s|
  s.name = 'WMLauncherDependency'
  s.version = '1.0.2'
  s.summary = 'Mac启动器核心库'
  s.description = <<-DESC
  Mac启动器依赖的组件库
  1.0.0版本：PatcherSDK(2.16.3), <PERSON><PERSON><PERSON>(2.6.4)
  1.0.1版本：PatcherSDK(2.18.3), Sparkle(2.6.4)
  1.0.2版本：PatcherSDK(2.19.2), <PERSON>rkle(2.6.4)
  DESC
  s.homepage = 'http://gitlab.sys.wanmei.com/iOS/WMLauncherDependency'
  s.license = { type: 'MIT', filePath: 'LICENSE' }
  s.author = { 'Mario' => '<EMAIL>' }
  s.source = { git: 'http://gitlab.sys.wanmei.com/iOS/macos-launcher.git', tag: s.version.to_s }

  s.platform = :osx, '12.0'

  # s.source_files = 'WMMacDependency/**/*'

  s.public_header_files = 'WMLauncherDependency/**/*.h',

  s.vendored_libraries = 'WMLauncherDependency/Patcher/libPatcherSDK.dylib'

  s.vendored_framework   = 'WMLauncherDependency/Sparkle/Sparkle.framework'
  s.frameworks = 'AppKit'

  s.libraries = 'z', 'bsm', 'c++'

  s.pod_target_xcconfig = { 'EXCLUDED_ARCHS[sdk=macosx*]' => 'x86_64' }

  s.user_target_xcconfig = { 'EXCLUDED_ARCHS[sdk=macosx*]' => 'x86_64'}
end
