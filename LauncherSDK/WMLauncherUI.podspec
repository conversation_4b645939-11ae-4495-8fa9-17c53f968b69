# encoding:UTF-8
Pod::Spec.new do |s|
  s.name = 'WMLauncherUI'
  s.version = '1.4.0'
  s.summary = 'Mac启动器UI模块'
  s.description = <<-DESC
  Mac启动器UI模块
  DESC
  s.homepage = 'http://gitlab.sys.wanmei.com/iOS/macos-launcher.git'
  s.license = { type: 'MIT', filePath: 'LICENSE' }
  s.author = { 'Mario' => '<EMAIL>' }
  s.source = { git: 'http://gitlab.sys.wanmei.com/iOS/macos-launcher.git', tag: s.version.to_s }

  s.platform = :osx, '12.0'

  s.frameworks = 'AppKit'

  if ENV["AREA_ENV"] == "LAUNCHER_AREA_OVERSEA"
    puts "****** 请注意当前 地区 = oversea"
    s.resources = "WMLauncherUI/Config/Oversea/WPAnaConfig.plist"
    
    s.resource_bundles = {
      'LauncherRes' => ['WMLauncherUI/Resources/Common/*','WMLauncherUI/Resources/Oversea/*'],
    }
  else
    puts "****** 请注意当前 地区 = mainland"
    s.resources = "WMLauncherUI/Config/Mainland/WPAnaConfig.plist"
    s.resource_bundles = {
      'LauncherRes' => ['WMLauncherUI/Resources/Common/*','WMLauncherUI/Resources/Mainland/*'],
    }
end

  s.source_files = 'WMLauncherUI/Classes/**/*.swift'
  
  common_envs = "#{ENV["LAUNCHER_MACRO_ENV"]} #{ENV["AREA_ENV"]}"
  
if ENV["SCENCE_ENV"] == "dev"
    dev_envs = "LAUNCHER_ENV_DEVELOP #{common_envs}"
    s.xcconfig = { "SWIFT_ACTIVE_COMPILATION_CONDITIONS[config=Debug]" => "DEBUG #{dev_envs}",
      "SWIFT_ACTIVE_COMPILATION_CONDITIONS[config=Release]" => "#{dev_envs}"}

    puts "****** 请注意当前 Env = #{ENV["SCENCE_ENV"]}， MACRO = #{ENV["LAUNCHER_MACRO_ENV"]}"

elsif ENV["SCENCE_ENV"] == "test"
    test_envs = "LAUNCHER_ENV_TEST #{common_envs}"
    s.xcconfig = { "SWIFT_ACTIVE_COMPILATION_CONDITIONS[config=Debug]" => "DEBUG #{test_envs}",
      "SWIFT_ACTIVE_COMPILATION_CONDITIONS[config=Release]" => "#{test_envs}"}

    puts "****** 请注意当前 Env = #{ENV["SCENCE_ENV"]}， MACRO = #{ENV["LAUNCHER_MACRO_ENV"]}"

else
  s.xcconfig = { "SWIFT_ACTIVE_COMPILATION_CONDITIONS[config=Debug]" => "DEBUG #{common_envs}",
    "SWIFT_ACTIVE_COMPILATION_CONDITIONS[config=Release]" => "#{common_envs}"}
  
    puts "****** 请注意当前 Env = OB， MACRO = #{ENV["LAUNCHER_MACRO_ENV"]}"
end

#  s.public_header_files = 'WMLauncherUI/Classes/**/*.swift'
  s.dependency "WMXAppKit",">= *******"
  s.dependency "WMWebViewJavascriptBridge"
  s.dependency "WMMasonry", ">= 1.1.2"
  s.dependency "WMLauncherCore"
  s.dependency "WMMBProgressHUD","~> *******"
  s.dependency "WMDevice",">= 0.2.14" #获取磁盘读写速度
  s.dependency "WMXCGLogger",">= 7.1.5" 
end
