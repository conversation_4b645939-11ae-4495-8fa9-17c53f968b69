source 'http://gitlab.sys.wanmei.com/iOS/sdk-dev-cocoapods-specs.git'
source 'https://cdn.cocoapods.org/'

use_frameworks! :linkage => :static
platform :macos, '12.0'

# archiveApp.sh打包时会获取环境(dev、test)和包名字段
ENV['SCENCE_ENV'] = 'ob'
ENV['PACKAGE_NAME'] = 'Launcher'

target 'Launcher' do
  pod 'WMLauncherUI', path: '../LauncherSDK/'
  pod 'WMLauncherCore', path: '../LauncherSDK/'
  pod 'WMLauncherDependency', path: '../LauncherSDK/'
  pod 'LauncherResource', path: './'

  # Apply settings for Apple Silicon only
  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['ARCHS'] = 'arm64'
        config.build_settings['EXCLUDED_ARCHS[sdk=macosx*]'] = 'x86_64'
      end
    end
  end
end
