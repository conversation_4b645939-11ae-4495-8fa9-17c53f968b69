PODS:
  - CrashSDK_macOS (*******):
    - WMDevice (>= 0.2.13)
  - LauncherResource (1.0.0)
  - WMAFNetworking (3.2.5)
  - WMAWSSDK (1.0.4):
    - WMOSCore (>= 0.3.3)
  - WMCategories (0.3.4)
  - WMCOSSDK (0.4.7):
    - WMOSCore (>= 0.3.3)
  - WMDevice (0.2.14):
    - WMMacRoute (~> 0.2.2)
  - WMLauncherCore (1.0.0):
    - CrashSDK_macOS (= *******)
    - WMCategories (>= 0.3.4)
    - WMLauncherDependency
    - WMWebOud
    - WMZipUtilities
    - WPAnalysisSDK (= 2.34.3)
  - WMLauncherDependency (1.0.2)
  - WMLauncherUI (1.4.0):
    - WMDevice (>= 0.2.14)
    - WMLauncherCore
    - WMMasonry (>= 1.1.2)
    - WMMBProgressHUD (~> *******)
    - WMWebViewJavascriptBridge
    - WMXAppKit (>= *******)
    - WMXCGLogger (>= 7.1.5)
  - WMMacRoute (0.2.2)
  - WMMasonry (1.1.2)
  - WMMBProgressHUD (*******)
  - WMNetworkDiagnose (0.1.9):
    - WMMacRoute (>= 0.2.0)
  - WMOSCheck (0.2.4):
    - WMAFNetworking (~> 3.2.5)
    - WMCategories (>= 0.3.4)
  - WMOSCore (0.3.3)
  - WMWebOud (2.0.8):
    - WMAFNetworking (~> 3.2.5)
    - WMAWSSDK (>= 1.0.4)
    - WMCategories (>= 0.3.4)
    - WMCOSSDK (>= 0.4.7)
    - WMOSCheck (~> 0.2.4)
  - WMWebViewJavascriptBridge (7.1.1)
  - WMXAppKit (*******)
  - WMXCGLogger (7.1.5)
  - WMZipUtilities (0.1.1)
  - WPAnalysisSDK (2.34.3):
    - WMCollectInfoSDK (>= 1.2.2)
    - WMDevice (>= 0.2.13)
    - WMNetworkDiagnose (>= 0.1.8)

DEPENDENCIES:
  - LauncherResource (from `./`)
  - WMLauncherCore (from `../LauncherSDK/`)
  - WMLauncherDependency (from `../LauncherSDK/`)
  - WMLauncherUI (from `../LauncherSDK/`)

SPEC REPOS:
  http://gitlab.sys.wanmei.com/iOS/sdk-dev-cocoapods-specs.git:
    - CrashSDK_macOS
    - WMAFNetworking
    - WMAWSSDK
    - WMCategories
    - WMCOSSDK
    - WMDevice
    - WMMacRoute
    - WMMasonry
    - WMMBProgressHUD
    - WMNetworkDiagnose
    - WMOSCheck
    - WMOSCore
    - WMWebOud
    - WMWebViewJavascriptBridge
    - WMXAppKit
    - WMXCGLogger
    - WMZipUtilities
    - WPAnalysisSDK

EXTERNAL SOURCES:
  LauncherResource:
    :path: "./"
  WMLauncherCore:
    :path: "../LauncherSDK/"
  WMLauncherDependency:
    :path: "../LauncherSDK/"
  WMLauncherUI:
    :path: "../LauncherSDK/"

SPEC CHECKSUMS:
  CrashSDK_macOS: bfbd54889dcd8d343905ff7120cb846a3a85eeec
  LauncherResource: 0f52a0c0f4659c87799141450ab37473fac1fbd3
  WMAFNetworking: a2b76259284339c2cc794c9285f520da856fde19
  WMAWSSDK: d79f8297f44c145c10e8743493ccae81407a9d04
  WMCategories: d4c8d7fe62e955033aa7277ff0372a2bb68ad940
  WMCOSSDK: 2369542764242b31cd7b134e4bd4a766aba01b79
  WMDevice: 36d26cc64b6ea0cdaeca006dcbc15f72a2a88c31
  WMLauncherCore: 983afb775f582c83b738d28d559ecb18b99f23b5
  WMLauncherDependency: c7b314580d889e84ffbf26fff28d64658e3b2d9f
  WMLauncherUI: d80c24edd79df2bcd9cd49256810573a1401f247
  WMMacRoute: 9dc51e0b517cda7b1664c8713d35ff8100f6dcc0
  WMMasonry: 7054a9da2b14e7ed8a19991cf0cc6a5973aaa499
  WMMBProgressHUD: 4509352879880617e65765cf9d7364d656e2b481
  WMNetworkDiagnose: 06849a562087c4c0ed6ce9e1ff217de5d43c38e1
  WMOSCheck: a6c50bee73b78122dde273960ebf1ca16eb60446
  WMOSCore: b01455121bcd11f75ad3d0d1acc3df1575c3c569
  WMWebOud: d59144e660ad4d7d3fd1fe87064a6eb99ce50535
  WMWebViewJavascriptBridge: 19aa6c8031dd0d222b15e4902cb20198f19704c3
  WMXAppKit: 58f43c885a70a04f8998d231ac1564e04feb8a5a
  WMXCGLogger: 89329bf92b1eff07fc8e9f4a206b926ba755c495
  WMZipUtilities: ef8222d9b25b0f42cdf4d2a3ed350375a19979b1
  WPAnalysisSDK: 3db7995bbf4b722ab707836b5a7c06cf62a200c7

PODFILE CHECKSUM: a15820cf79c126a0853c4a5608c5dd275fb86ce9

COCOAPODS: 1.15.2
