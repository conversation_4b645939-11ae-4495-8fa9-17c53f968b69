//
//  main.swift
//  Launcher
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/11/4.
//

import Cocoa
import WMLauncherUI

// 从 Info.plist 读取 APP_KEY，如果读取失败则使用默认值
func getAppKey() -> String {
    guard let infoPlist = Bundle.main.infoDictionary,
          let appKey = infoPlist["APP_KEY"] as? String,
          !appKey.isEmpty else {
        return "ggeaj0zcfv5fno50cnbap6wmwoi0flfs" // 非模版工程会被替换为空字符串
    }
    return appKey
}

let appkey = getAppKey()
let result = LaunchUIManager.shared.setup(configName: "launcherConfig", appKey: appkey)

if result  {
    let appDelegate = LaunchUIManager.shared.getLauncherAppDelegate()

    NSApplication.shared.delegate = appDelegate

    _ = NSApplicationMain(CommandLine.argc, CommandLine.unsafeArgv)
}


