// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		0F7052762CD8BBB9008B05A4 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0F7052752CD8BBB9008B05A4 /* Assets.xcassets */; };
		0F70529F2CD8C146008B05A4 /* main.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0F70529E2CD8C146008B05A4 /* main.swift */; };
		DEE4FB3F4F4E714308767CAA /* Pods_Launcher.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = D7AD0C0DA96AAF502D4F40DD /* Pods_Launcher.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0F70526E2CD8BBB6008B05A4 /* 诛仙世界启动器.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "诛仙世界启动器.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		0F7052752CD8BBB9008B05A4 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		0F70527A2CD8BBB9008B05A4 /* Launcher.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Launcher.entitlements; sourceTree = "<group>"; };
		0F70529E2CD8C146008B05A4 /* main.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = main.swift; sourceTree = "<group>"; };
		0FE648CB2CEC753700CD2FD8 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist; path = Info.plist; sourceTree = "<group>"; };
		9519DCA2B6500452E69D9DE4 /* Pods-Launcher.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Launcher.debug.xcconfig"; path = "Target Support Files/Pods-Launcher/Pods-Launcher.debug.xcconfig"; sourceTree = "<group>"; };
		BAE84BF4FAC335F4E5E18250 /* Pods-Launcher.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Launcher.release.xcconfig"; path = "Target Support Files/Pods-Launcher/Pods-Launcher.release.xcconfig"; sourceTree = "<group>"; };
		D7AD0C0DA96AAF502D4F40DD /* Pods_Launcher.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Launcher.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		0F70526B2CD8BBB6008B05A4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DEE4FB3F4F4E714308767CAA /* Pods_Launcher.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0F7052652CD8BBB6008B05A4 = {
			isa = PBXGroup;
			children = (
				0F7052702CD8BBB6008B05A4 /* Launcher */,
				0F70526F2CD8BBB6008B05A4 /* Products */,
				55F8A5E9363D229BF6C6172F /* Pods */,
				413C0FBE688147ADEC745F4E /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		0F70526F2CD8BBB6008B05A4 /* Products */ = {
			isa = PBXGroup;
			children = (
				0F70526E2CD8BBB6008B05A4 /* 诛仙世界启动器.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0F7052702CD8BBB6008B05A4 /* Launcher */ = {
			isa = PBXGroup;
			children = (
				0FE648CB2CEC753700CD2FD8 /* Info.plist */,
				0F70529E2CD8C146008B05A4 /* main.swift */,
				0F7052752CD8BBB9008B05A4 /* Assets.xcassets */,
				0F70527A2CD8BBB9008B05A4 /* Launcher.entitlements */,
			);
			path = Launcher;
			sourceTree = "<group>";
		};
		413C0FBE688147ADEC745F4E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				D7AD0C0DA96AAF502D4F40DD /* Pods_Launcher.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		55F8A5E9363D229BF6C6172F /* Pods */ = {
			isa = PBXGroup;
			children = (
				9519DCA2B6500452E69D9DE4 /* Pods-Launcher.debug.xcconfig */,
				BAE84BF4FAC335F4E5E18250 /* Pods-Launcher.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0F70526D2CD8BBB6008B05A4 /* Launcher */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0F7052932CD8BBB9008B05A4 /* Build configuration list for PBXNativeTarget "Launcher" */;
			buildPhases = (
				A2495E8F1286DFCC19974570 /* [CP] Check Pods Manifest.lock */,
				0F70526A2CD8BBB6008B05A4 /* Sources */,
				0F70526B2CD8BBB6008B05A4 /* Frameworks */,
				0F70526C2CD8BBB6008B05A4 /* Resources */,
				80F350B1D4C968E1D4F5FC38 /* [CP] Embed Pods Frameworks */,
				135A47FBA51683D171388A51 /* [CP] Copy Pods Resources */,
				0F6A97812D0BDFEB0070C0C1 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Launcher;
			productName = Launcher;
			productReference = 0F70526E2CD8BBB6008B05A4 /* 诛仙世界启动器.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0F7052662CD8BBB6008B05A4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					0F70526D2CD8BBB6008B05A4 = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = 0F7052692CD8BBB6008B05A4 /* Build configuration list for PBXProject "Launcher" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = "zh-Hans";
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hans",
				"zh-Hant",
			);
			mainGroup = 0F7052652CD8BBB6008B05A4;
			productRefGroup = 0F70526F2CD8BBB6008B05A4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0F70526D2CD8BBB6008B05A4 /* Launcher */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0F70526C2CD8BBB6008B05A4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0F7052762CD8BBB9008B05A4 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0F6A97812D0BDFEB0070C0C1 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\n# 定义源路径和目标路径\nSRC=\"${BUILT_PRODUCTS_DIR}/${CONTENTS_FOLDER_PATH}/Resources/crashpad_handler\"\nDEST=\"${BUILT_PRODUCTS_DIR}/${CONTENTS_FOLDER_PATH}/MacOS/crashpad_handler\"\n\n# 创建目标目录（如果不存在）\nmkdir -p \"$(dirname \"$DEST\")\"\n\n# 复制文件\nif [ -e \"$SRC\" ]; then\n    mv \"$SRC\" \"$DEST\"\n    chmod +x \"$DEST\" # 确保文件具有可执行权限\n    echo \"Copied crashpad_handler to Contents/MacOS\"\nelse\n    echo \"crashpad_handler not found at $SRC\"\nfi\n";
		};
		135A47FBA51683D171388A51 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Launcher/Pods-Launcher-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Launcher/Pods-Launcher-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Launcher/Pods-Launcher-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		80F350B1D4C968E1D4F5FC38 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Launcher/Pods-Launcher-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Launcher/Pods-Launcher-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Launcher/Pods-Launcher-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		A2495E8F1286DFCC19974570 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Launcher-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0F70526A2CD8BBB6008B05A4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				0F70529F2CD8C146008B05A4 /* main.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		0F7052912CD8BBB9008B05A4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		0F7052922CD8BBB9008B05A4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		0F7052942CD8BBB9008B05A4 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9519DCA2B6500452E69D9DE4 /* Pods-Launcher.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Launcher/Launcher.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Developer ID Application";
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=macosx*]" = R7A9EWN2QE;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"WMAWS_SDK_VERSION=@\\\"1.0.4\\\"",
					"WMCOS_SDK_VERSION=@\\\"0.4.7\\\"",
					"WMOSCheck_SDK_VERSION=@\\\"0.2.4\\\"",
					"WMWebOud_SDK_VERSION=@\\\"2.0.8\\\"",
					"WMZIP_SDK_VERSION=@\\\"0.1.1\\\"",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Launcher/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wanm.zxclient;
				PRODUCT_NAME = "诛仙世界启动器";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = com.wanm.zxclient;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		0F7052952CD8BBB9008B05A4 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = BAE84BF4FAC335F4E5E18250 /* Pods-Launcher.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = Launcher/Launcher.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Developer ID Application";
				CODE_SIGN_STYLE = Manual;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=macosx*]" = R7A9EWN2QE;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"$(inherited)",
					"COCOAPODS=1",
					"WMAWS_SDK_VERSION=@\\\"1.0.4\\\"",
					"WMCOS_SDK_VERSION=@\\\"0.4.7\\\"",
					"WMOSCheck_SDK_VERSION=@\\\"0.2.4\\\"",
					"WMWebOud_SDK_VERSION=@\\\"2.0.8\\\"",
					"WMZIP_SDK_VERSION=@\\\"0.1.1\\\"",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Launcher/Info.plist;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INFOPLIST_KEY_NSMainStoryboardFile = "";
				INFOPLIST_KEY_NSPrincipalClass = NSApplication;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				MARKETING_VERSION = 1.0.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.wanm.zxclient;
				PRODUCT_NAME = "诛仙世界启动器";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = com.wanm.zxclient;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0F7052692CD8BBB6008B05A4 /* Build configuration list for PBXProject "Launcher" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0F7052912CD8BBB9008B05A4 /* Debug */,
				0F7052922CD8BBB9008B05A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0F7052932CD8BBB9008B05A4 /* Build configuration list for PBXNativeTarget "Launcher" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0F7052942CD8BBB9008B05A4 /* Debug */,
				0F7052952CD8BBB9008B05A4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0F7052662CD8BBB6008B05A4 /* Project object */;
}
