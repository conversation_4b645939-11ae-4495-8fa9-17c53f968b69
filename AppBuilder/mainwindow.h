#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QSplitter>
#include <QMenuBar>
#include <QStatusBar>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMessageBox>
#include <QCloseEvent>
#include <QToolBar>
#include <QPushButton>

class RegionConfigManager;
class RegionListWidget;
class ConfigEditorWidget;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void onSaveConfig();
    void onRefreshProjects();
    void onValidateConfig();
    void onAbout();
    void onSelectProjectRoot();
    void onProjectRootChanged();
    void onProjectRootValidationChanged();
    void onConfigChanged();
    void onConfigSaved(const QString &regionName);
    void onErrorOccurred(const QString &message);

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupConnections();
    void updateWindowTitle();
    void updateStatusBar();
    void updateProjectPathDisplay();

private:
    RegionConfigManager *m_configManager;
    RegionListWidget *m_regionListWidget;
    ConfigEditorWidget *m_configEditorWidget;
    QSplitter *m_splitter;
    
    // 状态栏组件
    QLabel *m_currentRegionLabel;
    QLabel *m_saveStatusLabel;
    QLabel *m_projectPathLabel;

    // 工具栏组件
    QPushButton *m_selectProjectButton;
    
    // 菜单动作
    QAction *m_saveAction;
    QAction *m_refreshAction;
    QAction *m_validateAction;
    QAction *m_exitAction;
    QAction *m_aboutAction;
};

#endif // MAINWINDOW_H
