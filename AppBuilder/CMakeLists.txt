cmake_minimum_required(VERSION 3.16)

project(RegionConfigEditor VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置Qt6路径 - 可通过命令行参数传入
set(CMAKE_PREFIX_PATH "${QT6_PATH}")

# 查找Qt6组件 - 添加Quick支持用于混合UI
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Quick Qml QuickWidgets)

# 启用Qt6的自动处理
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 源文件列表
set(SOURCES
    main_widgets.cpp
    regionconfigmanager.cpp
    mainwindow.cpp
    regionlistwidget.cpp
    configeditorwidget.cpp
    aescrypto.cpp
)

# 头文件列表
set(HEADERS
    regionconfigmanager.h
    mainwindow.h
    regionlistwidget.h
    configeditorwidget.h
    aescrypto.h
)

# 创建可执行文件
add_executable(RegionConfigEditor ${SOURCES} ${HEADERS})

# 创建Qt资源文件（qrc），PREFIX决定QML访问路径
qt_add_resources(RegionConfigEditor "qml_resources"
    PREFIX "/qml"
    BASE "${CMAKE_CURRENT_SOURCE_DIR}/qml"
    FILES
        "${CMAKE_CURRENT_SOURCE_DIR}/qml/RegionListView.qml"
        "${CMAKE_CURRENT_SOURCE_DIR}/qml/RegionListItem.qml"
)

# 查找OpenSSL库
find_package(OpenSSL REQUIRED)

# 链接Qt6库 - 添加Quick支持
target_link_libraries(RegionConfigEditor PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::Quick
    Qt6::Qml
    Qt6::QuickWidgets
    OpenSSL::SSL
    OpenSSL::Crypto
)

# macOS特定配置
if(APPLE)
    set_target_properties(RegionConfigEditor PROPERTIES
        MACOSX_DEPLOYMENT_TARGET "12.0"
        MACOSX_BUNDLE TRUE
        MACOSX_BUNDLE_GUI_IDENTIFIER com.wanmei.regionconfigeditor
        MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
        MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
        MACOSX_BUNDLE_BUNDLE_NAME "Region Config Editor"
        MACOSX_BUNDLE_DISPLAY_NAME "地区配置编辑器"
        # 禁用代码签名以避免OpenSSL库的签名问题
        XCODE_ATTRIBUTE_CODE_SIGN_IDENTITY ""
        XCODE_ATTRIBUTE_CODE_SIGNING_REQUIRED "NO"
        XCODE_ATTRIBUTE_CODE_SIGNING_ALLOWED "NO"
    )

    # 查找macdeployqt工具
    find_program(MACDEPLOYQT_EXECUTABLE macdeployqt HINTS ${QT6_PATH}/bin)

    if(MACDEPLOYQT_EXECUTABLE)
        # 添加部署目标
        add_custom_target(deploy
            COMMAND ${MACDEPLOYQT_EXECUTABLE} RegionConfigEditor.app -qmldir=${CMAKE_SOURCE_DIR}/qml -verbose=2
            DEPENDS RegionConfigEditor
            COMMENT "Deploying Qt libraries to app bundle"
            WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
        )

        message(STATUS "macdeployqt found at: ${MACDEPLOYQT_EXECUTABLE}")
        message(STATUS "Use 'make deploy' to bundle Qt libraries")
    else()
        message(WARNING "macdeployqt not found at ${QT6_PATH}/bin. App bundle will not include Qt libraries.")
    endif()
endif()
