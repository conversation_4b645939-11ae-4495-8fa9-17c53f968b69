#ifndef CONFIGEDITORWIDGET_H
#define CONFIGEDITORWIDGET_H

#include <QWidget>

class RegionConfigManager;
class QVBoxLayout;
class QHBoxLayout;
class QTabWidget;
class QScrollArea;
class QGroupBox;
class QFormLayout;
class QGridLayout;
class QLabel;
class QLineEdit;
class QComboBox;
class QPushButton;
class QTextEdit;

class ConfigEditorWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ConfigEditorWidget(RegionConfigManager *configManager, QWidget *parent = nullptr);

private slots:
    void onCurrentRegionChanged();
    void onConfigChanged();
    void onSaveConfig();
    void onFieldChanged();
    void onSelectIconFile();
    void onSelectDmgBackground();
    void onSelectDmgIcon();
    void updateConfigDisplay();

private:
    void setupUI();
    void setupConnections();
    void setupBasicInfoTab();
    void setupEnvironmentTab();
    void setupResourcesTab();
    void setupGameParametersTab();  // 游戏参数信息标签页
    void setupSDKParametersTab();   // SDK参数信息标签页
    void setupAnalysisConfigGroup();
    void setupCrashConfigGroup();
    void setupOudConfigGroup();
    void updateSDKConfigGroup(QGroupBox *group, QFormLayout *layout,
                             QMap<QString, QLineEdit*> &edits, const QVariantMap &config);
    QVariantMap collectSDKConfigFromEdits(const QMap<QString, QLineEdit*> &edits);
    void createEmptyState();
    void updateFromConfig();
    void saveToConfig();
    QGroupBox* createGroupBox(const QString &title);
    void updateImagePreview(QLabel *preview, QLabel *pathLabel, const QString &imagePath);

    // 新增：高级配置相关方法
    void updatePackagingConfigDisplay();
    void updateNotarizationConfigDisplay();
    void updateGameParametersDisplay();
    void updateSDKParametersDisplay();
    void onPackagingConfigChanged();
    void onNotarizationConfigChanged();
    void onGameParametersChanged();
    void onSDKParametersChanged();

private:
    RegionConfigManager *m_configManager;
    
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_headerLayout;
    QLabel *m_titleLabel;
    QPushButton *m_saveButton;
    
    QTabWidget *m_tabWidget;
    QWidget *m_emptyStateWidget;
    
    // 基本信息标签页
    QWidget *m_basicInfoTab;
    QLineEdit *m_bundleIdEdit;
    QLineEdit *m_appNameEdit;
    QLineEdit *m_versionEdit;
    QLineEdit *m_buildNumberEdit;
    QComboBox *m_developmentRegionCombo;
    QLineEdit *m_feedUrlEdit;
    QLineEdit *m_appKeyEdit;
    
    // 环境配置标签页
    QWidget *m_environmentTab;
    QLineEdit *m_sceneEnvEdit;
    QLineEdit *m_packageNameEdit;
    QLineEdit *m_launcherMacroEdit;
    QLineEdit *m_areaEnvEdit;
    
    // 资源文件标签页
    QWidget *m_resourcesTab;
    QLabel *m_iconPreview;
    QLabel *m_iconPathLabel;
    QPushButton *m_selectIconButton;
    QLabel *m_dmgBackgroundPreview;
    QLabel *m_dmgBackgroundPathLabel;
    QPushButton *m_selectDmgBackgroundButton;
    QLabel *m_dmgIconPreview;
    QLabel *m_dmgIconPathLabel;
    QPushButton *m_selectDmgIconButton;
    
    // 新增：打包配置组件
    QLineEdit *m_bundleIdPackagingEdit;
    QLineEdit *m_provisioningProfileEdit;
    QLineEdit *m_teamIdEdit;

    // 新增：公证配置组件
    QLineEdit *m_codeSignIdentityEdit;
    QLineEdit *m_keychainProfileEdit;

    // 新增：游戏参数信息标签页
    QWidget *m_gameParametersTab;
    QTextEdit *m_gameParametersEdit;

    // SDK参数信息标签页
    QWidget *m_sdkParametersTab;
    QScrollArea *m_sdkScrollArea;
    QWidget *m_sdkContentWidget;
    QVBoxLayout *m_sdkMainLayout;

    // SDK配置组
    QGroupBox *m_analysisConfigGroup;
    QGroupBox *m_crashConfigGroup;
    QGroupBox *m_oudConfigGroup;

    // SDK配置表单布局
    QFormLayout *m_analysisConfigLayout;
    QFormLayout *m_crashConfigLayout;
    QFormLayout *m_oudConfigLayout;

    // SDK配置输入控件映射
    QMap<QString, QLineEdit*> m_analysisConfigEdits;
    QMap<QString, QLineEdit*> m_crashConfigEdits;
    QMap<QString, QLineEdit*> m_oudConfigEdits;

    bool m_updating;
};

#endif // CONFIGEDITORWIDGET_H
