# 地区配置编辑器 (Region Config Editor)

一个用于管理macOS启动器地区配置的可视化Qt6 Widgets桌面应用程序。

## 📋 项目状态

- **版本**: 1.0.0
- **技术栈**: Qt6 Widgets + C++17
- **平台**: macOS 12.0+
- **架构**: ARM64/x86_64
- **构建系统**: CMake 3.16+

## 功能特性

- 🔍 **自动扫描**: 自动扫描Channels目录下的所有地区项目
- 📝 **可视化编辑**: 直观的界面编辑RegionConfig.plist配置文件
- 🖼️ **资源管理**: 管理应用图标、DMG背景图等资源文件
- ✅ **配置验证**: 实时验证配置文件格式和必需字段
- 💾 **实时保存**: 支持实时保存和撤销更改
- 🎨 **现代界面**: 基于Qt6 QML的现代化用户界面

## 系统要求

- macOS 12.0 或更高版本
- Qt6.8.3 或更高版本
- CMake 3.16 或更高版本
- Xcode Command Line Tools

## 安装和构建

### 1. 环境准备

确保您的系统已安装Qt6。本项目配置为使用以下Qt6路径：
```
/Users/<USER>/Desktop/qt-6.8.3/macos
```

如果您的Qt6安装在不同位置，请修改 `CMakeLists.txt` 中的 `CMAKE_PREFIX_PATH`。

### 2. 构建项目

```bash
# 进入Builder目录
cd Builder

# 创建构建目录
mkdir build
cd build

# 配置项目
cmake ..

# 编译
make

# 或者使用Ninja（如果已安装）
# cmake -G Ninja ..
# ninja
```

### 3. 运行应用程序

```bash
# 在build目录中运行
./RegionConfigEditor.app/Contents/MacOS/RegionConfigEditor

# 或者直接打开应用程序包
open RegionConfigEditor.app
```

## 使用说明

### 1. 启动应用程序

启动后，应用程序会自动扫描项目根目录下的 `Channels/` 目录，查找包含 `RegionConfig.plist` 的地区项目。

### 2. 选择地区项目

在左侧的地区项目列表中点击选择要编辑的地区项目。选中后，右侧会显示该地区的配置编辑界面。

### 3. 编辑配置

配置编辑界面分为四个标签页：

#### 基本信息
- Bundle ID
- 产品名称和显示名称
- 版本号和构建号
- 开发地区设置
- 更新源URL和应用密钥

#### 环境配置
- Podfile环境配置（场景环境、包名称等）
- 项目配置（开发地区等）

#### 资源文件
- 应用图标管理（1024x1024 PNG）
- DMG背景图和图标管理
- 支持拖拽上传和文件选择

#### 高级配置
- 导出选项配置（开发中）
- 公证配置（开发中）
- 启动器配置（开发中）

### 4. 保存配置

- 修改配置后，应用程序会自动标记为有未保存更改
- 点击"保存配置"按钮或使用快捷键 `Cmd+S` 保存更改
- 关闭应用程序时会提示保存未保存的更改

## 项目结构

```
Builder/
├── CMakeLists.txt              # CMake配置文件
├── main.cpp                    # 应用程序入口
├── regionconfigmanager.h       # 配置管理器头文件
├── regionconfigmanager.cpp     # 配置管理器实现
├── qml/                        # QML界面文件
│   ├── main.qml               # 主界面
│   ├── RegionListView.qml     # 地区列表视图
│   ├── ConfigEditor.qml       # 配置编辑器
│   ├── FileSelector.qml       # 文件选择器
│   └── components/            # QML组件
│       ├── ConfigSection.qml  # 配置节组件
│       └── ImagePreview.qml   # 图像预览组件
└── README.md                  # 本文件
```

## 支持的配置文件

- `RegionConfig.plist` - 主配置文件
- `Release/ExportOptions.plist` - 导出选项（计划支持）
- `Release/notarization.plist` - 公证配置（计划支持）
- `Release/Resources/launcherConfig.json` - 启动器配置（计划支持）

## 支持的资源文件

- `icon_1024x1024.png` - 应用图标
- `Release/dmg-background.png` - DMG背景图
- `Release/dmg-icon.icns` - DMG图标

## 快捷键

- `Cmd+S` - 保存配置
- `F5` - 刷新项目列表
- `Cmd+Q` - 退出应用程序

## 故障排除

### 应用程序无法启动
1. 检查Qt6是否正确安装
2. 确认CMakeLists.txt中的Qt6路径是否正确
3. 检查是否有必要的权限访问项目目录

### 找不到地区项目
1. 确认应用程序在正确的项目根目录中运行
2. 检查Channels目录是否存在
3. 确认地区项目目录中包含RegionConfig.plist文件

### 配置保存失败
1. 检查文件权限
2. 确认配置文件格式正确
3. 查看应用程序日志获取详细错误信息

## 开发说明

### 添加新的配置项

1. 在 `regionconfigmanager.h` 中添加相应的属性和方法
2. 在 `regionconfigmanager.cpp` 中实现配置读取和保存逻辑
3. 在相应的QML文件中添加界面元素

### 自定义Qt6路径

修改 `CMakeLists.txt` 中的以下行：
```cmake
set(CMAKE_PREFIX_PATH "/your/qt6/path")
```

## 许可证

© 2024 Wanmei Technology. 保留所有权利。

## 联系方式

如有问题或建议，请联系开发团队。
