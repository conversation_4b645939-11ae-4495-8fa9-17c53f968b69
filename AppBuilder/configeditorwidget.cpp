#include "configeditorwidget.h"
#include "regionconfigmanager.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTabWidget>
#include <QScrollArea>
#include <QGroupBox>
#include <QFormLayout>
#include <QGridLayout>
#include <QLabel>
#include <QLineEdit>
#include <QComboBox>
#include <QPushButton>
#include <QTextEdit>
#include <QFileDialog>
#include <QPixmap>
#include <QFont>
#include <QFileInfo>
#include <QMessageBox>

ConfigEditorWidget::ConfigEditorWidget(RegionConfigManager *configManager, QWidget *parent)
    : QWidget(parent)
    , m_configManager(configManager)
    , m_areaTypeCombo(nullptr)
    , m_updating(false)
{
    setupUI();
    setupConnections();
    onCurrentRegionChanged(); // 初始化显示状态
}

void ConfigEditorWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);
    
    // 标题栏
    m_headerLayout = new QHBoxLayout;
    
    m_titleLabel = new QLabel("请选择地区项目");
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_headerLayout->addWidget(m_titleLabel);
    
    m_headerLayout->addStretch();
    
    m_saveButton = new QPushButton("保存配置");
    m_saveButton->setEnabled(false);
    m_headerLayout->addWidget(m_saveButton);
    
    m_mainLayout->addLayout(m_headerLayout);
    
    // 创建标签页
    m_tabWidget = new QTabWidget;
    setupBasicInfoTab();
    setupEnvironmentTab();
    setupResourcesTab();
    setupGameParametersTab();  // 游戏参数信息标签页
    setupSDKParametersTab();   // SDK参数信息标签页

    m_mainLayout->addWidget(m_tabWidget);
    
    // 创建空状态
    createEmptyState();
    
    // 设置样式
    setStyleSheet(
        "QGroupBox {"
        "    font-weight: bold;"
        "    border: 1px solid #d0d0d0;"
        "    border-radius: 5px;"
        "    margin-top: 10px;"
        "    padding-top: 10px;"
        "}"
        "QGroupBox::title {"
        "    subcontrol-origin: margin;"
        "    left: 10px;"
        "    padding: 0 5px 0 5px;"
        "}"
        "QLineEdit, QComboBox {"
        "    padding: 5px;"
        "    border: 1px solid #d0d0d0;"
        "    border-radius: 3px;"
        "}"
        "QLineEdit:focus, QComboBox:focus {"
        "    border: 2px solid #4080ff;"
        "}"
        "QPushButton {"
        "    padding: 8px 16px;"
        "    border: 1px solid #d0d0d0;"
        "    border-radius: 3px;"
        "    background-color: #f0f0f0;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e0e0e0;"
        "}"
        "QPushButton:pressed {"
        "    background-color: #d0d0d0;"
        "}"
        "QPushButton:disabled {"
        "    color: gray;"
        "    background-color: #f8f8f8;"
        "}"
    );
}

void ConfigEditorWidget::setupConnections()
{
    connect(m_saveButton, &QPushButton::clicked, this, &ConfigEditorWidget::onSaveConfig);

    // 监听配置管理器信号
    connect(m_configManager, &RegionConfigManager::currentRegionChanged,
            this, &ConfigEditorWidget::onCurrentRegionChanged);
    connect(m_configManager, &RegionConfigManager::currentConfigChanged,
            this, &ConfigEditorWidget::updateConfigDisplay);
    connect(m_configManager, &RegionConfigManager::hasUnsavedChangesChanged,
            this, &ConfigEditorWidget::onConfigChanged);

    // 监听所有输入控件的变更，简化变更检测
    connect(m_bundleIdEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);
    connect(m_appNameEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);
    connect(m_appKeyEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);
    connect(m_versionEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);
    connect(m_buildNumberEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);
    connect(m_developmentRegionCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), this, &ConfigEditorWidget::onFieldChanged);
    connect(m_feedUrlEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);
    connect(m_sceneEnvEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);
    connect(m_packageNameEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);
    connect(m_launcherMacroEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);
    connect(m_areaEnvEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onFieldChanged);

    // 新增：高级配置控件的信号连接
    connect(m_bundleIdPackagingEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onPackagingConfigChanged);
    connect(m_provisioningProfileEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onPackagingConfigChanged);
    connect(m_teamIdEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onPackagingConfigChanged);
    connect(m_codeSignIdentityEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onNotarizationConfigChanged);
    connect(m_keychainProfileEdit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onNotarizationConfigChanged);
    connect(m_gameParametersEdit, &QTextEdit::textChanged, this, &ConfigEditorWidget::onGameParametersChanged);
}

void ConfigEditorWidget::setupBasicInfoTab()
{
    m_basicInfoTab = new QWidget;
    QScrollArea *scrollArea = new QScrollArea;
    scrollArea->setWidget(m_basicInfoTab);
    scrollArea->setWidgetResizable(true);
    
    QVBoxLayout *layout = new QVBoxLayout(m_basicInfoTab);
    
    // 应用信息组
    QGroupBox *appInfoGroup = createGroupBox("应用信息");
    QFormLayout *appInfoLayout = new QFormLayout(appInfoGroup);
    
    m_bundleIdEdit = new QLineEdit;
    m_bundleIdEdit->setPlaceholderText("com.example.app");
    appInfoLayout->addRow("Bundle ID:", m_bundleIdEdit);
    
    m_appNameEdit = new QLineEdit;
    m_appNameEdit->setPlaceholderText("应用程序名称（同时用作产品名称和显示名称）");
    appInfoLayout->addRow("应用名称:", m_appNameEdit);

    m_appKeyEdit = new QLineEdit;
    m_appKeyEdit->setPlaceholderText("应用程序密钥");
    appInfoLayout->addRow("App_KEY:", m_appKeyEdit);

    m_versionEdit = new QLineEdit;
    m_versionEdit->setPlaceholderText("1.0.0");
    appInfoLayout->addRow("版本号:", m_versionEdit);
    
    m_buildNumberEdit = new QLineEdit;
    m_buildNumberEdit->setPlaceholderText("1");
    appInfoLayout->addRow("构建号:", m_buildNumberEdit);
    
    m_developmentRegionCombo = new QComboBox;
    m_developmentRegionCombo->addItems({"zh-Hans", "zh-Hant", "en", "ja", "ko"});
    appInfoLayout->addRow("开发地区:", m_developmentRegionCombo);
    
    layout->addWidget(appInfoGroup);
    
    // 更新配置组
    QGroupBox *updateGroup = createGroupBox("更新配置");
    QFormLayout *updateLayout = new QFormLayout(updateGroup);
    
    m_feedUrlEdit = new QLineEdit;
    m_feedUrlEdit->setPlaceholderText("https://example.com/appcast.xml");
    updateLayout->addRow("更新源URL:", m_feedUrlEdit);

    // 应用密钥字段已在上面的App_KEY中创建，这里不再重复创建
    
    layout->addWidget(updateGroup);

    // 打包配置组
    QGroupBox *packagingGroup = createGroupBox("打包配置");
    QFormLayout *packagingLayout = new QFormLayout(packagingGroup);

    m_bundleIdPackagingEdit = new QLineEdit;
    m_bundleIdPackagingEdit->setPlaceholderText("例如: com.example.app");
    packagingLayout->addRow("BundleID:", m_bundleIdPackagingEdit);

    m_provisioningProfileEdit = new QLineEdit;
    m_provisioningProfileEdit->setPlaceholderText("例如: ExampleApp_Distribution.mobileprovision");
    packagingLayout->addRow("描述文件名:", m_provisioningProfileEdit);

    m_teamIdEdit = new QLineEdit;
    m_teamIdEdit->setPlaceholderText("例如: ABCD123456");
    packagingLayout->addRow("TeamID:", m_teamIdEdit);

    layout->addWidget(packagingGroup);

    // 公证配置组
    QGroupBox *notarizationGroup = createGroupBox("公证配置");
    QFormLayout *notarizationLayout = new QFormLayout(notarizationGroup);

    m_codeSignIdentityEdit = new QLineEdit;
    m_codeSignIdentityEdit->setPlaceholderText("例如: Developer ID Application: Company Name");
    notarizationLayout->addRow("签名证书:", m_codeSignIdentityEdit);

    m_keychainProfileEdit = new QLineEdit;
    m_keychainProfileEdit->setPlaceholderText("例如: notarization-profile");
    notarizationLayout->addRow("Keychain profile:", m_keychainProfileEdit);

    layout->addWidget(notarizationGroup);

    layout->addStretch();

    m_tabWidget->addTab(scrollArea, "基本信息");
}

void ConfigEditorWidget::setupEnvironmentTab()
{
    m_environmentTab = new QWidget;
    QScrollArea *scrollArea = new QScrollArea;
    scrollArea->setWidget(m_environmentTab);
    scrollArea->setWidgetResizable(true);
    
    QVBoxLayout *layout = new QVBoxLayout(m_environmentTab);
    
    // Podfile环境配置组
    QGroupBox *podfileGroup = createGroupBox("Podfile环境配置");
    QFormLayout *podfileLayout = new QFormLayout(podfileGroup);
    
    // 场景环境（改为可输入文本框）
    QHBoxLayout *sceneEnvLayout = new QHBoxLayout;
    m_sceneEnvEdit = new QLineEdit;
    m_sceneEnvEdit->setPlaceholderText("ob/dev/test");
    sceneEnvLayout->addWidget(m_sceneEnvEdit);
    QPushButton *sceneEnvHelpBtn = new QPushButton("?");
    sceneEnvHelpBtn->setFixedSize(24, 24);
    sceneEnvHelpBtn->setStyleSheet("QPushButton { border: 1px solid #cccccc; border-radius: 12px; background-color: #f0f0f0; font-weight: bold; } QPushButton:hover { background-color: #e0e0e0; }");
    sceneEnvHelpBtn->setToolTip("ob:正式玩家的诛仙世界客户端\ndev:内部开发环境\ntest:正式资源的测试客户端");
    connect(sceneEnvHelpBtn, &QPushButton::clicked, [this]() {
        QMessageBox::information(this, "场景环境说明",
            "ob: 正式玩家的诛仙世界客户端\n"
            "dev: 内部开发环境\n"
            "test: 正式资源的测试客户端");
    });
    sceneEnvLayout->addWidget(sceneEnvHelpBtn);
    podfileLayout->addRow("场景环境:", sceneEnvLayout);
    
    // 包名称
    QHBoxLayout *packageNameLayout = new QHBoxLayout;
    m_packageNameEdit = new QLineEdit;
    m_packageNameEdit->setPlaceholderText("ZXLauncher");
    packageNameLayout->addWidget(m_packageNameEdit);
    QPushButton *packageNameHelpBtn = new QPushButton("?");
    packageNameHelpBtn->setFixedSize(24, 24);
    packageNameHelpBtn->setStyleSheet("QPushButton { border: 1px solid #cccccc; border-radius: 12px; background-color: #f0f0f0; font-weight: bold; } QPushButton:hover { background-color: #e0e0e0; }");
    packageNameHelpBtn->setToolTip("最终归档包时生成的dmg前缀名");
    connect(packageNameHelpBtn, &QPushButton::clicked, [this]() {
        QMessageBox::information(this, "包名称说明", "最终归档包时生成的dmg前缀名");
    });
    packageNameLayout->addWidget(packageNameHelpBtn);
    podfileLayout->addRow("包名称:", packageNameLayout);

    // 启动器宏环境
    QHBoxLayout *launcherMacroLayout = new QHBoxLayout;
    m_launcherMacroEdit = new QLineEdit;
    m_launcherMacroEdit->setPlaceholderText("可选配置");
    launcherMacroLayout->addWidget(m_launcherMacroEdit);
    QPushButton *launcherMacroHelpBtn = new QPushButton("?");
    launcherMacroHelpBtn->setFixedSize(24, 24);
    launcherMacroHelpBtn->setStyleSheet("QPushButton { border: 1px solid #cccccc; border-radius: 12px; background-color: #f0f0f0; font-weight: bold; } QPushButton:hover { background-color: #e0e0e0; }");
    launcherMacroHelpBtn->setToolTip("实现不同类型包的其他特定宏，如有多个，用空格分离\n已知宏:\nLAUNCHER_TYPE_BENCHMARK");
    connect(launcherMacroHelpBtn, &QPushButton::clicked, [this]() {
        QMessageBox::information(this, "启动器宏环境说明",
            "实现不同类型包的其他特定宏，如有多个，用空格分离\n\n"
            "已知宏:\n"
            "LAUNCHER_TYPE_BENCHMARK");
    });
    launcherMacroLayout->addWidget(launcherMacroHelpBtn);
    podfileLayout->addRow("启动器宏环境:", launcherMacroLayout);

    // 地区环境
    QHBoxLayout *areaEnvLayout = new QHBoxLayout;
    m_areaEnvEdit = new QLineEdit;
    m_areaEnvEdit->setPlaceholderText("可选配置");
    areaEnvLayout->addWidget(m_areaEnvEdit);
    QPushButton *areaEnvHelpBtn = new QPushButton("?");
    areaEnvHelpBtn->setFixedSize(24, 24);
    areaEnvHelpBtn->setStyleSheet("QPushButton { border: 1px solid #cccccc; border-radius: 12px; background-color: #f0f0f0; font-weight: bold; } QPushButton:hover { background-color: #e0e0e0; }");
    areaEnvHelpBtn->setToolTip("LAUNCHER_AREA_OVERSEA:海外\n其他:中国大陆");
    connect(areaEnvHelpBtn, &QPushButton::clicked, [this]() {
        QMessageBox::information(this, "地区环境说明",
            "LAUNCHER_AREA_OVERSEA: 海外\n"
            "其他: 中国大陆");
    });
    areaEnvLayout->addWidget(areaEnvHelpBtn);
    podfileLayout->addRow("地区环境:", areaEnvLayout);
    
    layout->addWidget(podfileGroup);
    
    // 项目配置组已移除，开发地区统一在基本信息标签页中管理
    layout->addStretch();
    
    m_tabWidget->addTab(scrollArea, "环境配置");
}

void ConfigEditorWidget::setupResourcesTab()
{
    m_resourcesTab = new QWidget;
    QScrollArea *scrollArea = new QScrollArea;
    scrollArea->setWidget(m_resourcesTab);
    scrollArea->setWidgetResizable(true);
    
    QVBoxLayout *layout = new QVBoxLayout(m_resourcesTab);
    
    // 应用图标组
    QGroupBox *iconGroup = createGroupBox("应用图标");
    QHBoxLayout *iconLayout = new QHBoxLayout(iconGroup);
    
    m_iconPreview = new QLabel;
    m_iconPreview->setFixedSize(100, 100);
    m_iconPreview->setStyleSheet("border: 1px solid #d0d0d0; background-color: #f8f8f8;");
    m_iconPreview->setAlignment(Qt::AlignCenter);
    m_iconPreview->setText("图标预览");
    iconLayout->addWidget(m_iconPreview);
    
    QVBoxLayout *iconInfoLayout = new QVBoxLayout;
    iconInfoLayout->addWidget(new QLabel("应用图标 (1024x1024)"));
    
    m_iconPathLabel = new QLabel("未设置");
    m_iconPathLabel->setStyleSheet("color: gray;");
    m_iconPathLabel->setWordWrap(true);
    iconInfoLayout->addWidget(m_iconPathLabel);
    
    m_selectIconButton = new QPushButton("选择图标文件");
    connect(m_selectIconButton, &QPushButton::clicked, this, &ConfigEditorWidget::onSelectIconFile);
    iconInfoLayout->addWidget(m_selectIconButton);
    
    iconInfoLayout->addStretch();
    iconLayout->addLayout(iconInfoLayout);
    
    layout->addWidget(iconGroup);
    
    // DMG资源组
    QGroupBox *dmgGroup = createGroupBox("DMG资源");
    QGridLayout *dmgLayout = new QGridLayout(dmgGroup);
    
    // DMG背景图
    dmgLayout->addWidget(new QLabel("DMG背景图:"), 0, 0);
    m_dmgBackgroundPreview = new QLabel;
    m_dmgBackgroundPreview->setFixedSize(80, 60);
    m_dmgBackgroundPreview->setStyleSheet("border: 1px solid #d0d0d0; background-color: #f8f8f8;");
    m_dmgBackgroundPreview->setAlignment(Qt::AlignCenter);
    m_dmgBackgroundPreview->setText("背景图");
    dmgLayout->addWidget(m_dmgBackgroundPreview, 0, 1);
    
    QVBoxLayout *dmgBgInfoLayout = new QVBoxLayout;
    m_dmgBackgroundPathLabel = new QLabel("未设置");
    m_dmgBackgroundPathLabel->setStyleSheet("color: gray;");
    dmgBgInfoLayout->addWidget(m_dmgBackgroundPathLabel);
    
    m_selectDmgBackgroundButton = new QPushButton("选择背景图");
    connect(m_selectDmgBackgroundButton, &QPushButton::clicked, this, &ConfigEditorWidget::onSelectDmgBackground);
    dmgBgInfoLayout->addWidget(m_selectDmgBackgroundButton);
    dmgLayout->addLayout(dmgBgInfoLayout, 0, 2);
    
    // DMG图标
    dmgLayout->addWidget(new QLabel("DMG图标:"), 1, 0);
    m_dmgIconPreview = new QLabel;
    m_dmgIconPreview->setFixedSize(60, 60);
    m_dmgIconPreview->setStyleSheet("border: 1px solid #d0d0d0; background-color: #f8f8f8;");
    m_dmgIconPreview->setAlignment(Qt::AlignCenter);
    m_dmgIconPreview->setText("DMG图标");
    dmgLayout->addWidget(m_dmgIconPreview, 1, 1);
    
    QVBoxLayout *dmgIconInfoLayout = new QVBoxLayout;
    m_dmgIconPathLabel = new QLabel("未设置");
    m_dmgIconPathLabel->setStyleSheet("color: gray;");
    dmgIconInfoLayout->addWidget(m_dmgIconPathLabel);
    
    m_selectDmgIconButton = new QPushButton("选择DMG图标");
    connect(m_selectDmgIconButton, &QPushButton::clicked, this, &ConfigEditorWidget::onSelectDmgIcon);
    dmgIconInfoLayout->addWidget(m_selectDmgIconButton);
    dmgLayout->addLayout(dmgIconInfoLayout, 1, 2);
    
    layout->addWidget(dmgGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(scrollArea, "资源文件");
}



void ConfigEditorWidget::setupGameParametersTab()
{
    m_gameParametersTab = new QWidget;
    QVBoxLayout *layout = new QVBoxLayout(m_gameParametersTab);

    // 标题和说明
    QLabel *titleLabel = new QLabel("游戏参数信息");
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(12);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    layout->addWidget(titleLabel);

    QLabel *descLabel = new QLabel("编辑 launcherConfig.json 文件内容：");
    descLabel->setStyleSheet("color: gray; margin-bottom: 10px;");
    layout->addWidget(descLabel);

    // JSON编辑器
    m_gameParametersEdit = new QTextEdit;
    m_gameParametersEdit->setPlaceholderText("JSON文件内容将在这里显示...");
    m_gameParametersEdit->setFont(QFont("Courier", 10)); // 使用等宽字体
    layout->addWidget(m_gameParametersEdit);

    // 提示信息
    QLabel *hintLabel = new QLabel("💡 提示：请确保JSON格式正确，保存时会自动验证格式。");
    hintLabel->setStyleSheet("color: #666; font-size: 11px; margin-top: 5px;");
    hintLabel->setWordWrap(true);
    layout->addWidget(hintLabel);

    m_tabWidget->addTab(m_gameParametersTab, "游戏参数信息");
}

void ConfigEditorWidget::createEmptyState()
{
    m_emptyStateWidget = new QWidget;
    QVBoxLayout *layout = new QVBoxLayout(m_emptyStateWidget);
    layout->setAlignment(Qt::AlignCenter);

    QLabel *iconLabel = new QLabel("🏗️");
    iconLabel->setAlignment(Qt::AlignCenter);
    QFont iconFont = iconLabel->font();
    iconFont.setPointSize(48);
    iconLabel->setFont(iconFont);
    layout->addWidget(iconLabel);

    QLabel *textLabel = new QLabel("请从左侧选择一个地区项目开始编辑配置");
    textLabel->setAlignment(Qt::AlignCenter);
    QFont textFont = textLabel->font();
    textFont.setPointSize(16);
    textLabel->setFont(textFont);
    textLabel->setStyleSheet("color: gray;");
    layout->addWidget(textLabel);

    QLabel *subTextLabel = new QLabel("选择项目后，您可以编辑该地区的所有配置文件");
    subTextLabel->setAlignment(Qt::AlignCenter);
    QFont subTextFont = subTextLabel->font();
    subTextFont.setPointSize(12);
    subTextLabel->setFont(subTextFont);
    subTextLabel->setStyleSheet("color: lightgray;");
    layout->addWidget(subTextLabel);

    m_mainLayout->addWidget(m_emptyStateWidget);
}

QGroupBox* ConfigEditorWidget::createGroupBox(const QString &title)
{
    QGroupBox *groupBox = new QGroupBox(title);
    return groupBox;
}

void ConfigEditorWidget::onCurrentRegionChanged()
{
    QString currentRegion = m_configManager->currentRegion();
    bool hasRegion = !currentRegion.isEmpty();

    if (hasRegion) {
        m_titleLabel->setText("配置编辑 - " + currentRegion);
        m_tabWidget->setVisible(true);
        m_emptyStateWidget->setVisible(false);
        updateConfigDisplay();
    } else {
        m_titleLabel->setText("请选择地区项目");
        m_tabWidget->setVisible(false);
        m_emptyStateWidget->setVisible(true);
        m_saveButton->setEnabled(false);
    }
}

void ConfigEditorWidget::onConfigChanged()
{
    bool hasChanges = m_configManager->hasUnsavedChanges();
    bool hasRegion = !m_configManager->currentRegion().isEmpty();
    m_saveButton->setEnabled(hasRegion && hasChanges);
}

void ConfigEditorWidget::onFieldChanged()
{
    // 简化的变更检测：任意字段修改后立即启用保存按钮
    if (!m_updating && !m_configManager->currentRegion().isEmpty()) {
        m_saveButton->setEnabled(true);
    }
}

void ConfigEditorWidget::onSaveConfig()
{
    saveToConfig();
    m_configManager->saveCurrentConfig();
}

void ConfigEditorWidget::updateConfigDisplay()
{
    if (m_updating) return;
    m_updating = true;

    updateFromConfig();

    m_updating = false;
}

void ConfigEditorWidget::updateFromConfig()
{
    qDebug() << "updateFromConfig: Starting update";
    QVariantMap config = m_configManager->currentConfig();

    // 更新基本信息
    QVariantMap infoPlist = config["InfoPlistConfig"].toMap();
    m_bundleIdEdit->setText(infoPlist["CFBundleIdentifier"].toString());
    // 使用CFBundleDisplayName作为应用名称的主要来源，如果为空则使用CFBundleName
    QString appName = infoPlist["CFBundleDisplayName"].toString();
    if (appName.isEmpty()) {
        appName = infoPlist["CFBundleName"].toString();
    }
    m_appNameEdit->setText(appName);
    m_appKeyEdit->setText(infoPlist["APP_KEY"].toString());
    m_versionEdit->setText(infoPlist["CFBundleShortVersionString"].toString());
    m_buildNumberEdit->setText(infoPlist["CFBundleVersion"].toString());

    QString devRegion = infoPlist["CFBundleDevelopmentRegion"].toString();
    int devRegionIndex = m_developmentRegionCombo->findText(devRegion);
    if (devRegionIndex >= 0) {
        m_developmentRegionCombo->setCurrentIndex(devRegionIndex);
    }

    m_feedUrlEdit->setText(infoPlist["SUFeedURL"].toString());

    // 更新环境配置
    QVariantMap podfileConfig = config["PodfileConfig"].toMap();
    m_sceneEnvEdit->setText(podfileConfig["SCENCE_ENV"].toString());

    m_packageNameEdit->setText(podfileConfig["PACKAGE_NAME"].toString());
    m_launcherMacroEdit->setText(podfileConfig["LAUNCHER_MACRO_ENV"].toString());
    m_areaEnvEdit->setText(podfileConfig["AREA_ENV"].toString());

    // 项目配置中的开发地区现在统一由基本信息标签页中的开发地区字段管理

    // 更新资源文件
    QVariantMap resourcePaths = config["ResourcePaths"].toMap();
    updateImagePreview(m_iconPreview, m_iconPathLabel, resourcePaths["iconPath"].toString());
    updateImagePreview(m_dmgBackgroundPreview, m_dmgBackgroundPathLabel, resourcePaths["dmgBackgroundPath"].toString());
    updateImagePreview(m_dmgIconPreview, m_dmgIconPathLabel, resourcePaths["dmgIconPath"].toString());

    // 新增：更新高级配置
    qDebug() << "updateFromConfig: Updating packaging config";
    updatePackagingConfigDisplay();
    qDebug() << "updateFromConfig: Updating notarization config";
    updateNotarizationConfigDisplay();
    qDebug() << "updateFromConfig: Updating game parameters";
    updateGameParametersDisplay();
    qDebug() << "updateFromConfig: Updating SDK parameters";
    if (!m_configManager->currentRegion().isEmpty() && m_sdkParametersTab) {
        updateSDKParametersDisplay();
    }
    qDebug() << "updateFromConfig: Finished update";
}

void ConfigEditorWidget::saveToConfig()
{
    if (m_updating) return;

    // 保存基本信息
    m_configManager->updateConfigValue("InfoPlistConfig", "CFBundleIdentifier", m_bundleIdEdit->text());
    // 应用名称同时更新CFBundleName和CFBundleDisplayName，确保两个值保持一致
    QString appName = m_appNameEdit->text();
    m_configManager->updateConfigValue("InfoPlistConfig", "CFBundleName", appName);
    m_configManager->updateConfigValue("InfoPlistConfig", "CFBundleDisplayName", appName);
    m_configManager->updateConfigValue("InfoPlistConfig", "APP_KEY", m_appKeyEdit->text());
    m_configManager->updateConfigValue("InfoPlistConfig", "CFBundleShortVersionString", m_versionEdit->text());
    m_configManager->updateConfigValue("InfoPlistConfig", "CFBundleVersion", m_buildNumberEdit->text());

    // 开发地区同步更新三个字段，确保三个值保持一致
    QString devRegion = m_developmentRegionCombo->currentText();
    m_configManager->updateConfigValue("InfoPlistConfig", "CFBundleDevelopmentRegion", devRegion);
    m_configManager->updateConfigValue("ProjectConfig", "developmentRegion", devRegion);
    // 同时更新knownRegions数组
    QVariantList knownRegions;
    knownRegions.append(devRegion);
    m_configManager->updateConfigValue("ProjectConfig", "knownRegions", knownRegions);

    m_configManager->updateConfigValue("InfoPlistConfig", "SUFeedURL", m_feedUrlEdit->text());

    // 保存环境配置
    m_configManager->updateConfigValue("PodfileConfig", "SCENCE_ENV", m_sceneEnvEdit->text());
    m_configManager->updateConfigValue("PodfileConfig", "PACKAGE_NAME", m_packageNameEdit->text());
    m_configManager->updateConfigValue("PodfileConfig", "LAUNCHER_MACRO_ENV", m_launcherMacroEdit->text());
    m_configManager->updateConfigValue("PodfileConfig", "AREA_ENV", m_areaEnvEdit->text());
}

void ConfigEditorWidget::updateImagePreview(QLabel *preview, QLabel *pathLabel, const QString &imagePath)
{
    if (imagePath.isEmpty()) {
        preview->setText("无图像");
        pathLabel->setText("未设置");
        pathLabel->setStyleSheet("color: gray;");
        return;
    }

    QPixmap pixmap(imagePath);
    if (!pixmap.isNull()) {
        QPixmap scaledPixmap = pixmap.scaled(preview->size(), Qt::KeepAspectRatio, Qt::SmoothTransformation);
        preview->setPixmap(scaledPixmap);

        QFileInfo fileInfo(imagePath);
        pathLabel->setText(fileInfo.fileName());
        pathLabel->setStyleSheet("color: black;");
    } else {
        preview->setText("加载失败");
        pathLabel->setText("文件无效");
        pathLabel->setStyleSheet("color: red;");
    }
}

void ConfigEditorWidget::onSelectIconFile()
{
    QString filePath = QFileDialog::getOpenFileName(this, "选择应用图标",
        QString(), "图像文件 (*.png *.jpg *.jpeg);;PNG 文件 (*.png);;所有文件 (*)");

    if (!filePath.isEmpty()) {
        QString targetPath = m_configManager->getRegionIconPath(m_configManager->currentRegion());
        if (m_configManager->copyImageFile(filePath, targetPath)) {
            m_configManager->updateConfigValue("ResourcePaths", "iconPath", targetPath);
            updateImagePreview(m_iconPreview, m_iconPathLabel, targetPath);
        }
    }
}

void ConfigEditorWidget::onSelectDmgBackground()
{
    QString filePath = QFileDialog::getOpenFileName(this, "选择DMG背景图",
        QString(), "图像文件 (*.png *.jpg *.jpeg);;PNG 文件 (*.png);;所有文件 (*)");

    if (!filePath.isEmpty()) {
        QString targetPath = m_configManager->getRegionReleasePath(m_configManager->currentRegion()) + "/dmg-background.png";
        if (m_configManager->copyImageFile(filePath, targetPath)) {
            m_configManager->updateConfigValue("ResourcePaths", "dmgBackgroundPath", targetPath);
            updateImagePreview(m_dmgBackgroundPreview, m_dmgBackgroundPathLabel, targetPath);
        }
    }
}

void ConfigEditorWidget::onSelectDmgIcon()
{
    QString filePath = QFileDialog::getOpenFileName(this, "选择DMG图标",
        QString(), "图像文件 (*.icns *.png);;ICNS 文件 (*.icns);;所有文件 (*)");

    if (!filePath.isEmpty()) {
        QString targetPath = m_configManager->getRegionReleasePath(m_configManager->currentRegion()) + "/dmg-icon.icns";
        if (m_configManager->copyImageFile(filePath, targetPath)) {
            m_configManager->updateConfigValue("ResourcePaths", "dmgIconPath", targetPath);
            updateImagePreview(m_dmgIconPreview, m_dmgIconPathLabel, targetPath);
        }
    }
}

// ==================== 新增：高级配置方法实现 ====================

void ConfigEditorWidget::updatePackagingConfigDisplay()
{
    // 临时禁用信号以避免循环更新
    bool wasUpdating = m_updating;
    m_updating = true;

    QVariantMap packagingConfig = m_configManager->getPackagingConfig();
    m_bundleIdPackagingEdit->setText(packagingConfig["bundleId"].toString());
    m_provisioningProfileEdit->setText(packagingConfig["provisioningProfile"].toString());
    m_teamIdEdit->setText(packagingConfig["teamId"].toString());

    m_updating = wasUpdating;
}

void ConfigEditorWidget::updateNotarizationConfigDisplay()
{
    // 临时禁用信号以避免循环更新
    bool wasUpdating = m_updating;
    m_updating = true;

    QVariantMap notarizationConfig = m_configManager->getNotarizationConfig();
    m_codeSignIdentityEdit->setText(notarizationConfig["codeSignIdentity"].toString());
    m_keychainProfileEdit->setText(notarizationConfig["keychainProfile"].toString());

    m_updating = wasUpdating;
}

void ConfigEditorWidget::updateGameParametersDisplay()
{
    // 临时禁用信号以避免循环更新
    bool wasUpdating = m_updating;
    m_updating = true;

    // 只显示 config 部分的内容
    QVariantMap gameConfig = m_configManager->getGameConfig();
    QJsonObject jsonObj = QJsonObject::fromVariantMap(gameConfig);
    QJsonDocument doc(jsonObj);
    QString jsonContent = doc.toJson(QJsonDocument::Indented);
    m_gameParametersEdit->setPlainText(jsonContent);

    m_updating = wasUpdating;
}

void ConfigEditorWidget::onPackagingConfigChanged()
{
    if (m_updating) return;

    QVariantMap config;
    config["bundleId"] = m_bundleIdPackagingEdit->text();
    config["provisioningProfile"] = m_provisioningProfileEdit->text();
    config["teamId"] = m_teamIdEdit->text();

    if (m_configManager->updatePackagingConfig(config)) {
        // 配置已更新，触发保存按钮状态更新
        onFieldChanged();
    }
}

void ConfigEditorWidget::onNotarizationConfigChanged()
{
    if (m_updating) return;

    QVariantMap config;
    config["codeSignIdentity"] = m_codeSignIdentityEdit->text();
    config["keychainProfile"] = m_keychainProfileEdit->text();

    if (m_configManager->updateNotarizationConfig(config)) {
        // 配置已更新，触发保存按钮状态更新
        onFieldChanged();
    }
}

void ConfigEditorWidget::onGameParametersChanged()
{
    if (m_updating) return;

    QString jsonContent = m_gameParametersEdit->toPlainText();

    // 解析JSON内容
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(jsonContent.toUtf8(), &error);
    if (error.error != QJsonParseError::NoError) {
        // JSON格式错误，暂时不更新
        return;
    }

    // 更新游戏配置
    QVariantMap gameConfig = doc.object().toVariantMap();
    if (m_configManager->updateGameConfig(gameConfig)) {
        // 配置已更新，触发保存按钮状态更新
        onFieldChanged();
    }
}

void ConfigEditorWidget::setupSDKParametersTab()
{
    qDebug() << "setupSDKParametersTab: Starting setup";

    // 创建主标签页
    m_sdkParametersTab = new QWidget;

    // 创建滚动区域
    m_sdkScrollArea = new QScrollArea;
    m_sdkScrollArea->setWidgetResizable(true);

    // 创建内容容器
    m_sdkContentWidget = new QWidget;
    m_sdkMainLayout = new QVBoxLayout(m_sdkContentWidget);

    // 标题和说明
    QLabel *titleLabel = new QLabel("SDK参数信息");
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(12);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    m_sdkMainLayout->addWidget(titleLabel);

    QLabel *descLabel = new QLabel("编辑 launcherConfig.json 文件中的 SDK 相关配置：");
    descLabel->setStyleSheet("color: gray; margin-bottom: 10px;");
    m_sdkMainLayout->addWidget(descLabel);

    // 创建三个配置组
    qDebug() << "setupSDKParametersTab: Creating config groups";
    setupAnalysisConfigGroup();
    setupCrashConfigGroup();
    setupOudConfigGroup();

    m_sdkMainLayout->addStretch();

    // 设置滚动区域的内容
    m_sdkScrollArea->setWidget(m_sdkContentWidget);

    // 创建标签页布局
    QVBoxLayout *tabLayout = new QVBoxLayout(m_sdkParametersTab);
    tabLayout->setContentsMargins(0, 0, 0, 0);
    tabLayout->addWidget(m_sdkScrollArea);

    // 添加到标签页控件
    m_tabWidget->addTab(m_sdkParametersTab, "SDK参数信息");

    qDebug() << "setupSDKParametersTab: Setup completed";
}

void ConfigEditorWidget::setupAnalysisConfigGroup()
{
    qDebug() << "setupAnalysisConfigGroup: Creating analysis config group";
    m_analysisConfigGroup = createGroupBox("分析配置 (analysisConfig)");
    m_analysisConfigLayout = new QFormLayout(m_analysisConfigGroup);

    // 添加说明标签
    QLabel *infoLabel = new QLabel("用于数据分析和统计的配置参数");
    infoLabel->setStyleSheet("color: gray; font-size: 11px;");
    m_analysisConfigLayout->addRow(infoLabel);

    m_sdkMainLayout->addWidget(m_analysisConfigGroup);
    qDebug() << "setupAnalysisConfigGroup: Analysis config group created";
}

void ConfigEditorWidget::setupCrashConfigGroup()
{
    qDebug() << "setupCrashConfigGroup: Creating crash config group";
    m_crashConfigGroup = createGroupBox("崩溃配置 (crashConfig)");
    m_crashConfigLayout = new QFormLayout(m_crashConfigGroup);

    // 添加说明标签
    QLabel *infoLabel = new QLabel("用于崩溃报告和错误收集的配置参数");
    infoLabel->setStyleSheet("color: gray; font-size: 11px;");
    m_crashConfigLayout->addRow(infoLabel);

    m_sdkMainLayout->addWidget(m_crashConfigGroup);
    qDebug() << "setupCrashConfigGroup: Crash config group created";
}

void ConfigEditorWidget::setupOudConfigGroup()
{
    qDebug() << "setupOudConfigGroup: Creating OUD config group";
    m_oudConfigGroup = createGroupBox("OUD配置 (oudConfig)");
    m_oudConfigLayout = new QFormLayout(m_oudConfigGroup);

    // 添加说明标签
    QLabel *infoLabel = new QLabel("用于用户数据和行为分析的配置参数");
    infoLabel->setStyleSheet("color: gray; font-size: 11px;");
    m_oudConfigLayout->addRow(infoLabel);

    m_sdkMainLayout->addWidget(m_oudConfigGroup);
    qDebug() << "setupOudConfigGroup: OUD config group created";
}

void ConfigEditorWidget::updateSDKParametersDisplay()
{
    qDebug() << "updateSDKParametersDisplay: Starting update";

    // 检查SDK组件是否已初始化
    if (!m_analysisConfigGroup || !m_crashConfigGroup || !m_oudConfigGroup) {
        qDebug() << "updateSDKParametersDisplay: SDK components not initialized yet";
        return;
    }

    if (!m_analysisConfigLayout || !m_crashConfigLayout || !m_oudConfigLayout) {
        qDebug() << "updateSDKParametersDisplay: SDK layouts not initialized yet";
        return;
    }

    // 临时禁用信号以避免循环更新
    bool wasUpdating = m_updating;
    m_updating = true;

    qDebug() << "updateSDKParametersDisplay: Updating analysis config";
    updateSDKConfigGroup(m_analysisConfigGroup, m_analysisConfigLayout,
                        m_analysisConfigEdits, m_configManager->getAnalysisConfig());

    qDebug() << "updateSDKParametersDisplay: Updating crash config";
    updateSDKConfigGroup(m_crashConfigGroup, m_crashConfigLayout,
                        m_crashConfigEdits, m_configManager->getCrashConfig());

    qDebug() << "updateSDKParametersDisplay: Updating OUD config";
    updateSDKConfigGroup(m_oudConfigGroup, m_oudConfigLayout,
                        m_oudConfigEdits, m_configManager->getOudConfig());

    m_updating = wasUpdating;
    qDebug() << "updateSDKParametersDisplay: Update completed";
}

void ConfigEditorWidget::onSDKParametersChanged()
{
    if (m_updating) return;

    // 收集所有SDK配置的修改
    QVariantMap analysisConfig = collectSDKConfigFromEdits(m_analysisConfigEdits);
    QVariantMap crashConfig = collectSDKConfigFromEdits(m_crashConfigEdits);
    QVariantMap oudConfig = collectSDKConfigFromEdits(m_oudConfigEdits);

    // 特殊处理OUD配置中的areaType
    if (m_areaTypeCombo) {
        oudConfig["areaType"] = m_areaTypeCombo->currentData().toInt();
    }

    // 更新配置
    m_configManager->updateAnalysisConfig(analysisConfig);
    m_configManager->updateCrashConfig(crashConfig);
    m_configManager->updateOudConfig(oudConfig);

    // 触发保存按钮状态更新
    onFieldChanged();
}

void ConfigEditorWidget::updateSDKConfigGroup(QGroupBox *group, QFormLayout *layout,
                                             QMap<QString, QLineEdit*> &edits, const QVariantMap &config)
{
    if (!group || !layout) {
        qWarning() << "updateSDKConfigGroup: Invalid group or layout";
        return;
    }

    qDebug() << "updateSDKConfigGroup: Updating group" << group->title() << "with" << config.size() << "items";

    // 清除现有的输入控件，但保留说明标签
    QList<QLayoutItem*> itemsToRemove;
    for (int i = 0; i < layout->rowCount(); ++i) {
        QLayoutItem *labelItem = layout->itemAt(i, QFormLayout::LabelRole);
        QLayoutItem *fieldItem = layout->itemAt(i, QFormLayout::FieldRole);

        // 如果这一行有字段控件（不是纯说明标签），则标记为删除
        if (fieldItem && fieldItem->widget()) {
            itemsToRemove.append(labelItem);
            itemsToRemove.append(fieldItem);
        }
    }

    // 删除标记的控件
    for (QLayoutItem *item : itemsToRemove) {
        if (item && item->widget()) {
            // 如果删除的是areaType的ComboBox，清空引用
            if (item->widget() == m_areaTypeCombo) {
                m_areaTypeCombo = nullptr;
            }
            layout->removeItem(item);
            delete item->widget();
            delete item;
        }
    }
    edits.clear();

    // 为每个配置项创建输入控件
    for (auto it = config.begin(); it != config.end(); ++it) {
        QString key = it.key();
        QString value = it.value().toString();

        // 特殊处理areaType字段
        if (key == "areaType" && group->title().contains("OUD配置")) {
            // 创建下拉菜单
            m_areaTypeCombo = new QComboBox;
            m_areaTypeCombo->addItem("0", 0);
            m_areaTypeCombo->addItem("2", 2);

            // 设置当前值
            int currentValue = value.toInt();
            int index = m_areaTypeCombo->findData(currentValue);
            if (index >= 0) {
                m_areaTypeCombo->setCurrentIndex(index);
            }

            // 连接信号
            connect(m_areaTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
                    this, &ConfigEditorWidget::onAreaTypeChanged);

            layout->addRow(key + ":", m_areaTypeCombo);
            // 注意：areaType不添加到edits映射中，因为它不是QLineEdit
        } else {
            QLineEdit *edit = new QLineEdit;
            edit->setText(value);
            edit->setPlaceholderText("请输入 " + key + " 的值");

            // 连接信号
            connect(edit, &QLineEdit::textChanged, this, &ConfigEditorWidget::onSDKParametersChanged);

            layout->addRow(key + ":", edit);
            edits[key] = edit;
        }
    }

    qDebug() << "updateSDKConfigGroup: Finished updating group" << group->title();
}

QVariantMap ConfigEditorWidget::collectSDKConfigFromEdits(const QMap<QString, QLineEdit*> &edits)
{
    QVariantMap config;
    for (auto it = edits.begin(); it != edits.end(); ++it) {
        QString key = it.key();
        QString value = it.value()->text();

        // 尝试转换为适当的数据类型
        bool isNumber;
        int intValue = value.toInt(&isNumber);
        if (isNumber) {
            config[key] = intValue;
        } else {
            config[key] = value;
        }
    }
    return config;
}

void ConfigEditorWidget::onAreaTypeChanged()
{
    if (m_updating || !m_areaTypeCombo) return;

    // 获取当前选择的areaType值
    int areaType = m_areaTypeCombo->currentData().toInt();

    // 根据areaType设置对应的hostUrl
    QString hostUrl;
    if (areaType == 0) {
        hostUrl = "https://wos.laohu.com";
    } else if (areaType == 2) {
        hostUrl = "https://wos.perfectworldgames.com";
    }

    // 查找hostUrl的输入框并设置值
    if (m_oudConfigEdits.contains("hostUrl")) {
        QLineEdit *hostUrlEdit = m_oudConfigEdits["hostUrl"];
        if (hostUrlEdit) {
            hostUrlEdit->setText(hostUrl);
        }
    }

    // 触发配置更新
    onSDKParametersChanged();
}
