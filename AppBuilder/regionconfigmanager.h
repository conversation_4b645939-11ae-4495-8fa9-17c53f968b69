#ifndef REGIONCONFIGMANAGER_H
#define REGIONCONFIGMANAGER_H

#include <QObject>
#include <QStringList>
#include <QVariantMap>
#include <QDir>
#include <QJsonObject>
#include <QJsonDocument>
#include "aescrypto.h"

class RegionConfigManager : public QObject
{
    Q_OBJECT

    Q_PROPERTY(QStringList regionProjects READ regionProjects NOTIFY regionProjectsChanged)
    Q_PROPERTY(QString currentRegion READ currentRegion WRITE setCurrentRegion NOTIFY currentRegionChanged)
    Q_PROPERTY(QVariantMap currentConfig READ currentConfig NOTIFY currentConfigChanged)
    Q_PROPERTY(QString projectRoot READ projectRoot WRITE setProjectRoot NOTIFY projectRootChanged)
    Q_PROPERTY(bool isValidProjectRoot READ isValidProjectRoot NOTIFY isValidProjectRootChanged)
    Q_PROPERTY(bool hasUnsavedChanges READ hasUnsavedChanges NOTIFY hasUnsavedChangesChanged)

public:
    explicit RegionConfigManager(QObject *parent = nullptr);

    // 属性访问器
    QStringList regionProjects() const { return m_regionProjects; }
    QString currentRegion() const { return m_currentRegion; }
    QVariantMap currentConfig() const { return m_currentConfig; }
    QString projectRoot() const { return m_projectRoot; }
    bool hasUnsavedChanges() const { return m_hasUnsavedChanges; }
    bool isValidProjectRoot() const;

    // 属性设置器
    void setCurrentRegion(const QString &region);
    void setProjectRoot(const QString &path);

public slots:
    // 核心功能
    void scanRegionProjects();
    void loadRegionConfig(const QString &regionName);
    bool saveCurrentConfig();
    bool validateConfig();
    
    // 配置操作
    void updateConfigValue(const QString &section, const QString &key, const QVariant &value);
    QVariant getConfigValue(const QString &section, const QString &key) const;
    
    // 文件操作
    QString selectImageFile(const QString &title = "选择图像文件");
    QString getRegionIconPath(const QString &regionName) const;
    QString getRegionReleasePath(const QString &regionName) const;
    bool copyImageFile(const QString &sourcePath, const QString &targetPath);
    
    // 实用功能
    QString getAbsolutePath(const QString &relativePath) const;
    QStringList getSupportedImageFormats() const;
    bool fileExists(const QString &path) const;

    // 项目根目录管理
    Q_INVOKABLE QString selectProjectRoot();
    Q_INVOKABLE bool validateProjectRoot(const QString &path) const;
    Q_INVOKABLE QString getDefaultProjectRoot() const;

    // 新增：高级配置功能
    Q_INVOKABLE QVariantMap getPackagingConfig() const;
    Q_INVOKABLE QVariantMap getNotarizationConfig() const;
    Q_INVOKABLE QString getLauncherConfigJson() const;
    Q_INVOKABLE bool updatePackagingConfig(const QVariantMap &config);
    Q_INVOKABLE bool updateNotarizationConfig(const QVariantMap &config);
    Q_INVOKABLE bool updateLauncherConfigJson(const QString &jsonContent);

    // 新增：分离的启动器配置管理
    Q_INVOKABLE QVariantMap getGameConfig() const;
    Q_INVOKABLE QVariantMap getAnalysisConfig() const;
    Q_INVOKABLE QVariantMap getCrashConfig() const;
    Q_INVOKABLE QVariantMap getOudConfig() const;
    Q_INVOKABLE bool updateGameConfig(const QVariantMap &config);
    Q_INVOKABLE bool updateAnalysisConfig(const QVariantMap &config);
    Q_INVOKABLE bool updateCrashConfig(const QVariantMap &config);
    Q_INVOKABLE bool updateOudConfig(const QVariantMap &config);

signals:
    void regionProjectsChanged();
    void currentRegionChanged();
    void currentConfigChanged();
    void projectRootChanged();
    void isValidProjectRootChanged();
    void hasUnsavedChangesChanged();
    void errorOccurred(const QString &message);
    void configSaved(const QString &regionName);

private:
    // 内部方法
    void loadPlistConfig(const QString &filePath);
    void loadJsonConfig(const QString &filePath, const QString &section);
    bool savePlistConfig(const QString &filePath);
    bool saveJsonConfig(const QString &filePath, const QString &section);
    void setHasUnsavedChanges(bool hasChanges);
    QString findProjectRoot() const;

    // 新增：高级配置内部方法
    QVariantMap parsePlistFile(const QString &filePath) const;
    bool writePlistFile(const QString &filePath, const QVariantMap &data) const;
    QString readJsonFile(const QString &filePath) const;
    bool writeJsonFile(const QString &filePath, const QString &content) const;
    void parseLauncherConfig();
    QString combineLauncherConfigs() const;
    bool saveCombinedLauncherConfig();

    // 成员变量
    QStringList m_regionProjects;
    QString m_currentRegion;
    QVariantMap m_currentConfig;
    QString m_projectRoot;
    bool m_hasUnsavedChanges;

    // 新增：分离的启动器配置数据
    QVariantMap m_gameConfig;
    QVariantMap m_analysisConfig;
    QVariantMap m_crashConfig;
    QVariantMap m_oudConfig;
    
    // 配置文件路径缓存
    QString m_currentRegionConfigPath;
    QString m_currentExportOptionsPath;
    QString m_currentNotarizationPath;
    QString m_currentLauncherConfigPath;
};

#endif // REGIONCONFIGMANAGER_H
