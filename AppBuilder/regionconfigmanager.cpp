#include "regionconfigmanager.h"
#include <QApplication>
#include <QFileDialog>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QJsonObject>
#include <QSettings>
#include <QDebug>
#include <QFileInfo>
#include <QImageReader>
#include <QTextStream>
#include <QStringConverter>

RegionConfigManager::RegionConfigManager(QObject *parent)
    : QObject(parent)
    , m_hasUnsavedChanges(false)
{
    // 自动查找项目根目录
    m_projectRoot = findProjectRoot();
    if (m_projectRoot.isEmpty()) {
        m_projectRoot = getDefaultProjectRoot();
    }

    // 扫描地区项目
    scanRegionProjects();
}

void RegionConfigManager::setCurrentRegion(const QString &region)
{
    if (m_currentRegion != region) {
        m_currentRegion = region;
        emit currentRegionChanged();
        
        if (!region.isEmpty()) {
            loadRegionConfig(region);
        }
    }
}

void RegionConfigManager::setProjectRoot(const QString &path)
{
    if (m_projectRoot != path) {
        m_projectRoot = path;
        emit projectRootChanged();
        emit isValidProjectRootChanged();
        scanRegionProjects();
    }
}

void RegionConfigManager::scanRegionProjects()
{
    m_regionProjects.clear();
    
    if (m_projectRoot.isEmpty()) {
        emit regionProjectsChanged();
        return;
    }
    
    QDir channelsDir(m_projectRoot + "/Channels");
    if (!channelsDir.exists()) {
        qWarning() << "Channels directory not found:" << channelsDir.absolutePath();
        emit regionProjectsChanged();
        return;
    }
    
    // 扫描所有包含 RegionConfig.plist 的目录
    QStringList entries = channelsDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);
    for (const QString &entry : entries) {
        QString configPath = channelsDir.absoluteFilePath(entry + "/RegionConfig.plist");
        if (QFileInfo::exists(configPath)) {
            m_regionProjects.append(entry);
        }
    }
    
    m_regionProjects.sort();
    emit regionProjectsChanged();
    
    qDebug() << "Found" << m_regionProjects.size() << "region projects:" << m_regionProjects;
}

void RegionConfigManager::loadRegionConfig(const QString &regionName)
{
    if (regionName.isEmpty() || m_projectRoot.isEmpty()) {
        return;
    }

    m_currentRegion = regionName;  // 设置当前地区名称
    m_currentConfig.clear();

    // 构建配置文件路径
    QString regionPath = m_projectRoot + "/Channels/" + regionName;
    m_currentRegionConfigPath = regionPath + "/RegionConfig.plist";
    m_currentExportOptionsPath = regionPath + "/Release/ExportOptions.plist";
    m_currentNotarizationPath = regionPath + "/Release/notarization.plist";
    m_currentLauncherConfigPath = regionPath + "/Release/Resources/launcherConfig.json";
    
    // 加载主配置文件
    loadPlistConfig(m_currentRegionConfigPath);
    
    // 加载其他配置文件
    if (QFileInfo::exists(m_currentExportOptionsPath)) {
        loadPlistConfig(m_currentExportOptionsPath);
    }
    
    if (QFileInfo::exists(m_currentNotarizationPath)) {
        loadPlistConfig(m_currentNotarizationPath);
    }
    
    if (QFileInfo::exists(m_currentLauncherConfigPath)) {
        loadJsonConfig(m_currentLauncherConfigPath, "LauncherConfig");
        // 解析分离的启动器配置
        parseLauncherConfig();
    }
    
    // 添加资源文件路径信息
    QVariantMap resourcePaths;
    resourcePaths["iconPath"] = regionPath + "/icon_1024x1024.png";
    resourcePaths["dmgBackgroundPath"] = regionPath + "/Release/dmg-background.png";
    resourcePaths["dmgIconPath"] = regionPath + "/Release/dmg-icon.icns";
    m_currentConfig["ResourcePaths"] = resourcePaths;
    
    setHasUnsavedChanges(false);
    emit currentConfigChanged();
    
    qDebug() << "Loaded config for region:" << regionName;
}

void RegionConfigManager::loadPlistConfig(const QString &filePath)
{
    if (!QFileInfo::exists(filePath)) {
        qWarning() << "Plist file not found:" << filePath;
        return;
    }

    QSettings settings(filePath, QSettings::NativeFormat);

    // 读取所有键值对
    QStringList keys = settings.allKeys();
    for (const QString &key : keys) {
        QVariant value = settings.value(key);

        // 处理扁平结构的键名，如 "InfoPlistConfig·CFBundleName"（注意使用·分隔符）
        if (key.contains("·")) {
            QStringList keyParts = key.split("·");
            if (keyParts.size() >= 2) {
                QString section = keyParts[0];
                QString subKey = keyParts.mid(1).join("·");

                QVariantMap sectionMap = m_currentConfig[section].toMap();
                sectionMap[subKey] = value;
                m_currentConfig[section] = sectionMap;
            }
        } else {
            // 处理没有前缀的键（如ExportOptions.plist中的键）
            if (filePath.contains("ExportOptions.plist")) {
                QVariantMap exportMap = m_currentConfig["ExportOptions"].toMap();
                exportMap[key] = value;
                m_currentConfig["ExportOptions"] = exportMap;
            } else if (filePath.contains("notarization.plist")) {
                QVariantMap notarizationMap = m_currentConfig["Notarization"].toMap();
                notarizationMap[key] = value;
                m_currentConfig["Notarization"] = notarizationMap;
            } else {
                m_currentConfig[key] = value;
            }
        }
    }

    qDebug() << "Loaded plist config from:" << filePath << "with" << keys.size() << "keys";
}

void RegionConfigManager::loadJsonConfig(const QString &filePath, const QString &section)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        qWarning() << "Cannot open JSON file:" << filePath;
        return;
    }
    
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "JSON parse error:" << error.errorString();
        return;
    }
    
    m_currentConfig[section] = doc.object().toVariantMap();
}

bool RegionConfigManager::saveCurrentConfig()
{
    if (m_currentRegion.isEmpty() || !m_hasUnsavedChanges) {
        return true;
    }
    
    bool success = true;
    
    // 保存主配置文件
    if (!savePlistConfig(m_currentRegionConfigPath)) {
        success = false;
    }
    
    // 保存其他配置文件
    if (QFileInfo::exists(m_currentExportOptionsPath)) {
        if (!savePlistConfig(m_currentExportOptionsPath)) {
            success = false;
        }
    }
    
    if (QFileInfo::exists(m_currentNotarizationPath)) {
        if (!savePlistConfig(m_currentNotarizationPath)) {
            success = false;
        }
    }
    
    if (QFileInfo::exists(m_currentLauncherConfigPath)) {
        if (!saveJsonConfig(m_currentLauncherConfigPath, "LauncherConfig")) {
            success = false;
        }
    }
    
    if (success) {
        setHasUnsavedChanges(false);
        emit configSaved(m_currentRegion);
        qDebug() << "Config saved successfully for region:" << m_currentRegion;
    } else {
        emit errorOccurred("保存配置文件时发生错误");
    }
    
    return success;
}

bool RegionConfigManager::savePlistConfig(const QString &filePath)
{
    // 使用QSettings保存plist文件
    QSettings settings(filePath, QSettings::NativeFormat);

    // 清除现有内容
    settings.clear();

    // 根据文件路径确定要保存的配置节
    if (filePath.contains("RegionConfig.plist")) {
        // 保存主配置文件的所有节，使用正确的键名格式
        QStringList sections = {"InfoPlistConfig", "PodfileConfig", "ProjectConfig"};
        for (const QString &section : sections) {
            QVariantMap sectionData = m_currentConfig[section].toMap();
            for (auto it = sectionData.begin(); it != sectionData.end(); ++it) {
                // 使用正确的键名格式，如 "InfoPlistConfig·CFBundleName"（注意使用·而不是.）
                QString flatKey = section + "·" + it.key();
                settings.setValue(flatKey, it.value());
            }
        }
    } else if (filePath.contains("ExportOptions.plist")) {
        // 保存导出选项配置
        QVariantMap exportData = m_currentConfig["ExportOptions"].toMap();
        for (auto it = exportData.begin(); it != exportData.end(); ++it) {
            settings.setValue(it.key(), it.value());
        }
    } else if (filePath.contains("notarization.plist")) {
        // 保存公证配置
        QVariantMap notarizationData = m_currentConfig["Notarization"].toMap();
        for (auto it = notarizationData.begin(); it != notarizationData.end(); ++it) {
            settings.setValue(it.key(), it.value());
        }
    }

    settings.sync();

    if (settings.status() != QSettings::NoError) {
        qWarning() << "Failed to save plist config to:" << filePath;
        return false;
    }

    qDebug() << "Successfully saved plist config to:" << filePath;
    return true;
}

bool RegionConfigManager::saveJsonConfig(const QString &filePath, const QString &section)
{
    QVariantMap configData = m_currentConfig[section].toMap();
    QJsonObject jsonObj = QJsonObject::fromVariantMap(configData);
    QJsonDocument doc(jsonObj);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly)) {
        qWarning() << "Cannot open JSON file for writing:" << filePath;
        return false;
    }
    
    file.write(doc.toJson());
    return true;
}

void RegionConfigManager::updateConfigValue(const QString &section, const QString &key, const QVariant &value)
{
    QVariantMap sectionMap = m_currentConfig[section].toMap();
    sectionMap[key] = value;
    m_currentConfig[section] = sectionMap;
    
    setHasUnsavedChanges(true);
    emit currentConfigChanged();
}

QVariant RegionConfigManager::getConfigValue(const QString &section, const QString &key) const
{
    QVariantMap sectionMap = m_currentConfig[section].toMap();
    return sectionMap[key];
}

QString RegionConfigManager::selectImageFile(const QString &title)
{
    QStringList filters;
    filters << "图像文件 (*.png *.jpg *.jpeg *.icns)"
            << "PNG 文件 (*.png)"
            << "JPEG 文件 (*.jpg *.jpeg)"
            << "ICNS 文件 (*.icns)"
            << "所有文件 (*)";
    
    return QFileDialog::getOpenFileName(nullptr, title, QString(), filters.join(";;"));
}

QString RegionConfigManager::getRegionIconPath(const QString &regionName) const
{
    return m_projectRoot + "/Channels/" + regionName + "/icon_1024x1024.png";
}

QString RegionConfigManager::getRegionReleasePath(const QString &regionName) const
{
    return m_projectRoot + "/Channels/" + regionName + "/Release";
}

bool RegionConfigManager::copyImageFile(const QString &sourcePath, const QString &targetPath)
{
    if (sourcePath == targetPath) {
        return true;
    }
    
    // 确保目标目录存在
    QFileInfo targetInfo(targetPath);
    QDir targetDir = targetInfo.dir();
    if (!targetDir.exists()) {
        targetDir.mkpath(".");
    }
    
    // 如果目标文件已存在，先删除
    if (QFileInfo::exists(targetPath)) {
        QFile::remove(targetPath);
    }
    
    return QFile::copy(sourcePath, targetPath);
}

QString RegionConfigManager::getAbsolutePath(const QString &relativePath) const
{
    if (QFileInfo(relativePath).isAbsolute()) {
        return relativePath;
    }
    return m_projectRoot + "/" + relativePath;
}

QStringList RegionConfigManager::getSupportedImageFormats() const
{
    QStringList formats;
    QList<QByteArray> supportedFormats = QImageReader::supportedImageFormats();
    for (const QByteArray &format : supportedFormats) {
        formats.append(QString::fromLatin1(format).toLower());
    }
    return formats;
}

bool RegionConfigManager::fileExists(const QString &path) const
{
    return QFileInfo::exists(getAbsolutePath(path));
}

bool RegionConfigManager::validateConfig()
{
    // 实现配置验证逻辑
    if (m_currentConfig.isEmpty()) {
        emit errorOccurred("配置为空");
        return false;
    }
    
    // 检查必需的配置项
    QVariantMap infoPlistConfig = m_currentConfig["InfoPlistConfig"].toMap();
    if (infoPlistConfig["CFBundleIdentifier"].toString().isEmpty()) {
        emit errorOccurred("Bundle ID 不能为空");
        return false;
    }
    
    if (infoPlistConfig["CFBundleName"].toString().isEmpty()) {
        emit errorOccurred("产品名称不能为空");
        return false;
    }
    
    return true;
}

void RegionConfigManager::setHasUnsavedChanges(bool hasChanges)
{
    if (m_hasUnsavedChanges != hasChanges) {
        m_hasUnsavedChanges = hasChanges;
        emit hasUnsavedChangesChanged();
    }
}

QString RegionConfigManager::findProjectRoot() const
{
    // 首先尝试固定的项目路径
    QString fixedPath = "/Users/<USER>/Desktop/pwrdwork/macos-launcher";
    if (validateProjectRoot(fixedPath)) {
        return fixedPath;
    }

    // 从当前应用程序目录开始向上查找项目根目录
    QDir currentDir = QDir::current();

    // 检查当前目录是否包含 Channels 目录
    while (!currentDir.isRoot()) {
        if (currentDir.exists("Channels") && currentDir.exists("Scripts")) {
            return currentDir.absolutePath();
        }
        if (!currentDir.cdUp()) {
            break;
        }
    }

    // 如果没找到，返回固定路径作为默认值
    return fixedPath;
}

bool RegionConfigManager::isValidProjectRoot() const
{
    return validateProjectRoot(m_projectRoot);
}

QString RegionConfigManager::selectProjectRoot()
{
    QString selectedPath = QFileDialog::getExistingDirectory(
        nullptr,
        "选择项目根目录",
        m_projectRoot.isEmpty() ? QStandardPaths::writableLocation(QStandardPaths::HomeLocation) : m_projectRoot,
        QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks
    );

    if (!selectedPath.isEmpty() && validateProjectRoot(selectedPath)) {
        setProjectRoot(selectedPath);
        return selectedPath;
    }

    return QString();
}

bool RegionConfigManager::validateProjectRoot(const QString &path) const
{
    if (path.isEmpty()) {
        return false;
    }

    QDir dir(path);
    if (!dir.exists()) {
        return false;
    }

    // 检查是否包含Channels目录
    if (!dir.exists("Channels")) {
        return false;
    }

    // 检查Channels目录是否包含至少一个地区项目
    QDir channelsDir(dir.absoluteFilePath("Channels"));
    QStringList entries = channelsDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);

    for (const QString &entry : entries) {
        QString configPath = channelsDir.absoluteFilePath(entry + "/RegionConfig.plist");
        if (QFileInfo::exists(configPath)) {
            return true;
        }
    }

    return false;
}

QString RegionConfigManager::getDefaultProjectRoot() const
{
    // 尝试几个常见的项目位置
    QStringList possiblePaths = {
        "/Users/<USER>/Desktop/pwrdwork/macos-launcher",
        QStandardPaths::writableLocation(QStandardPaths::HomeLocation) + "/Desktop/pwrdwork/macos-launcher",
        QStandardPaths::writableLocation(QStandardPaths::HomeLocation) + "/Documents/macos-launcher",
        QStandardPaths::writableLocation(QStandardPaths::HomeLocation) + "/Projects/macos-launcher"
    };

    for (const QString &path : possiblePaths) {
        if (validateProjectRoot(path)) {
            return path;
        }
    }

    // 如果都没找到，返回用户主目录
    return QStandardPaths::writableLocation(QStandardPaths::HomeLocation);
}

// ==================== 新增：高级配置功能实现 ====================

QVariantMap RegionConfigManager::getPackagingConfig() const
{
    QVariantMap result;

    if (m_currentRegion.isEmpty() || !QFileInfo::exists(m_currentExportOptionsPath)) {
        return result;
    }

    QVariantMap exportOptions = parsePlistFile(m_currentExportOptionsPath);

    // 提取打包配置信息
    if (exportOptions.contains("provisioningProfiles")) {
        QVariantMap provisioningProfiles = exportOptions["provisioningProfiles"].toMap();

        // 提取BundleID和描述文件名
        for (auto it = provisioningProfiles.begin(); it != provisioningProfiles.end(); ++it) {
            result["bundleId"] = it.key();
            result["provisioningProfile"] = it.value().toString();
            break; // 只取第一个，因为通常只有一个
        }
    }

    // 提取TeamID
    if (exportOptions.contains("teamID")) {
        result["teamId"] = exportOptions["teamID"].toString();
    }

    return result;
}

QVariantMap RegionConfigManager::getNotarizationConfig() const
{
    QVariantMap result;

    if (m_currentRegion.isEmpty() || !QFileInfo::exists(m_currentNotarizationPath)) {
        return result;
    }

    QVariantMap notarization = parsePlistFile(m_currentNotarizationPath);

    // 提取公证配置信息
    if (notarization.contains("CODE_SIGN_IDENTITY")) {
        result["codeSignIdentity"] = notarization["CODE_SIGN_IDENTITY"].toString();
    }

    if (notarization.contains("KEYCHAIN_PROFILE")) {
        result["keychainProfile"] = notarization["KEYCHAIN_PROFILE"].toString();
    }

    return result;
}

QString RegionConfigManager::getLauncherConfigJson() const
{
    if (m_currentRegion.isEmpty() || !QFileInfo::exists(m_currentLauncherConfigPath)) {
        return QString();
    }

    // 读取原始文件内容
    QString rawContent = readJsonFile(m_currentLauncherConfigPath);
    if (rawContent.isEmpty()) {
        return QString();
    }

    // 获取当前配置中的App_KEY - 修正：从InfoPlistConfig节获取APP_KEY
    QString appKey = m_currentConfig.value("InfoPlistConfig").toMap().value("APP_KEY").toString();

    // 调试信息
    qDebug() << "getLauncherConfigJson: Current config keys:" << m_currentConfig.keys();
    qDebug() << "getLauncherConfigJson: InfoPlistConfig keys:" << m_currentConfig.value("InfoPlistConfig").toMap().keys();
    qDebug() << "getLauncherConfigJson: APP_KEY value:" << appKey;

    if (appKey.isEmpty()) {
        qDebug() << "getLauncherConfigJson: APP_KEY is empty, returning raw content";
        return rawContent;
    }

    // 尝试解密内容
    QString decryptedContent = AESCrypto::decryptLauncherConfig(rawContent, appKey);
    qDebug() << "getLauncherConfigJson: Decryption result length:" << decryptedContent.length();
    return decryptedContent;
}

bool RegionConfigManager::updatePackagingConfig(const QVariantMap &config)
{
    if (m_currentRegion.isEmpty()) {
        return false;
    }

    // 读取现有的ExportOptions.plist
    QVariantMap exportOptions = parsePlistFile(m_currentExportOptionsPath);

    // 更新provisioningProfiles
    if (config.contains("bundleId") && config.contains("provisioningProfile")) {
        QVariantMap provisioningProfiles;
        provisioningProfiles[config["bundleId"].toString()] = config["provisioningProfile"].toString();
        exportOptions["provisioningProfiles"] = provisioningProfiles;
    }

    // 更新teamID
    if (config.contains("teamId")) {
        exportOptions["teamID"] = config["teamId"].toString();
    }

    // 保存文件
    bool success = writePlistFile(m_currentExportOptionsPath, exportOptions);
    if (success) {
        setHasUnsavedChanges(true);
        emit currentConfigChanged();
    }

    return success;
}

bool RegionConfigManager::updateNotarizationConfig(const QVariantMap &config)
{
    if (m_currentRegion.isEmpty()) {
        return false;
    }

    // 读取现有的notarization.plist
    QVariantMap notarization = parsePlistFile(m_currentNotarizationPath);

    // 更新签名证书
    if (config.contains("codeSignIdentity")) {
        notarization["CODE_SIGN_IDENTITY"] = config["codeSignIdentity"].toString();
    }

    // 更新Keychain profile
    if (config.contains("keychainProfile")) {
        notarization["KEYCHAIN_PROFILE"] = config["keychainProfile"].toString();
    }

    // 保存文件
    bool success = writePlistFile(m_currentNotarizationPath, notarization);
    if (success) {
        setHasUnsavedChanges(true);
        emit currentConfigChanged();
    }

    return success;
}

bool RegionConfigManager::updateLauncherConfigJson(const QString &jsonContent)
{
    if (m_currentRegion.isEmpty()) {
        return false;
    }

    // 验证JSON格式
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(jsonContent.toUtf8(), &error);
    if (error.error != QJsonParseError::NoError) {
        emit errorOccurred(QString("JSON格式错误: %1").arg(error.errorString()));
        return false;
    }

    // 获取当前配置中的App_KEY - 修正：从InfoPlistConfig节获取APP_KEY
    QString appKey = m_currentConfig.value("InfoPlistConfig").toMap().value("APP_KEY").toString();
    QString contentToSave = jsonContent;

    // 调试信息
    qDebug() << "updateLauncherConfigJson: APP_KEY value:" << appKey;

    if (!appKey.isEmpty()) {
        // 如果有App_KEY，则加密内容后保存
        QString encryptedContent = AESCrypto::encryptLauncherConfig(jsonContent, appKey);
        if (!encryptedContent.isEmpty()) {
            contentToSave = encryptedContent;
            qDebug() << "updateLauncherConfigJson: Content encrypted successfully";
        } else {
            qWarning() << "updateLauncherConfigJson: Failed to encrypt content, saving as plain text";
        }
    } else {
        qDebug() << "updateLauncherConfigJson: APP_KEY is empty, saving as plain text";
    }

    // 保存文件
    bool success = writeJsonFile(m_currentLauncherConfigPath, contentToSave);
    if (success) {
        setHasUnsavedChanges(true);
        emit currentConfigChanged();
    }

    return success;
}

// ==================== 内部辅助方法实现 ====================

QVariantMap RegionConfigManager::parsePlistFile(const QString &filePath) const
{
    QVariantMap result;

    if (!QFileInfo::exists(filePath)) {
        return result;
    }

    QSettings settings(filePath, QSettings::NativeFormat);
    QStringList keys = settings.allKeys();

    for (const QString &key : keys) {
        QVariant value = settings.value(key);
        result[key] = value;
    }

    return result;
}

bool RegionConfigManager::writePlistFile(const QString &filePath, const QVariantMap &data) const
{
    // 确保目录存在
    QFileInfo fileInfo(filePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            qWarning() << "Failed to create directory:" << dir.absolutePath();
            return false;
        }
    }

    QSettings settings(filePath, QSettings::NativeFormat);

    // 清除现有内容
    settings.clear();

    // 写入新数据
    for (auto it = data.begin(); it != data.end(); ++it) {
        settings.setValue(it.key(), it.value());
    }

    settings.sync();
    return settings.status() == QSettings::NoError;
}

QString RegionConfigManager::readJsonFile(const QString &filePath) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Cannot open JSON file for reading:" << filePath;
        return QString();
    }

    return QString::fromUtf8(file.readAll());
}

bool RegionConfigManager::writeJsonFile(const QString &filePath, const QString &content) const
{
    // 确保目录存在
    QFileInfo fileInfo(filePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists()) {
        if (!dir.mkpath(".")) {
            qWarning() << "Failed to create directory:" << dir.absolutePath();
            return false;
        }
    }

    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        qWarning() << "Cannot open JSON file for writing:" << filePath;
        return false;
    }

    QTextStream out(&file);
    out.setEncoding(QStringConverter::Utf8);
    out << content;

    return true;
}

// ==================== 新增：分离的启动器配置管理 ====================

void RegionConfigManager::parseLauncherConfig()
{
    // 清空之前的配置
    m_gameConfig.clear();
    m_analysisConfig.clear();
    m_crashConfig.clear();
    m_oudConfig.clear();

    // 获取解密后的JSON内容
    QString jsonContent = getLauncherConfigJson();
    if (jsonContent.isEmpty()) {
        qDebug() << "parseLauncherConfig: No launcher config content to parse";
        return;
    }

    // 解析JSON
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(jsonContent.toUtf8(), &error);
    if (error.error != QJsonParseError::NoError) {
        qWarning() << "parseLauncherConfig: JSON parse error:" << error.errorString();
        return;
    }

    QJsonObject rootObj = doc.object();

    // 解析各个配置节
    if (rootObj.contains("config")) {
        m_gameConfig = rootObj["config"].toObject().toVariantMap();
    }

    if (rootObj.contains("analysisConfig")) {
        m_analysisConfig = rootObj["analysisConfig"].toObject().toVariantMap();
    }

    if (rootObj.contains("crashConfig")) {
        m_crashConfig = rootObj["crashConfig"].toObject().toVariantMap();
    }

    if (rootObj.contains("oudConfig")) {
        m_oudConfig = rootObj["oudConfig"].toObject().toVariantMap();
    }

    qDebug() << "parseLauncherConfig: Parsed launcher config successfully";
    qDebug() << "  - gameConfig keys:" << m_gameConfig.keys();
    qDebug() << "  - analysisConfig keys:" << m_analysisConfig.keys();
    qDebug() << "  - crashConfig keys:" << m_crashConfig.keys();
    qDebug() << "  - oudConfig keys:" << m_oudConfig.keys();
}

QVariantMap RegionConfigManager::getGameConfig() const
{
    return m_gameConfig;
}

QVariantMap RegionConfigManager::getAnalysisConfig() const
{
    return m_analysisConfig;
}

QVariantMap RegionConfigManager::getCrashConfig() const
{
    return m_crashConfig;
}

QVariantMap RegionConfigManager::getOudConfig() const
{
    return m_oudConfig;
}

bool RegionConfigManager::updateGameConfig(const QVariantMap &config)
{
    m_gameConfig = config;
    return saveCombinedLauncherConfig();
}

bool RegionConfigManager::updateAnalysisConfig(const QVariantMap &config)
{
    m_analysisConfig = config;
    return saveCombinedLauncherConfig();
}

bool RegionConfigManager::updateCrashConfig(const QVariantMap &config)
{
    m_crashConfig = config;
    return saveCombinedLauncherConfig();
}

bool RegionConfigManager::updateOudConfig(const QVariantMap &config)
{
    m_oudConfig = config;
    return saveCombinedLauncherConfig();
}

QString RegionConfigManager::combineLauncherConfigs() const
{
    QJsonObject rootObj;

    // 添加各个配置节
    if (!m_gameConfig.isEmpty()) {
        rootObj["config"] = QJsonObject::fromVariantMap(m_gameConfig);
    }

    if (!m_analysisConfig.isEmpty()) {
        rootObj["analysisConfig"] = QJsonObject::fromVariantMap(m_analysisConfig);
    }

    if (!m_crashConfig.isEmpty()) {
        rootObj["crashConfig"] = QJsonObject::fromVariantMap(m_crashConfig);
    }

    if (!m_oudConfig.isEmpty()) {
        rootObj["oudConfig"] = QJsonObject::fromVariantMap(m_oudConfig);
    }

    QJsonDocument doc(rootObj);
    return doc.toJson(QJsonDocument::Compact);
}

bool RegionConfigManager::saveCombinedLauncherConfig()
{
    if (m_currentRegion.isEmpty()) {
        return false;
    }

    // 合并所有配置
    QString combinedJson = combineLauncherConfigs();
    if (combinedJson.isEmpty()) {
        qWarning() << "saveCombinedLauncherConfig: Combined JSON is empty";
        return false;
    }

    // 使用现有的updateLauncherConfigJson方法来加密和保存
    return updateLauncherConfigJson(combinedJson);
}
