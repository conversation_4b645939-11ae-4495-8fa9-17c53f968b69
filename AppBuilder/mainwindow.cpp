#include "mainwindow.h"
#include "regionconfigmanager.h"
#include "regionlistwidget.h"
#include "configeditorwidget.h"

#include <QApplication>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QMenuBar>
#include <QStatusBar>
#include <QLabel>
#include <QMessageBox>
#include <QCloseEvent>
#include <QKeySequence>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_configManager(new RegionConfigManager(this))
    , m_regionListWidget(nullptr)
    , m_configEditorWidget(nullptr)
    , m_splitter(nullptr)
{
    setWindowTitle("地区配置编辑器");
    setMinimumSize(1200, 800);
    resize(1600, 900);
    
    setupUI();
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    setupConnections();
    
    // 初始化状态
    updateWindowTitle();
    updateStatusBar();
}

MainWindow::~MainWindow()
{
}

void MainWindow::setupUI()
{
    // 创建中央分割器
    m_splitter = new QSplitter(Qt::Horizontal, this);
    setCentralWidget(m_splitter);
    
    // 创建左侧地区列表
    m_regionListWidget = new RegionListWidget(m_configManager, this);
    m_splitter->addWidget(m_regionListWidget);
    
    // 创建右侧配置编辑器
    m_configEditorWidget = new ConfigEditorWidget(m_configManager, this);
    m_splitter->addWidget(m_configEditorWidget);
    
    // 设置分割器比例
    m_splitter->setSizes({300, 900});
    m_splitter->setStretchFactor(0, 0);
    m_splitter->setStretchFactor(1, 1);
}

void MainWindow::setupMenuBar()
{
    // 文件菜单
    QMenu *fileMenu = menuBar()->addMenu("文件");
    
    m_saveAction = fileMenu->addAction("保存配置");
    m_saveAction->setShortcut(QKeySequence::Save);
    m_saveAction->setEnabled(false);
    connect(m_saveAction, &QAction::triggered, this, &MainWindow::onSaveConfig);
    
    fileMenu->addSeparator();
    
    m_refreshAction = fileMenu->addAction("刷新项目列表");
    m_refreshAction->setShortcut(QKeySequence::Refresh);
    connect(m_refreshAction, &QAction::triggered, this, &MainWindow::onRefreshProjects);
    
    fileMenu->addSeparator();
    
    m_exitAction = fileMenu->addAction("退出");
    m_exitAction->setShortcut(QKeySequence::Quit);
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);
    
    // 编辑菜单
    QMenu *editMenu = menuBar()->addMenu("编辑");
    
    m_validateAction = editMenu->addAction("验证配置");
    m_validateAction->setEnabled(false);
    connect(m_validateAction, &QAction::triggered, this, &MainWindow::onValidateConfig);
    
    // 帮助菜单
    QMenu *helpMenu = menuBar()->addMenu("帮助");
    
    m_aboutAction = helpMenu->addAction("关于");
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::onAbout);
}

void MainWindow::setupStatusBar()
{
    // 当前地区标签
    m_currentRegionLabel = new QLabel("请选择地区项目");
    statusBar()->addWidget(m_currentRegionLabel);
    
    // 分隔符
    statusBar()->addPermanentWidget(new QLabel("|"));
    
    // 保存状态标签
    m_saveStatusLabel = new QLabel("已保存");
    m_saveStatusLabel->setStyleSheet("color: green;");
    statusBar()->addPermanentWidget(m_saveStatusLabel);
    
    // 分隔符
    statusBar()->addPermanentWidget(new QLabel("|"));
    
    // 项目路径标签
    m_projectPathLabel = new QLabel();
    m_projectPathLabel->setStyleSheet("color: gray; font-size: 10px;");
    statusBar()->addPermanentWidget(m_projectPathLabel);
}

void MainWindow::setupConnections()
{
    // 配置管理器信号
    connect(m_configManager, &RegionConfigManager::currentRegionChanged,
            this, &MainWindow::updateWindowTitle);
    connect(m_configManager, &RegionConfigManager::currentRegionChanged,
            this, &MainWindow::updateStatusBar);
    connect(m_configManager, &RegionConfigManager::hasUnsavedChangesChanged,
            this, &MainWindow::onConfigChanged);
    connect(m_configManager, &RegionConfigManager::configSaved,
            this, &MainWindow::onConfigSaved);
    connect(m_configManager, &RegionConfigManager::errorOccurred,
            this, &MainWindow::onErrorOccurred);
    connect(m_configManager, &RegionConfigManager::projectRootChanged,
            this, &MainWindow::onProjectRootChanged);
    connect(m_configManager, &RegionConfigManager::isValidProjectRootChanged,
            this, &MainWindow::onProjectRootValidationChanged);

    // 工具栏按钮信号
    connect(m_selectProjectButton, &QPushButton::clicked,
            this, &MainWindow::onSelectProjectRoot);
}

void MainWindow::updateWindowTitle()
{
    QString title = "地区配置编辑器";
    
    if (!m_configManager->currentRegion().isEmpty()) {
        title += " - " + m_configManager->currentRegion();
    }
    
    if (m_configManager->hasUnsavedChanges()) {
        title += " *";
    }
    
    setWindowTitle(title);
}

void MainWindow::updateStatusBar()
{
    // 更新当前地区
    if (m_configManager->currentRegion().isEmpty()) {
        m_currentRegionLabel->setText("请选择地区项目");
        m_currentRegionLabel->setStyleSheet("color: gray;");
    } else {
        m_currentRegionLabel->setText("当前地区: " + m_configManager->currentRegion());
        m_currentRegionLabel->setStyleSheet("color: black;");
    }
    
    // 更新保存状态
    if (m_configManager->hasUnsavedChanges()) {
        m_saveStatusLabel->setText("有未保存的更改");
        m_saveStatusLabel->setStyleSheet("color: red;");
    } else {
        m_saveStatusLabel->setText("已保存");
        m_saveStatusLabel->setStyleSheet("color: green;");
    }
    
    // 更新项目路径
    m_projectPathLabel->setText("项目根目录: " + m_configManager->projectRoot());
    
    // 更新菜单状态
    bool hasRegion = !m_configManager->currentRegion().isEmpty();
    m_saveAction->setEnabled(hasRegion && m_configManager->hasUnsavedChanges());
    m_validateAction->setEnabled(hasRegion);
}

void MainWindow::onSaveConfig()
{
    if (m_configManager->saveCurrentConfig()) {
        // 保存成功，状态会通过信号自动更新
    }
}

void MainWindow::onRefreshProjects()
{
    m_configManager->scanRegionProjects();
}

void MainWindow::onValidateConfig()
{
    if (m_configManager->validateConfig()) {
        QMessageBox::information(this, "验证结果", "配置验证通过！");
    }
    // 错误信息会通过errorOccurred信号显示
}

void MainWindow::onAbout()
{
    QMessageBox::about(this, "关于地区配置编辑器",
        "<h3>地区配置编辑器</h3>"
        "<p>版本 1.0.0</p>"
        "<p>这是一个用于管理macOS启动器地区配置的可视化工具。</p>"
        "<p><b>功能特性：</b></p>"
        "<ul>"
        "<li>自动扫描Channels目录下的地区项目</li>"
        "<li>可视化编辑RegionConfig.plist配置</li>"
        "<li>管理图标和资源文件</li>"
        "<li>配置文件格式验证</li>"
        "<li>实时保存和预览</li>"
        "</ul>"
        "<p>© 2024 Wanmei Technology</p>");
}

void MainWindow::onConfigChanged()
{
    updateWindowTitle();
    updateStatusBar();
}

void MainWindow::onConfigSaved(const QString &regionName)
{
    updateStatusBar();
    statusBar()->showMessage(QString("地区 '%1' 的配置已成功保存").arg(regionName), 3000);
}

void MainWindow::onErrorOccurred(const QString &message)
{
    QMessageBox::warning(this, "错误", message);
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    if (m_configManager->hasUnsavedChanges()) {
        QMessageBox::StandardButton ret = QMessageBox::question(this, "确认退出",
            "您有未保存的更改，确定要退出吗？",
            QMessageBox::Yes | QMessageBox::No | QMessageBox::Save);
        
        if (ret == QMessageBox::Save) {
            if (m_configManager->saveCurrentConfig()) {
                event->accept();
            } else {
                event->ignore();
            }
        } else if (ret == QMessageBox::Yes) {
            event->accept();
        } else {
            event->ignore();
        }
    } else {
        event->accept();
    }
}

void MainWindow::setupToolBar()
{
    QToolBar *toolBar = addToolBar("主工具栏");
    toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    // 选择项目根目录按钮
    m_selectProjectButton = new QPushButton("选择项目目录");
    m_selectProjectButton->setIcon(style()->standardIcon(QStyle::SP_DirOpenIcon));
    m_selectProjectButton->setToolTip("选择包含Channels目录的项目根目录");
    toolBar->addWidget(m_selectProjectButton);

    toolBar->addSeparator();

    // 添加项目路径显示
    QLabel *pathLabel = new QLabel("项目路径:");
    toolBar->addWidget(pathLabel);

    m_projectPathLabel = new QLabel();
    m_projectPathLabel->setStyleSheet("QLabel { color: #666666; font-family: monospace; }");
    m_projectPathLabel->setMinimumWidth(300);
    toolBar->addWidget(m_projectPathLabel);

    updateProjectPathDisplay();
}

void MainWindow::onSelectProjectRoot()
{
    QString selectedPath = m_configManager->selectProjectRoot();
    if (!selectedPath.isEmpty()) {
        QMessageBox::information(this, "项目目录已更新",
                                QString("项目根目录已设置为:\n%1").arg(selectedPath));
    }
}

void MainWindow::onProjectRootChanged()
{
    updateProjectPathDisplay();
    updateStatusBar();
    updateWindowTitle();
}

void MainWindow::onProjectRootValidationChanged()
{
    updateProjectPathDisplay();

    if (!m_configManager->isValidProjectRoot()) {
        QMessageBox::warning(this, "项目目录无效",
                            "当前项目目录不包含有效的Channels目录或地区配置文件。\n"
                            "请选择正确的项目根目录。");
    }
}

void MainWindow::updateProjectPathDisplay()
{
    QString projectRoot = m_configManager->projectRoot();
    bool isValid = m_configManager->isValidProjectRoot();

    if (projectRoot.isEmpty()) {
        m_projectPathLabel->setText("未选择项目目录");
        m_projectPathLabel->setStyleSheet("QLabel { color: #ff6666; font-family: monospace; }");
    } else if (isValid) {
        // 显示简化的路径
        QString displayPath = projectRoot;
        if (displayPath.length() > 50) {
            displayPath = "..." + displayPath.right(47);
        }
        m_projectPathLabel->setText(displayPath);
        m_projectPathLabel->setStyleSheet("QLabel { color: #666666; font-family: monospace; }");
    } else {
        QString displayPath = projectRoot;
        if (displayPath.length() > 50) {
            displayPath = "..." + displayPath.right(47);
        }
        m_projectPathLabel->setText(displayPath + " (无效)");
        m_projectPathLabel->setStyleSheet("QLabel { color: #ff6666; font-family: monospace; }");
    }

    m_projectPathLabel->setToolTip(projectRoot);
}
