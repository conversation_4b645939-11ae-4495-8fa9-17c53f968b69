#include "aescrypto.h"
#include <QCryptographicHash>
#include <QDebug>
#include <QJsonDocument>
#include <QJsonParseError>
#include <openssl/aes.h>
#include <openssl/evp.h>

QString AESCrypto::aes128Encrypt(const QString &plainText, const QString &key)
{
    if (plainText.isEmpty() || key.length() != 16) {
        qWarning() << "AES128Encrypt: Invalid input - plainText is empty or key length is not 16";
        return QString();
    }
    
    QByteArray plainData = plainText.toUtf8();
    QByteArray keyData = key.toUtf8();
    
    // PKCS7填充
    QByteArray paddedData = pkcs7Padding(plainData, 16);
    
    // 创建加密上下文
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        qWarning() << "AES128Encrypt: Failed to create cipher context";
        return QString();
    }
    
    // 初始化加密操作（AES-128-ECB模式）
    if (EVP_EncryptInit_ex(ctx, EVP_aes_128_ecb(), nullptr, 
                          reinterpret_cast<const unsigned char*>(keyData.data()), nullptr) != 1) {
        qWarning() << "AES128Encrypt: Failed to initialize encryption";
        EVP_CIPHER_CTX_free(ctx);
        return QString();
    }
    
    // 禁用填充，因为我们已经手动进行了PKCS7填充
    EVP_CIPHER_CTX_set_padding(ctx, 0);
    
    // 执行加密
    QByteArray cipherData(paddedData.size() + 16, 0); // 预留额外空间
    int len = 0;
    int cipherLen = 0;
    
    if (EVP_EncryptUpdate(ctx, reinterpret_cast<unsigned char*>(cipherData.data()), &len,
                         reinterpret_cast<const unsigned char*>(paddedData.data()), paddedData.size()) != 1) {
        qWarning() << "AES128Encrypt: Failed to encrypt data";
        EVP_CIPHER_CTX_free(ctx);
        return QString();
    }
    cipherLen = len;
    
    if (EVP_EncryptFinal_ex(ctx, reinterpret_cast<unsigned char*>(cipherData.data()) + len, &len) != 1) {
        qWarning() << "AES128Encrypt: Failed to finalize encryption";
        EVP_CIPHER_CTX_free(ctx);
        return QString();
    }
    cipherLen += len;
    
    EVP_CIPHER_CTX_free(ctx);
    
    // 调整密文大小
    cipherData.resize(cipherLen);
    
    // 返回Base64编码的结果
    return cipherData.toBase64();
}

QString AESCrypto::aes128Decrypt(const QString &cipherText, const QString &key)
{
    if (cipherText.isEmpty() || key.length() != 16) {
        qWarning() << "AES128Decrypt: Invalid input - cipherText is empty or key length is not 16";
        return QString();
    }
    
    // Base64解码
    QByteArray cipherData = QByteArray::fromBase64(cipherText.toUtf8());
    if (cipherData.isEmpty()) {
        qWarning() << "AES128Decrypt: Failed to decode Base64 data";
        return QString();
    }
    
    QByteArray keyData = key.toUtf8();
    
    // 创建解密上下文
    EVP_CIPHER_CTX *ctx = EVP_CIPHER_CTX_new();
    if (!ctx) {
        qWarning() << "AES128Decrypt: Failed to create cipher context";
        return QString();
    }
    
    // 初始化解密操作（AES-128-ECB模式）
    if (EVP_DecryptInit_ex(ctx, EVP_aes_128_ecb(), nullptr,
                          reinterpret_cast<const unsigned char*>(keyData.data()), nullptr) != 1) {
        qWarning() << "AES128Decrypt: Failed to initialize decryption";
        EVP_CIPHER_CTX_free(ctx);
        return QString();
    }
    
    // 禁用填充，因为我们会手动处理PKCS7填充
    EVP_CIPHER_CTX_set_padding(ctx, 0);
    
    // 执行解密
    QByteArray plainData(cipherData.size() + 16, 0); // 预留额外空间
    int len = 0;
    int plainLen = 0;
    
    if (EVP_DecryptUpdate(ctx, reinterpret_cast<unsigned char*>(plainData.data()), &len,
                         reinterpret_cast<const unsigned char*>(cipherData.data()), cipherData.size()) != 1) {
        qWarning() << "AES128Decrypt: Failed to decrypt data";
        EVP_CIPHER_CTX_free(ctx);
        return QString();
    }
    plainLen = len;
    
    if (EVP_DecryptFinal_ex(ctx, reinterpret_cast<unsigned char*>(plainData.data()) + len, &len) != 1) {
        qWarning() << "AES128Decrypt: Failed to finalize decryption";
        EVP_CIPHER_CTX_free(ctx);
        return QString();
    }
    plainLen += len;
    
    EVP_CIPHER_CTX_free(ctx);
    
    // 调整明文大小
    plainData.resize(plainLen);
    
    // PKCS7去填充
    QByteArray unpaddedData = pkcs7UnPadding(plainData);
    if (unpaddedData.isEmpty()) {
        qWarning() << "AES128Decrypt: Failed to remove PKCS7 padding";
        return QString();
    }
    
    return QString::fromUtf8(unpaddedData);
}

QString AESCrypto::encryptLauncherConfig(const QString &info, const QString &appKey)
{
    QString aesKey = getAESKey(appKey);
    if (aesKey.isEmpty()) {
        qWarning() << "EncryptLauncherConfig: Invalid AppKey - length must be at least 16 characters";
        return QString();
    }

    return aes128Encrypt(info, aesKey);
}

QString AESCrypto::decryptLauncherConfig(const QString &encryptedData, const QString &appKey)
{
    if (encryptedData.isEmpty()) {
        return QString();
    }

    QString aesKey = getAESKey(appKey);
    if (aesKey.isEmpty()) {
        qWarning() << "DecryptLauncherConfig: Invalid AppKey - length must be at least 16 characters";
        return encryptedData; // 返回原始数据，兼容未加密的老版本
    }

    // 尝试解密
    QString decrypted = aes128Decrypt(encryptedData, aesKey);

    // 如果解密失败，可能是未加密的数据，直接返回原始数据
    if (decrypted.isEmpty()) {
        qDebug() << "DecryptLauncherConfig: Decryption failed, returning original data (might be unencrypted)";
        return encryptedData;
    }

    // 验证解密后的数据是否为有效的JSON
    QJsonParseError error;
    QJsonDocument::fromJson(decrypted.toUtf8(), &error);
    if (error.error != QJsonParseError::NoError) {
        qDebug() << "DecryptLauncherConfig: Decrypted data is not valid JSON, returning original data";
        return encryptedData;
    }

    return decrypted;
}

QByteArray AESCrypto::pkcs7Padding(const QByteArray &data, int blockSize)
{
    int paddingSize = blockSize - (data.size() % blockSize);
    QByteArray paddedData = data;

    // 添加填充字节，每个填充字节的值等于填充的字节数
    for (int i = 0; i < paddingSize; ++i) {
        paddedData.append(static_cast<char>(paddingSize));
    }

    return paddedData;
}

QByteArray AESCrypto::pkcs7UnPadding(const QByteArray &data)
{
    if (data.isEmpty()) {
        return QByteArray();
    }

    // 获取最后一个字节，它表示填充的字节数
    int paddingSize = static_cast<unsigned char>(data.at(data.size() - 1));

    // 验证填充是否有效
    if (paddingSize <= 0 || paddingSize > 16 || paddingSize > data.size()) {
        qWarning() << "PKCS7UnPadding: Invalid padding size:" << paddingSize;
        return QByteArray();
    }

    // 验证所有填充字节是否都相同
    for (int i = data.size() - paddingSize; i < data.size(); ++i) {
        if (static_cast<unsigned char>(data.at(i)) != paddingSize) {
            qWarning() << "PKCS7UnPadding: Invalid padding bytes";
            return QByteArray();
        }
    }

    // 移除填充字节
    return data.left(data.size() - paddingSize);
}

QString AESCrypto::getAESKey(const QString &appKey)
{
    if (appKey.length() < 16) {
        qWarning() << "GetAESKey: AppKey length is less than 16 characters";
        return QString();
    }

    // 取AppKey的后16位作为AES密钥
    return appKey.right(16);
}
