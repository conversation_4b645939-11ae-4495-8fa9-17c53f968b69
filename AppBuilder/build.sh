#!/bin/bash

# 地区配置编辑器构建脚本 (Qt6 Widgets版)
# Region Config Editor Build Script (Qt6 Widgets Version)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}===== $1 =====${NC}"
}

# 默认参数
BUILD_TYPE="Release"
CLEAN_BUILD=false
RUN_AFTER_BUILD=false
QT_PATH="/Users/<USER>/Desktop/qt-6.8.3/macos"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_DIR="$SCRIPT_DIR/build"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            echo "地区配置编辑器构建脚本 (Qt6 Widgets版)"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  -h, --help     显示此帮助信息"
            echo "  -c, --clean    清理构建目录"
            echo "  -r, --release  发布模式构建 (默认)"
            echo "  -d, --debug    调试模式构建"
            echo "  --run          构建完成后运行应用程序"
            echo "  --qt6-path=PATH 指定Qt6安装路径"
            echo ""
            exit 0
            ;;
        -c|--clean)
            CLEAN_BUILD=true
            shift
            ;;
        -r|--release)
            BUILD_TYPE="Release"
            shift
            ;;
        -d|--debug)
            BUILD_TYPE="Debug"
            shift
            ;;
        --run)
            RUN_AFTER_BUILD=true
            shift
            ;;
        --qt6-path=*)
            QT_PATH="${1#*=}"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            exit 1
            ;;
    esac
done

log_step "地区配置编辑器构建脚本 (Qt6 Widgets版)"
log_info "构建类型: $BUILD_TYPE"
log_info "Qt6路径: $QT_PATH"
log_info "构建目录: $BUILD_DIR"

# 检查Qt6安装
check_qt6() {
    log_step "检查Qt6安装"
    
    if [[ ! -d "$QT_PATH" ]]; then
        log_error "Qt6路径不存在: $QT_PATH"
        exit 1
    fi
    
    local qt_cmake="$QT_PATH/lib/cmake/Qt6/Qt6Config.cmake"
    if [[ ! -f "$qt_cmake" ]]; then
        log_error "Qt6 CMake配置文件不存在: $qt_cmake"
        exit 1
    fi
    
    log_info "Qt6安装检查通过"
}

# 检查依赖
check_dependencies() {
    log_step "检查构建依赖"
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        log_error "CMake未安装，请先安装CMake"
        exit 1
    fi
    
    local cmake_version=$(cmake --version | head -n1 | cut -d' ' -f3)
    log_info "CMake版本: $cmake_version"
    
    # 检查编译器
    if ! command -v clang++ &> /dev/null; then
        log_error "Clang++编译器未找到，请安装Xcode Command Line Tools"
        exit 1
    fi
    
    log_info "构建依赖检查通过"
}

# 清理构建目录
clean_build_dir() {
    if [[ "$CLEAN_BUILD" == true ]]; then
        log_step "清理构建目录"
        
        if [[ -d "$BUILD_DIR" ]]; then
            log_info "删除现有构建目录: $BUILD_DIR"
            rm -rf "$BUILD_DIR"
        fi
        
        log_info "构建目录清理完成"
    fi
}

# 配置项目
configure_project() {
    log_step "配置CMake项目"
    
    # 创建构建目录
    mkdir -p "$BUILD_DIR"
    cd "$BUILD_DIR"
    
    # 设置环境变量
    export QTFRAMEWORK_BYPASS_LICENSE_CHECK=1
    
    # 配置CMake
    log_info "运行CMake配置..."
    log_info "使用Qt6路径: $QT_PATH"
    cmake \
        -DCMAKE_BUILD_TYPE="$BUILD_TYPE" \
        -DQT6_PATH="$QT_PATH" \
        -DCMAKE_OSX_DEPLOYMENT_TARGET=12.0 \
        "$SCRIPT_DIR"
    
    log_info "CMake配置完成"
}

# 构建项目
build_project() {
    log_step "构建项目"

    cd "$BUILD_DIR"

    log_info "开始编译..."
    log_info "使用 $(sysctl -n hw.ncpu) 个并行任务"

    # 设置环境变量避免部署问题
    export QTFRAMEWORK_BYPASS_LICENSE_CHECK=1

    if make -j$(sysctl -n hw.ncpu); then
        log_info "编译成功完成"
    else
        log_error "编译失败"
        exit 1
    fi
}

# 部署Qt库到应用程序包
deploy_qt_libraries() {
    log_step "部署Qt库"

    cd "$BUILD_DIR"

    # 检查macdeployqt是否可用
    if command -v make >/dev/null 2>&1; then
        log_info "开始部署Qt库到应用程序包..."

        if make deploy; then
            log_info "Qt库部署成功"

            # 检查部署后的应用程序大小
            local app_size=$(du -sh RegionConfigEditor.app | cut -f1)
            log_info "部署后应用程序大小: $app_size"

            # 重新签名应用程序包以解决代码签名不一致问题
            log_info "重新签名应用程序包..."
            if codesign --force --deep --sign - RegionConfigEditor.app; then
                log_info "应用程序重新签名成功"
            else
                log_warn "应用程序重新签名失败，可能会出现启动问题"
            fi
        else
            log_warn "Qt库部署失败，应用程序可能需要Qt运行时环境"
        fi
    else
        log_warn "make命令不可用，跳过Qt库部署"
    fi
}

# 检查构建结果
check_build_result() {
    log_step "检查构建结果"
    
    local app_path="$BUILD_DIR/RegionConfigEditor.app"
    local executable_path="$app_path/Contents/MacOS/RegionConfigEditor"
    
    if [[ -d "$app_path" ]]; then
        log_info "应用程序包已生成: $app_path"
        
        if [[ -f "$executable_path" ]]; then
            log_info "可执行文件已生成: $executable_path"
            
            # 检查可执行文件权限
            if [[ -x "$executable_path" ]]; then
                log_info "可执行文件权限正确"
            else
                log_warn "可执行文件权限不正确，正在修复..."
                chmod +x "$executable_path"
            fi
        else
            log_error "可执行文件未生成"
            exit 1
        fi
    else
        log_error "应用程序包未生成"
        exit 1
    fi
    
    # 显示应用程序信息
    local app_size=$(du -sh "$app_path" | cut -f1)
    log_info "应用程序大小: $app_size"
    
    log_info "构建结果检查通过"
}

# 运行应用程序
run_application() {
    if [[ "$RUN_AFTER_BUILD" == true ]]; then
        log_step "运行应用程序"
        
        local app_path="$BUILD_DIR/RegionConfigEditor.app"
        
        log_info "启动应用程序: $app_path"
        open "$app_path"
        
        log_info "应用程序已启动"
    fi
}

# 显示构建总结
show_summary() {
    log_step "构建总结"
    
    local app_path="$BUILD_DIR/RegionConfigEditor.app"
    
    echo ""
    log_info "🎉 构建成功完成！"
    echo ""
    log_info "应用程序位置: $app_path"
    log_info "构建类型: $BUILD_TYPE"
    log_info "Qt6路径: $QT_PATH"
    echo ""
    log_info "运行应用程序:"
    echo "  open \"$app_path\""
    echo ""
    log_info "或者直接运行可执行文件:"
    echo "  \"$app_path/Contents/MacOS/RegionConfigEditor\""
    echo ""
    log_info "特性:"
    echo "  ✅ 纯Qt6 Widgets实现"
    echo "  ✅ 支持地区项目扫描"
    echo "  ✅ 配置文件编辑"
    echo "  ✅ 自动代码签名修复"
    echo ""
}

# 主执行流程
main() {
    # 检查环境
    check_qt6
    check_dependencies
    
    # 构建流程
    clean_build_dir
    configure_project
    build_project
    deploy_qt_libraries
    check_build_result
    
    # 后续操作
    run_application
    show_summary
}

# 错误处理
trap 'log_error "构建过程中发生错误，退出码: $?"' ERR

# 执行主流程
main
