import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: root
    color: "#f5f5f5"

    // 对外暴露的属性
    property alias searchText: searchField.text
    property alias regionCount: countLabel.text
    property var regionModel: ListModel {}
    property int selectedIndex: -1  // 添加选中索引属性

    // 信号定义
    signal regionSelected(string regionName)
    signal refreshRequested()
    signal searchTextUpdated(string text)
    signal openDirectoryRequested(string regionName)
    
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 10
        
        // 标题区域
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 40
            color: "transparent"
            
            Text {
                anchors.centerIn: parent
                text: "地区项目"
                font.pixelSize: 16
                font.bold: true
                color: "#333333"
            }
        }
        
        // 搜索框
        TextField {
            id: searchField
            Layout.fillWidth: true
            Layout.preferredHeight: 35
            placeholderText: "搜索地区项目..."
            selectByMouse: true
            
            background: Rectangle {
                color: "white"
                border.color: searchField.activeFocus ? "#4080ff" : "#d0d0d0"
                border.width: 1
                radius: 5
            }
            
            onTextChanged: {
                root.searchTextUpdated(text)
            }
        }
        
        // 计数标签
        Text {
            id: countLabel
            Layout.fillWidth: true
            text: "找到 0 个地区项目"
            font.pixelSize: 12
            color: "#666666"
        }
        
        // 地区列表
        ScrollView {
            Layout.fillWidth: true
            Layout.fillHeight: true
            
            ListView {
                id: regionListView
                model: root.regionModel
                spacing: 2
                
                delegate: RegionListItem {
                    width: regionListView.width
                    regionName: model.name || ""
                    regionDescription: model.description || ""
                    hasConfig: model.hasConfig || false
                    hasIcon: model.hasIcon || false
                    hasRelease: model.hasRelease || false
                    isSelected: index === root.selectedIndex  // 使用 root.selectedIndex 而不是 regionListView.currentIndex

                    onClicked: {
                        root.selectedIndex = index  // 更新 root.selectedIndex
                        regionListView.currentIndex = index
                        root.regionSelected(regionName)
                    }

                    onOpenDirectoryRequested: {
                        root.openDirectoryRequested(regionName)
                    }
                }
                
                highlight: Rectangle {
                    color: "#e6f3ff"
                    radius: 5
                    border.color: "#4080ff"
                    border.width: 1
                }
                
                highlightMoveDuration: 200
            }
        }
        
        // 刷新按钮
        Button {
            Layout.fillWidth: true
            Layout.preferredHeight: 35
            text: "刷新列表"
            
            background: Rectangle {
                color: parent.pressed ? "#3060d0" : (parent.hovered ? "#5090ff" : "#4080ff")
                radius: 5
                border.color: "#3060d0"
                border.width: 1
            }
            
            contentItem: Text {
                text: parent.text
                color: "white"
                font.pixelSize: 14
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
            }
            
            onClicked: {
                root.refreshRequested()
            }
        }
    }
    
    // 公共方法
    function updateRegionList(regions) {
        regionModel.clear()
        for (var i = 0; i < regions.length; i++) {
            regionModel.append(regions[i])
        }
        countLabel.text = "找到 " + regions.length + " 个地区项目"
    }
    
    function clearSelection() {
        root.selectedIndex = -1
        regionListView.currentIndex = -1
    }

    function selectRegion(regionName) {
        for (var i = 0; i < regionModel.count; i++) {
            if (regionModel.get(i).name === regionName) {
                root.selectedIndex = i
                regionListView.currentIndex = i
                break
            }
        }
    }
}
