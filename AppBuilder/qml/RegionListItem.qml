import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: root
    height: 80
    color: mouseArea.containsMouse ? "#f0f8ff" : "white"
    border.color: "#e0e0e0"
    border.width: 1
    radius: 5
    
    // 属性定义
    property string regionName: ""
    property string regionDescription: ""
    property bool hasConfig: false
    property bool hasIcon: false
    property bool hasRelease: false
    property bool isSelected: false  // 添加选中状态属性

    // 信号定义
    signal clicked()
    signal openDirectoryRequested()
    
    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        cursorShape: Qt.PointingHandCursor

        onClicked: {
            // 只有在点击的不是按钮区域时才触发
            root.clicked()
        }

        // 让按钮区域的点击事件优先处理
        propagateComposedEvents: true
    }
    
    RowLayout {
        anchors.fill: parent
        anchors.margins: 10
        spacing: 10
        
        // 左侧图标区域
        Rectangle {
            Layout.preferredWidth: 40
            Layout.preferredHeight: 40
            Layout.alignment: Qt.AlignVCenter
            color: "#4080ff"
            radius: 20
            
            Text {
                anchors.centerIn: parent
                text: regionName.length > 0 ? regionName.charAt(0).toUpperCase() : "?"
                color: "white"
                font.pixelSize: 18
                font.bold: true
            }
        }
        
        // 中间信息区域
        ColumnLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            spacing: 5
            
            // 地区名称
            Text {
                Layout.fillWidth: true
                text: regionName
                font.pixelSize: 14
                font.bold: true
                color: root.isSelected ? "#ff0000" : "#333333"  // 选中时显示红色，非选中时显示深灰色
                elide: Text.ElideRight
            }
            
            // 地区描述
            Text {
                Layout.fillWidth: true
                text: regionDescription
                font.pixelSize: 12
                color: "#666666"
                elide: Text.ElideRight
            }
        }

        // 右侧按钮区域
        RowLayout {
            Layout.minimumWidth: 80
            Layout.maximumWidth: 80
            Layout.fillHeight: true
            Layout.alignment: Qt.AlignVCenter
            spacing: 8

            // 打开目录按钮
            Rectangle {
                id: openDirButton
                Layout.preferredWidth: 36
                Layout.preferredHeight: 36
                Layout.alignment: Qt.AlignVCenter
                color: openDirButtonArea.containsMouse ? "#e3f2fd" : "#f8f9fa"
                border.color: openDirButtonArea.containsMouse ? "#2196f3" : "#dee2e6"
                border.width: 1
                radius: 6

                Text {
                    anchors.centerIn: parent
                    text: "📁"
                    font.pixelSize: 16
                    color: "#495057"
                }

                MouseArea {
                    id: openDirButtonArea
                    anchors.fill: parent
                    hoverEnabled: true
                    cursorShape: Qt.PointingHandCursor

                    onClicked: function(mouse) {
                        mouse.accepted = true  // 阻止事件传播
                        root.openDirectoryRequested()
                    }
                }

                // 工具提示
                ToolTip {
                    visible: openDirButtonArea.containsMouse
                    text: "打开配置文件夹"
                    delay: 500
                }
            }

            // 箭头
            Text {
                Layout.preferredWidth: 20
                Layout.preferredHeight: 36
                Layout.alignment: Qt.AlignVCenter
                text: "›"
                font.pixelSize: 20
                color: "#999999"
                horizontalAlignment: Text.AlignHCenter
                verticalAlignment: Text.AlignVCenter
                visible: true  // 确保箭头可见
            }
        }
    }
    
    // 状态指示器组件
    component StatusIndicator: Rectangle {
        property string label: ""
        property bool status: false
        
        width: statusText.implicitWidth + 8
        height: 18
        color: status ? "#e8f5e8" : "#f5f5f5"
        border.color: status ? "#4caf50" : "#cccccc"
        border.width: 1
        radius: 9
        
        Text {
            id: statusText
            anchors.centerIn: parent
            text: label
            font.pixelSize: 10
            color: parent.status ? "#2e7d32" : "#666666"
        }
    }
}
