#ifndef AESCRYPTO_H
#define AESCRYPTO_H

#include <QString>
#include <QByteArray>

/**
 * @brief AES128加密解密工具类
 * 
 * 实现与helper.go中相同的AES128加密解密功能
 * 使用ECB模式和PKCS7填充
 */
class AESCrypto
{
public:
    /**
     * @brief AES128加密
     * @param plainText 明文字符串
     * @param key 16字节密钥
     * @return 加密后的Base64编码字符串，失败返回空字符串
     */
    static QString aes128Encrypt(const QString &plainText, const QString &key);
    
    /**
     * @brief AES128解密
     * @param cipherText Base64编码的密文字符串
     * @param key 16字节密钥
     * @return 解密后的明文字符串，失败返回空字符串
     */
    static QString aes128Decrypt(const QString &cipherText, const QString &key);
    
    /**
     * @brief 加密启动器配置
     * @param info 配置信息JSON字符串
     * @param appKey 应用密钥（取后16位作为AES密钥）
     * @return 加密后的Base64编码字符串，失败返回空字符串
     */
    static QString encryptLauncherConfig(const QString &info, const QString &appKey);
    
    /**
     * @brief 解密启动器配置
     * @param encryptedData 加密的配置数据
     * @param appKey 应用密钥（取后16位作为AES密钥）
     * @return 解密后的JSON字符串，失败返回原始数据（兼容未加密的老版本）
     */
    static QString decryptLauncherConfig(const QString &encryptedData, const QString &appKey);

private:
    /**
     * @brief PKCS7填充
     * @param data 原始数据
     * @param blockSize 块大小（AES为16字节）
     * @return 填充后的数据
     */
    static QByteArray pkcs7Padding(const QByteArray &data, int blockSize);
    
    /**
     * @brief PKCS7去填充
     * @param data 填充后的数据
     * @return 去填充后的原始数据
     */
    static QByteArray pkcs7UnPadding(const QByteArray &data);
    
    /**
     * @brief 获取AppKey的后16位作为AES密钥
     * @param appKey 完整的AppKey
     * @return 16字节的AES密钥，如果AppKey长度不足16位返回空字符串
     */
    static QString getAESKey(const QString &appKey);
};

#endif // AESCRYPTO_H
