#include "regionlistwidget.h"
#include "regionconfigmanager.h"

#include <QVBoxLayout>
#include <QQmlEngine>
#include <QQmlContext>
#include <QQuickItem>
#include <QUrl>
#include <QTimer>
#include <QProcess>
#include <QDir>
#include <QMessageBox>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QListWidget>
#include <QPushButton>
#include <QListWidgetItem>
#include <QFont>

RegionListWidget::RegionListWidget(RegionConfigManager *configManager, QWidget *parent)
    : QWidget(parent)
    , m_configManager(configManager)
    , m_mainLayout(nullptr)
    , m_titleLabel(nullptr)
    , m_quickWidget(nullptr)
    , m_searchEdit(nullptr)
    , m_countLabel(nullptr)
    , m_regionList(nullptr)
    , m_refreshButton(nullptr)
    , m_useQuickView(true)  // 使用Qt Quick实现混合UI
{
    setupUI();
    setupConnections();
    updateRegionList();
}

void RegionListWidget::setupUI()
{
    setFixedWidth(500);

    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setSpacing(10);
    m_mainLayout->setContentsMargins(10, 10, 10, 10);

    if (m_useQuickView) {
        setupQuickView();
    } else {
        // 保留原有的Widget实现作为备选
        setupTraditionalView();
    }
}

void RegionListWidget::setupQuickView()
{
    // 创建Qt Quick Widget
    m_quickWidget = new QQuickWidget(this);
    m_quickWidget->setResizeMode(QQuickWidget::SizeRootObjectToView);

    // 设置QML上下文
    QQmlContext *context = m_quickWidget->rootContext();
    context->setContextProperty("regionListWidget", this);
    context->setContextProperty("configManager", m_configManager);

    // 加载QML文件
    QUrl qmlUrl("qrc:/qml/RegionListView.qml");
    qDebug() << "Loading QML from:" << qmlUrl;
    m_quickWidget->setSource(qmlUrl);

    // 检查加载状态
    if (m_quickWidget->status() == QQuickWidget::Error) {
        qWarning() << "Failed to load QML file:" << m_quickWidget->errors();
        // 如果QML加载失败，回退到传统Widget
        m_useQuickView = false;
        setupTraditionalView();
        return;
    }

    m_mainLayout->addWidget(m_quickWidget);

    // 延迟设置信号连接，确保QML完全加载
    QTimer::singleShot(100, this, [this]() {
        if (m_quickWidget && m_quickWidget->rootObject()) {
            QObject *rootObject = m_quickWidget->rootObject();
            connect(rootObject, SIGNAL(regionSelected(QString)),
                    this, SLOT(onQuickRegionSelected(QString)));
            connect(rootObject, SIGNAL(refreshRequested()),
                    this, SLOT(onQuickRefreshRequested()));
            connect(rootObject, SIGNAL(searchTextUpdated(QString)),
                    this, SLOT(onQuickSearchTextChanged(QString)));
            connect(rootObject, SIGNAL(openDirectoryRequested(QString)),
                    this, SLOT(onOpenDirectoryRequested(QString)));
            qDebug() << "Qt Quick signals connected successfully";

            // 初始化数据
            updateQuickModel();
        } else {
            qWarning() << "Qt Quick root object not available, falling back to traditional view";
            m_useQuickView = false;
            setupTraditionalView();
        }
    });
}

void RegionListWidget::setupTraditionalView()
{
    
    // 标题栏
    QHBoxLayout *titleLayout = new QHBoxLayout;
    
    m_titleLabel = new QLabel("地区项目");
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    titleLayout->addWidget(m_titleLabel);
    
    titleLayout->addStretch();
    
    m_refreshButton = new QPushButton("刷新");
    m_refreshButton->setMaximumWidth(60);
    titleLayout->addWidget(m_refreshButton);
    
    m_mainLayout->addLayout(titleLayout);
    
    // 搜索框
    m_searchEdit = new QLineEdit;
    m_searchEdit->setPlaceholderText("搜索地区项目...");
    m_mainLayout->addWidget(m_searchEdit);
    
    // 项目计数
    m_countLabel = new QLabel("0 个项目");
    m_countLabel->setStyleSheet("color: gray; font-size: 10px;");
    m_mainLayout->addWidget(m_countLabel);
    
    // 地区列表
    m_regionList = new QListWidget;
    m_regionList->setAlternatingRowColors(true);
    m_regionList->setSelectionMode(QAbstractItemView::SingleSelection);
    m_mainLayout->addWidget(m_regionList);
    
    // 设置样式
    setStyleSheet(
        "RegionListWidget {"
        "    background-color: #f5f5f5;"
        "    border: 1px solid #d0d0d0;"
        "}"
        "QListWidget {"
        "    background-color: white;"
        "    border: 1px solid #d0d0d0;"
        "    border-radius: 5px;"
        "}"
        "QListWidget::item {"
        "    padding: 8px;"
        "    border-bottom: 1px solid #e0e0e0;"
        "}"
        "QListWidget::item:selected {"
        "    background-color: #d0e0ff;"
        "    border: 2px solid #4080ff;"
        "}"
        "QListWidget::item:hover {"
        "    background-color: #e0e0e0;"
        "}"
    );
}

void RegionListWidget::setupConnections()
{
    if (m_useQuickView) {
        // Qt Quick版本的连接将在QML加载完成后设置
        // 这里暂时不设置，因为rootObject可能还没准备好
    } else {
        // 传统Widget版本的连接
        connect(m_searchEdit, &QLineEdit::textChanged,
                this, &RegionListWidget::onSearchTextChanged);
        connect(m_regionList, &QListWidget::itemClicked,
                this, &RegionListWidget::onRegionItemClicked);
        connect(m_refreshButton, &QPushButton::clicked,
                this, &RegionListWidget::onRefreshClicked);
    }

    // 监听配置管理器的信号
    connect(m_configManager, &RegionConfigManager::regionProjectsChanged,
            this, &RegionListWidget::updateRegionList);
    connect(m_configManager, &RegionConfigManager::currentRegionChanged,
            this, &RegionListWidget::updateRegionList);
}

void RegionListWidget::onSearchTextChanged(const QString &text)
{
    m_currentFilter = text;
    if (m_useQuickView) {
        updateQuickModel();
    } else {
        populateRegionList();
    }
}

void RegionListWidget::onRegionItemClicked(QListWidgetItem *item)
{
    if (item) {
        QString regionName = item->data(Qt::UserRole).toString();
        m_configManager->setCurrentRegion(regionName);
    }
}

void RegionListWidget::onRefreshClicked()
{
    m_configManager->scanRegionProjects();
}

void RegionListWidget::updateRegionList()
{
    m_allRegions = m_configManager->regionProjects();

    if (m_useQuickView) {
        updateQuickModel();
    } else {
        populateRegionList();
    }
}

void RegionListWidget::populateRegionList()
{
    // 如果使用Qt Quick视图，不需要操作传统的QListWidget
    if (m_useQuickView || !m_regionList) {
        return;
    }

    m_regionList->clear();
    
    QStringList filteredRegions;
    for (const QString &region : m_allRegions) {
        if (m_currentFilter.isEmpty() || 
            region.toLower().contains(m_currentFilter.toLower())) {
            filteredRegions.append(region);
        }
    }
    
    // 更新计数
    m_countLabel->setText(QString("%1 个项目").arg(filteredRegions.size()));
    
    // 添加项目到列表
    for (const QString &region : filteredRegions) {
        QListWidgetItem *item = new QListWidgetItem;
        
        // 设置显示文本
        QString displayText = region;
        QString description = getRegionDescription(region);
        if (!description.isEmpty()) {
            displayText += QString("\n\n%1").arg(description);
        }
    
        item->setText(displayText);
        item->setData(Qt::UserRole, region);
        
        // 高亮当前选中的地区
        if (region == m_configManager->currentRegion()) {
            item->setSelected(true);
            m_regionList->setCurrentItem(item);
        }
        
        m_regionList->addItem(item);
    }
    
    // 如果没有项目，显示提示
    if (filteredRegions.isEmpty()) {
        QListWidgetItem *emptyItem = new QListWidgetItem;
        if (m_allRegions.isEmpty()) {
            emptyItem->setText("未找到地区项目\n请检查Channels目录");
        } else {
            emptyItem->setText("没有匹配的项目");
        }
        emptyItem->setFlags(Qt::NoItemFlags); // 不可选择
        emptyItem->setTextAlignment(Qt::AlignCenter);
        m_regionList->addItem(emptyItem);
    }
}

QString RegionListWidget::getRegionDescription(const QString &regionName) const
{
    // 根据地区名称生成描述
    QString description;
    if (regionName.contains("ZXSJ", Qt::CaseInsensitive)) {
        description = "诛仙世界";
    } else if (regionName.contains("Demo", Qt::CaseInsensitive)) {
        description = "Demo";
    }

    if (regionName.contains("BenchMark", Qt::CaseInsensitive)) {
        description += "-性能测试";
    }

    if (regionName.contains("TW", Qt::CaseInsensitive)) {
        description += "-港澳台";
    }

    if (regionName.contains("OB", Qt::CaseInsensitive)) {
        description += "-公测版本";
    } else if (regionName.contains("DEV", Qt::CaseInsensitive)) {
         description += "-开发版本";
    } else if (regionName.contains("TEST", Qt::CaseInsensitive)) {
         description += "-测试版本";
    } else if (regionName.contains("OPS", Qt::CaseInsensitive)) {
         description += "-运行版本";
    }
    return description;
}



void RegionListWidget::updateQuickModel()
{
    if (!m_quickWidget) return;

    QObject *rootObject = m_quickWidget->rootObject();
    if (!rootObject) return;

    // 准备地区数据
    QVariantList regionData;
    for (const QString &region : m_allRegions) {
        if (!m_currentFilter.isEmpty() &&
            !region.contains(m_currentFilter, Qt::CaseInsensitive)) {
            continue;
        }

        QVariantMap regionInfo;
        regionInfo["name"] = region;
        regionInfo["description"] = getRegionDescription(region);
        regionInfo["hasConfig"] = m_configManager->fileExists("Channels/" + region + "/RegionConfig.plist");
        regionInfo["hasIcon"] = m_configManager->fileExists("Channels/" + region + "/icon_1024x1024.png");
        regionInfo["hasRelease"] = QDir(m_configManager->getAbsolutePath("Channels/" + region + "/Release")).exists();

        regionData.append(regionInfo);
    }

    // 调用QML方法更新模型
    QMetaObject::invokeMethod(rootObject, "updateRegionList",
                              Q_ARG(QVariant, QVariant::fromValue(regionData)));
}

void RegionListWidget::onQuickRegionSelected(const QString &regionName)
{
    qDebug() << "Qt Quick region selected:" << regionName;
    m_configManager->setCurrentRegion(regionName);
}

void RegionListWidget::onQuickRefreshRequested()
{
    qDebug() << "Qt Quick refresh requested";
    m_configManager->scanRegionProjects();
}

void RegionListWidget::onQuickSearchTextChanged(const QString &text)
{
    qDebug() << "Qt Quick search text changed:" << text;
    m_currentFilter = text;
    updateQuickModel();
}

void RegionListWidget::onOpenDirectoryRequested(const QString &regionName)
{
    qDebug() << "Open directory requested for region:" << regionName;

    if (regionName.isEmpty() || !m_configManager) {
        return;
    }

    // 构建地区配置文件夹路径
    QString regionPath = m_configManager->getAbsolutePath("Channels/" + regionName);

    // 检查路径是否存在
    if (!QDir(regionPath).exists()) {
        QMessageBox::warning(this, "错误",
            QString("地区配置文件夹不存在：\n%1").arg(regionPath));
        return;
    }

    // 使用系统默认文件管理器打开目录
#ifdef Q_OS_MAC
    QProcess::startDetached("open", QStringList() << regionPath);
#elif defined(Q_OS_WIN)
    QProcess::startDetached("explorer", QStringList() << QDir::toNativeSeparators(regionPath));
#else
    QProcess::startDetached("xdg-open", QStringList() << regionPath);
#endif
}
