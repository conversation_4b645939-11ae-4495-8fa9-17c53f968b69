#ifndef REGIONLISTWIDGET_H
#define REGIONLISTWIDGET_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QListWidget>
#include <QPushButton>
#include <QListWidgetItem>
#include <QQuickWidget>
#include <QQmlContext>
#include <QQmlEngine>

class RegionConfigManager;

class RegionListWidget : public QWidget
{
    Q_OBJECT

public:
    explicit RegionListWidget(RegionConfigManager *configManager, QWidget *parent = nullptr);

private slots:
    void onSearchTextChanged(const QString &text);
    void onRegionItemClicked(QListWidgetItem *item);
    void onRefreshClicked();
    void updateRegionList();

private:
    void setupUI();
    void setupConnections();
    void populateRegionList();
    QString getRegionDescription(const QString &regionName) const;

    // Qt Quick相关方法
    void setupQuickView();
    void setupTraditionalView();
    void updateQuickModel();
    Q_INVOKABLE void onQuickRegionSelected(const QString &regionName);
    Q_INVOKABLE void onQuickRefreshRequested();
    Q_INVOKABLE void onQuickSearchTextChanged(const QString &text);
    Q_INVOKABLE void onOpenDirectoryRequested(const QString &regionName);

private:
    RegionConfigManager *m_configManager;

    QVBoxLayout *m_mainLayout;
    QLabel *m_titleLabel;
    QQuickWidget *m_quickWidget;  // Qt Quick组件

    // 保留传统Widget组件作为备选
    QLineEdit *m_searchEdit;
    QLabel *m_countLabel;
    QListWidget *m_regionList;
    QPushButton *m_refreshButton;

    QStringList m_allRegions;
    QString m_currentFilter;
    bool m_useQuickView;  // 控制使用Qt Quick还是传统Widget
};

#endif // REGIONLISTWIDGET_H
