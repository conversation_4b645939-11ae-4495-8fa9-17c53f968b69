#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QDebug>

#include "mainwindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("Region Config Editor");
    app.setApplicationDisplayName("地区配置编辑器");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Wanmei");
    app.setOrganizationDomain("com.wanmei");
    
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置应用程序图标（如果有的话）
    // app.setWindowIcon(QIcon(":/icons/app_icon.png"));
    
    // 创建主窗口
    MainWindow window;
    window.show();
    
    qDebug() << "地区配置编辑器已启动";
    qDebug() << "当前工作目录:" << QDir::currentPath();
    
    return app.exec();
}
