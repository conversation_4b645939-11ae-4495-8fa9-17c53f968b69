# 构建系统完整使用文档

## 概述

本文档详细介绍了模板化构建系统的两个核心脚本的使用方法：
- `build-region-launcher.sh` - 单个地区项目构建脚本
- `batch-build-all-regions.sh` - 批量构建所有地区项目脚本

## 1. build-region-launcher.sh - 单个项目构建脚本

### 基本语法
```bash
./Scripts/build-region-launcher.sh -p <project_dir> [选项]
```

### 命令行参数

#### 必需参数
- `-p <project_dir>` - 地区工程目录名

#### 可选参数
- `-o <output_dir>` - 输出目录（默认：./build/Archive）
- `-clean` - 构建完成后清理临时文件
- `-j` - Jenkins 环境打包
- `-u` - 生成升级 appcast.xml 文件
- `-n` - 对 DMG 进行公证
- `-a` - 根据 appcast.xml 自动升级 app 版本
- `-b` - 根据 appcast.xml 自动升级 build 版本
- `--prepare-only` - **新功能**：仅准备模式，只执行配置和依赖安装

### 使用示例

#### 基本构建
```bash
# 构建 Demo_Dev 项目
./Scripts/build-region-launcher.sh -p Demo_Dev -clean

# 构建 ZXSJ_DEV 项目并生成升级文件
./Scripts/build-region-launcher.sh -p ZXSJ_DEV -clean -u

# 构建并公证
./Scripts/build-region-launcher.sh -p ZXSJ_TW_DEV -clean -u -n
```

#### 仅准备模式（新功能）
```bash
# 仅执行配置和依赖安装，不进行实际构建
./Scripts/build-region-launcher.sh -p Demo_Dev --prepare-only

# 适用场景：
# - 开发调试：快速准备开发环境
# - 问题排查：检查配置是否正确
# - 手动构建：准备好环境后手动操作
```

#### 高级用法
```bash
# 自定义输出目录
./Scripts/build-region-launcher.sh -p ZXSJ_OB -o ./custom_output -clean

# Jenkins 环境构建
./Scripts/build-region-launcher.sh -p ZXSJ_DEV -j -clean -u

# 自动版本升级
./Scripts/build-region-launcher.sh -p Demo_Dev -clean -u -a -b
```

### 构建流程

#### 正常构建模式
1. ✅ 清理 build 目录
2. ✅ 创建临时工程
3. ✅ 更新 Podfile 配置
4. ✅ 更新 Info.plist 配置（包含 getAppKey 默认值修改）
5. ✅ 更新项目配置
6. ✅ 复制 Release 资源文件
7. ✅ 处理应用图标
8. ✅ 更新描述文件配置
9. ✅ 安装 CocoaPods 依赖
10. ✅ 执行 Xcode 构建
11. ✅ 创建 DMG 文件
12. ✅ 生成升级配置（如果指定 -u）
13. ✅ 公证处理（如果指定 -n）
14. ✅ 清理临时文件（如果指定 -clean）

#### 仅准备模式（--prepare-only）
1. ✅ 清理 build 目录
2. ✅ 创建临时工程
3. ✅ 更新 Podfile 配置
4. ✅ 更新 Info.plist 配置
5. ✅ 更新项目配置
6. ✅ 复制 Release 资源文件
7. ✅ 处理应用图标
8. ✅ 更新描述文件配置
9. ✅ 安装 CocoaPods 依赖
10. ⏹️ **停止**：保留临时工程，输出路径供后续使用

### 输出结果

#### 正常构建输出
```
<project_dir>/Archive/
├── <app_name>.dmg           # 应用安装包
├── appcast.xml              # 升级配置文件（如果使用 -u）
└── <app_name>.app/          # 应用程序包
```

#### 仅准备模式输出
```
build/temp_<project>_<timestamp>/
├── Launcher.xcodeproj       # 配置好的 Xcode 项目
├── Podfile                  # 更新后的 Podfile
├── Pods/                    # 安装的依赖
└── 其他配置文件...
```

## 2. batch-build-all-regions.sh - 批量构建脚本

### 基本语法
```bash
./Scripts/batch-build-all-regions.sh [选项]
```

### 命令行参数
- `-h, --help` - 显示帮助信息
- `--dry-run` - 仅显示将要构建的项目，不执行实际构建
- `--no-upgrade` - 不生成升级 appcast.xml 文件
- `--with-notarize` - 对 DMG 进行公证（需要配置公证环境）

### 使用示例

#### 预览构建计划
```bash
# 查看将要构建的项目列表
./Scripts/batch-build-all-regions.sh --dry-run
```

#### 批量构建
```bash
# 标准批量构建（推荐）
./Scripts/batch-build-all-regions.sh

# 批量构建但不生成升级文件
./Scripts/batch-build-all-regions.sh --no-upgrade

# 批量构建并公证所有项目
./Scripts/batch-build-all-regions.sh --with-notarize
```

### 自动扫描功能

脚本会自动扫描项目根目录下所有包含 `RegionConfig.plist` 的目录：

```
当前扫描到的项目：
1. Demo_Dev                    # 用户重命名后的项目
2. ZXSJ_BenchMark_DEV
3. ZXSJ_BenchMark_OB
4. ZXSJ_BenchMark_TW
5. ZXSJ_BenchMark_TW_DEV
6. ZXSJ_DEV
7. ZXSJ_OB
8. ZXSJ_TW_DEV
9. ZXSJ_TW_OB
10. ZXSJ_TW_OPS
```

### 日志管理

#### 日志文件位置
```
batch_build_logs/
├── Demo_Dev_build_20250626_122823.log
├── ZXSJ_DEV_build_20250626_122924.log
├── ZXSJ_TW_DEV_build_20250626_123025.log
└── ...
```

#### 查看日志
```bash
# 查看所有日志文件
ls -la batch_build_logs/

# 查看特定项目的最新日志
tail -f batch_build_logs/Demo_Dev_build_*.log

# 查看失败项目的错误信息
grep -i error batch_build_logs/ZXSJ_DEV_build_*.log
```

### 构建统计报告

批量构建完成后会显示详细统计：

```
===== 批量构建完成 =====
[信息] 构建统计:
[信息]   总项目数: 10
[信息]   成功构建: 8
[信息]   失败构建: 2
[信息]   总耗时: 1200秒 (20分钟)

[信息] ✅ 成功构建的项目:
[信息]   - Demo_Dev
[信息]     归档目录: /path/to/Demo_Dev/Archive
[信息]     DMG 文件: 1 个

[错误] ❌ 失败构建的项目:
[错误]   - ZXSJ_DEV
[错误]     日志文件: batch_build_logs/ZXSJ_DEV_build_20250626_123025.log
```

## 3. 支持的项目目录

### 当前支持的地区项目
- `Demo_Dev` - 演示开发版（原 LauncherDemo）
- `ZXSJ_DEV` - 大陆开发版
- `ZXSJ_OB` - 大陆正式版
- `ZXSJ_TW_DEV` - 台湾开发版
- `ZXSJ_TW_OB` - 台湾正式版
- `ZXSJ_TW_OPS` - 台湾运营版
- `ZXSJ_BenchMark_DEV` - 基准测试开发版
- `ZXSJ_BenchMark_OB` - 基准测试正式版
- `ZXSJ_BenchMark_TW` - 基准测试台湾版
- `ZXSJ_BenchMark_TW_DEV` - 基准测试台湾开发版

### 项目目录要求
每个地区项目目录必须包含：
- `RegionConfig.plist` - 地区配置文件
- 其他必要的资源文件（图标、配置等）

## 4. 常见使用场景

### 开发调试场景
```bash
# 1. 快速准备开发环境
./Scripts/build-region-launcher.sh -p Demo_Dev --prepare-only

# 2. 进入临时工程目录进行开发
cd build/temp_Demo_Dev_1234567890
open Launcher.xcodeproj

# 3. 手动构建和调试
```

### 日常构建场景
```bash
# 1. 构建单个项目
./Scripts/build-region-launcher.sh -p Demo_Dev -clean -u

# 2. 批量构建所有项目
./Scripts/batch-build-all-regions.sh

# 3. 查看构建结果
ls -la */Archive/
```

### 发布场景
```bash
# 1. 批量构建并公证
./Scripts/batch-build-all-regions.sh --with-notarize

# 2. 检查构建结果
./Scripts/batch-build-all-regions.sh --dry-run

# 3. 上传发布包
# 手动上传各项目的 Archive 目录中的文件
```

## 5. 故障排除

### 常见问题

#### 1. 构建失败
```bash
# 查看详细错误日志
tail -50 batch_build_logs/<project>_build_*.log

# 检查项目配置
./Scripts/build-region-launcher.sh -p <project> --prepare-only
```

#### 2. 依赖问题
```bash
# 清理并重新安装依赖
rm -rf build/
./Scripts/build-region-launcher.sh -p <project> --prepare-only
```

#### 3. 公证失败
```bash
# 检查公证配置
# 确保设置了正确的 Apple ID 和应用专用密码
```

### 日志分析
```bash
# 查看所有失败的构建
grep -l "构建失败\|error\|Error" batch_build_logs/*.log

# 统计构建时间
grep "耗时" batch_build_logs/*.log
```

## 6. 最佳实践

### 构建前检查
1. 确保所有地区项目的 `RegionConfig.plist` 文件存在且配置正确
2. 检查网络连接（CocoaPods 依赖下载）
3. 确保有足够的磁盘空间

### 批量构建建议
1. 使用 `--dry-run` 预览构建计划
2. 在非工作时间进行批量构建
3. 定期清理 `batch_build_logs` 目录

### 开发调试建议
1. 使用 `--prepare-only` 快速准备环境
2. 保留临时工程用于问题排查
3. 定期清理 `build` 目录

## 7. 更新日志

### 最新改进
- ✅ 添加 `--prepare-only` 参数支持仅准备模式
- ✅ 创建批量构建脚本 `batch-build-all-regions.sh`
- ✅ 修复 getAppKey 默认值处理
- ✅ 适配 Demo_Dev 项目名称变更
- ✅ 改进日志管理，使用独立的 `batch_build_logs` 目录

### 技术改进
- 🔧 优化临时工程保留机制
- 🔧 增强错误处理和日志输出
- 🔧 改进批量构建的并发安全性
- 🔧 统一中文日志输出格式

---

**注意事项**：
- 所有路径都相对于项目根目录
- 构建过程中请勿中断，可能导致临时文件残留
- 公证功能需要配置 Apple 开发者账号信息
- 建议在构建前备份重要数据
