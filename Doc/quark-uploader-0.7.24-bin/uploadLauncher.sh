#!/bin/bash -l
# 用于上传Launcher.dmg最新版本到dev后台macupdate分支

if [ $# -lt 1 ]; then
  echo "Usage: uploadLauncher.sh [folderPath]"
  echo "eg: uploadLauncher.sh ./Release"
  exit 1
fi


UPLOAD_PATH=$1
SCRIPT_PATH=$(realpath "$0")
SCRIPT_DIR=$(dirname "$SCRIPT_PATH")

${SCRIPT_DIR}/quark-uploader-darwin-arm64 resource upload --appId 1000000 --branchName macupdate  --versionType 1  \
  --folderPath "${UPLOAD_PATH}" --userName anonymous --uid "1000000" --token "5f98d13589c74e8bbbb59caedeea29bc"
    

