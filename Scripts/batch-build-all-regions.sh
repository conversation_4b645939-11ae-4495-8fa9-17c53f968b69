#!/bin/bash

# 批量打包所有地区项目脚本
# 自动扫描并构建所有存在 RegionConfig.plist 的地区项目

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
cNone='\033[00m'
cRed='\033[00;31m'
cGreen='\033[00;32m'
cLightGreen='\033[01;32m'
cYellow='\033[00;33m'
cCyan='\033[00;36m'
cBlue='\033[00;34m'

log_info() {
    echo -e "${cLightGreen}[信息]${cNone} $1"
}

log_warn() {
    echo -e "${cYellow}[警告]${cNone} $1"
}

log_error() {
    echo -e "${cRed}[错误]${cNone} $1"
}

log_step() {
    echo -e "${cCyan}===== $1 =====${cNone}"
}

log_progress() {
    echo -e "${cBlue}[进度]${cNone} $1"
}

# 显示帮助信息
usage() {
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  -h, --help       显示帮助信息"
    echo "  --dry-run        仅显示将要构建的项目，不执行实际构建"
    echo "  --no-upgrade     不生成升级 appcast.xml 文件"
    echo "  --with-notarize  对 DMG 进行公证（需要配置公证环境）"
    echo "  --projects <项目列表>  仅构建指定的项目（逗号分隔或空格分隔）"
    echo
    echo "说明:"
    echo "  此脚本会自动扫描 Channels/ 目录下所有包含 RegionConfig.plist 的地区项目"
    echo "  对每个项目执行构建命令，生成 DMG 和升级配置文件"
    echo "  构建日志保存在 batch_build_logs/ 目录下"
    echo
    echo "示例:"
    echo "  $0                                    # 构建所有地区项目"
    echo "  $0 --dry-run                          # 仅显示将要构建的项目"
    echo "  $0 --with-notarize                    # 构建并公证所有项目"
    echo "  $0 --projects \"Demo_Dev,ZXSJ_DEV\"      # 仅构建指定项目（逗号分隔）"
    echo "  $0 --projects Demo_Dev ZXSJ_TW_OB     # 仅构建指定项目（空格分隔）"
    exit 1
}

# 初始化变量
DRY_RUN="0"
WITH_UPGRADE="1"
WITH_NOTARIZE="0"
FILTER_PROJECTS=""
SPECIFIED_PROJECTS=()

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            usage
            ;;
        --dry-run)
            DRY_RUN="1"
            ;;
        --no-upgrade)
            WITH_UPGRADE="0"
            ;;
        --with-notarize)
            WITH_NOTARIZE="1"
            ;;
        --projects)
            shift
            if [[ $# -eq 0 ]]; then
                echo "错误: --projects 参数需要指定项目列表"
                usage
            fi
            FILTER_PROJECTS="$1"
            ;;
        *)
            # 如果前一个参数是 --projects，则当前参数可能是项目名
            if [[ -n "$FILTER_PROJECTS" && "$1" != --* ]]; then
                FILTER_PROJECTS="$FILTER_PROJECTS $1"
            else
                echo "未知参数: $1"
                usage
            fi
            ;;
    esac
    shift
done

echo "===== 批量构建所有地区项目 ====="
echo "项目根目录: $PROJECT_ROOT"
echo "脚本目录: $SCRIPT_DIR"

# 扫描所有地区项目目录（新的 Channels 目录结构）
log_step "扫描地区项目目录"

CHANNELS_DIR="$PROJECT_ROOT/Channels"
if [[ ! -d "$CHANNELS_DIR" ]]; then
    log_error "Channels 目录不存在: $CHANNELS_DIR"
    log_error "请确认项目已重新组织到 Channels/ 目录下"
    exit 1
fi

log_info "扫描目录: $CHANNELS_DIR"

REGION_PROJECTS=()
for dir in "$CHANNELS_DIR"/*; do
    if [[ -d "$dir" && -f "$dir/RegionConfig.plist" ]]; then
        dir_name=$(basename "$dir")
        # 排除一些特殊目录（如果有的话）
        if [[ "$dir_name" != "LauncherMaster" && "$dir_name" != "LauncherSDK" ]]; then
            REGION_PROJECTS+=("$dir_name")
            log_info "发现地区项目: $dir_name"
        fi
    fi
done

if [[ ${#REGION_PROJECTS[@]} -eq 0 ]]; then
    log_error "未找到任何地区项目目录"
    log_error "请确保项目目录包含 RegionConfig.plist 文件"
    exit 1
fi

log_info "总共发现 ${#REGION_PROJECTS[@]} 个地区项目"

# 处理项目过滤
if [[ -n "$FILTER_PROJECTS" ]]; then
    log_step "处理项目过滤"

    # 解析项目列表（支持逗号分隔和空格分隔）
    IFS=', ' read -ra SPECIFIED_PROJECTS <<< "$FILTER_PROJECTS"

    log_info "指定的项目列表: ${SPECIFIED_PROJECTS[*]}"

    # 验证指定的项目是否存在
    FILTERED_PROJECTS=()
    for specified_project in "${SPECIFIED_PROJECTS[@]}"; do
        # 去除空白字符
        specified_project=$(echo "$specified_project" | xargs)

        if [[ -z "$specified_project" ]]; then
            continue
        fi

        # 检查项目是否在扫描到的项目列表中
        found=0
        for region_project in "${REGION_PROJECTS[@]}"; do
            if [[ "$region_project" == "$specified_project" ]]; then
                found=1
                break
            fi
        done

        if [[ $found -eq 1 ]]; then
            # 再次验证项目目录和配置文件（新的 Channels 目录结构）
            if [[ -d "$PROJECT_ROOT/Channels/$specified_project" && -f "$PROJECT_ROOT/Channels/$specified_project/RegionConfig.plist" ]]; then
                FILTERED_PROJECTS+=("$specified_project")
                log_info "✅ 验证通过: $specified_project"
            else
                log_error "❌ 项目目录或配置文件不存在: Channels/$specified_project"
                exit 1
            fi
        else
            log_error "❌ 指定的项目不在可用项目列表中: $specified_project"
            log_error "可用项目: ${REGION_PROJECTS[*]}"
            exit 1
        fi
    done

    if [[ ${#FILTERED_PROJECTS[@]} -eq 0 ]]; then
        log_error "没有有效的项目可以构建"
        exit 1
    fi

    # 使用过滤后的项目列表
    REGION_PROJECTS=("${FILTERED_PROJECTS[@]}")
    log_info "过滤后的项目数量: ${#REGION_PROJECTS[@]}"
fi

# 显示构建计划
log_step "构建计划"

BUILD_COMMAND_BASE="./Scripts/build-region-launcher.sh"
BUILD_ARGS="-clean"

if [[ "$WITH_UPGRADE" == "1" ]]; then
    BUILD_ARGS="$BUILD_ARGS -u"
    log_info "✅ 将生成升级 appcast.xml 文件"
else
    log_info "⚠️  跳过升级 appcast.xml 文件生成"
fi

if [[ "$WITH_NOTARIZE" == "1" ]]; then
    BUILD_ARGS="$BUILD_ARGS -n"
    log_info "✅ 将对 DMG 进行公证"
else
    log_info "⚠️  跳过 DMG 公证"
fi

log_info "构建命令模板: $BUILD_COMMAND_BASE -p <项目名> $BUILD_ARGS"

echo
log_info "将要构建的项目列表:"
for i in "${!REGION_PROJECTS[@]}"; do
    project="${REGION_PROJECTS[$i]}"
    echo "  $((i+1)). $project"
done

if [[ "$DRY_RUN" == "1" ]]; then
    log_step "🔍 仅预览模式"
    log_info "以上是将要构建的项目列表"
    log_info "使用 $0 执行实际构建"
    exit 0
fi

# 创建日志目录（使用独立的日志目录，避免被构建脚本清理）
LOG_DIR="$PROJECT_ROOT/batch_build_logs"
mkdir -p "$LOG_DIR"
log_info "构建日志目录: $LOG_DIR"

# 构建统计
TOTAL_PROJECTS=${#REGION_PROJECTS[@]}
SUCCESSFUL_BUILDS=()
FAILED_BUILDS=()
BUILD_START_TIME=$(date +%s)

log_step "开始批量构建"

for i in "${!REGION_PROJECTS[@]}"; do
    project="${REGION_PROJECTS[$i]}"
    current_num=$((i+1))
    
    log_progress "[$current_num/$TOTAL_PROJECTS] 开始构建项目: $project"
    
    # 生成日志文件名
    timestamp=$(date +%Y%m%d_%H%M%S)
    log_file="$LOG_DIR/${project}_build_${timestamp}.log"
    
    # 构建命令
    build_cmd="$BUILD_COMMAND_BASE -p $project $BUILD_ARGS"
    
    log_info "执行命令: $build_cmd"
    log_info "日志文件: $log_file"
    
    # 记录构建开始时间
    project_start_time=$(date +%s)
    
    # 确保日志目录存在（防止被构建脚本清理）
    mkdir -p "$LOG_DIR"

    # 执行构建
    if $build_cmd > "$log_file" 2>&1; then
        project_end_time=$(date +%s)
        project_duration=$((project_end_time - project_start_time))
        
        SUCCESSFUL_BUILDS+=("$project")
        log_progress "✅ [$current_num/$TOTAL_PROJECTS] $project 构建成功 (耗时: ${project_duration}秒)"
    else
        project_end_time=$(date +%s)
        project_duration=$((project_end_time - project_start_time))
        
        FAILED_BUILDS+=("$project")
        log_progress "❌ [$current_num/$TOTAL_PROJECTS] $project 构建失败 (耗时: ${project_duration}秒)"
        log_error "详细错误信息请查看: $log_file"
        
        # 显示最后几行错误信息
        if [[ -f "$log_file" ]]; then
            log_error "最后的错误信息:"
            tail -5 "$log_file" | while read line; do
                log_error "  $line"
            done
        else
            log_error "日志文件未生成，可能是构建脚本启动失败"
        fi
    fi
    
    echo
done

# 构建完成统计
BUILD_END_TIME=$(date +%s)
TOTAL_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))

log_step "批量构建完成"

log_info "构建统计:"
log_info "  总项目数: $TOTAL_PROJECTS"
log_info "  成功构建: ${#SUCCESSFUL_BUILDS[@]}"
log_info "  失败构建: ${#FAILED_BUILDS[@]}"
log_info "  总耗时: ${TOTAL_DURATION}秒 ($((TOTAL_DURATION/60))分钟)"

if [[ ${#SUCCESSFUL_BUILDS[@]} -gt 0 ]]; then
    echo
    log_info "✅ 成功构建的项目:"
    for project in "${SUCCESSFUL_BUILDS[@]}"; do
        log_info "  - $project"
        
        # 显示构建结果路径
        archive_dir="$PROJECT_ROOT/Channels/$project/Archive"
        if [[ -d "$archive_dir" ]]; then
            log_info "    归档目录: $archive_dir"
            dmg_files=$(find "$archive_dir" -name "*.dmg" -type f 2>/dev/null | wc -l)
            if [[ $dmg_files -gt 0 ]]; then
                log_info "    DMG 文件: $dmg_files 个"
            fi
        fi
    done
fi

if [[ ${#FAILED_BUILDS[@]} -gt 0 ]]; then
    echo
    log_error "❌ 失败构建的项目:"
    for project in "${FAILED_BUILDS[@]}"; do
        log_error "  - $project"
        
        # 显示对应的日志文件
        if [[ -d "$LOG_DIR" ]]; then
            latest_log=$(find "$LOG_DIR" -name "${project}_build_*.log" -type f 2>/dev/null | sort | tail -1)
            if [[ -n "$latest_log" ]]; then
                log_error "    日志文件: $latest_log"
            else
                log_error "    日志文件: 未生成"
            fi
        else
            log_error "    日志目录: 不存在"
        fi
    done
    
    echo
    log_error "请检查失败项目的日志文件以获取详细错误信息"
fi

echo
if [[ ${#FAILED_BUILDS[@]} -eq 0 ]]; then
    log_step "🎉 所有项目构建成功！"
    exit 0
else
    log_step "⚠️  部分项目构建失败"
    log_warn "成功率: $((${#SUCCESSFUL_BUILDS[@]} * 100 / TOTAL_PROJECTS))%"
    exit 1
fi
