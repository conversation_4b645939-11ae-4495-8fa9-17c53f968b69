#!/bin/bash
# macos.app公证脚本

# 显示帮助信息的函数
usage() {
    echo "Usage: $0 [-d value] [-o value] [-s value] [-k value]"
    echo
    echo "Options:"
    echo "  -d    dmg Path"
    echo "  -o    Output Path"
    echo "  -s    Code Sign Identity"
    echo "  -k    keychain profile"
    exit 1
}

EXPORT_PATH=$(pwd)
while getopts 'd:s:k:o:' OPT; do
    case $OPT in
        d) DMG_PATH=$OPTARG;;
        o) EXPORT_PATH=$(realpath "$OPTARG");;
        s) CODE_SIGN_IDENTITY=$OPTARG;;
        k) KEYCHAIN_PROFILE=$OPTARG;;
        ?) usage;;
    esac
done

# 对DMG做签名
codesign -f -s "${CODE_SIGN_IDENTITY}" "${DMG_PATH}" --options runtime

# Xcode doesn't show run script errors in build log.
#NOTARIZATION_LOG="$EXPORT_PATH/NotarizationSubmit.log"
#exec > "$NOTARIZATION_LOG" 2>&1

echo "----------begin notarytool ---------------"
echo $(date +"%Y-%m-%d %T")
# Submit the finished deliverables for notarization.
# Wait up to 2 hours for a response.
# Use verbose logging in order to file feedback if an error occurs.
NOTARIZATION_RESPONSE="${EXPORT_PATH}/NotarizationResponse.plist"
# 将秘钥存到keychan
#xcrun notarytool store-credentials "notarytool-password"  --apple-id "<EMAIL>"  --team-id 29C5EDQ3MS --password xxxxxx
xcrun notarytool submit --keychain-profile ${KEYCHAIN_PROFILE}  --verbose "$DMG_PATH" --wait --timeout 2h --output-format plist > "$NOTARIZATION_RESPONSE"

return_code=$?

if [ $return_code -eq 0 ]; then
	message=`/usr/libexec/PlistBuddy -c "Print :message" "$NOTARIZATION_RESPONSE"`
	status=`/usr/libexec/PlistBuddy -c "Print :status" "$NOTARIZATION_RESPONSE"`

	# 对app进行stapler将票证附加到.dmg
	xcrun stapler staple "$DMG_PATH"

	# 查看公证状态
	spctl -vvv --assess --type install "$DMG_PATH"
     
    echo "notarytool result success status: (${status}), message: (${message})"

    echo "----------finish notarytool success---------------"
else
	# 获取公证认证过程日志
    SUBMISSION_ID=$(/usr/libexec/PlistBuddy -c "Print :id" "$NOTARIZATION_RESPONSE" 2>/dev/null)
    NOTARIZATION_RESULT="$EXPORT_PATH/NotarizationResult.json"
#xcrun notarytool log 38D6D86D-4554-4077-969C-057C37797FFD  --keychain-profile "notarytool-password" NotarizationResult.json
    xcrun notarytool log "$SUBMISSION_ID" --keychain-profile "$KEYCHAIN_PROFILE" "$NOTARIZATION_RESULT"
    cat "$NOTARIZATION_RESULT"
    
    echo "----------finish notarytool failure---------------"
    exit 1
fi


