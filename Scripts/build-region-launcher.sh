#!/bin/bash -l

# 模板化构建脚本 - 基于配置文件构建不同地区的启动器
#
# 功能：
# 1. 基于 LauncherMaster 创建临时工程
# 2. 根据 RegionConfig.plist 动态替换配置
# 3. 处理应用图标和资源文件
# 4. 执行完整的打包流程

set -e  # 遇到错误立即退出

# 颜色定义
cNone='\033[00m'
cRed='\033[00;31m'
cGreen='\033[00;32m'
cLightGreen='\033[01;32m'
cYellow='\033[00;33m'
cCyan='\033[00;36m'
cDarkGray='\033[00;90m'
cWhite='\033[00;97m'

# 日志函数
log_info() {
    echo -e "${cLightGreen}[INFO]${cNone} $1"
}

log_warn() {
    echo -e "${cYellow}[WARN]${cNone} $1"
}

log_error() {
    echo -e "${cRed}[ERROR]${cNone} $1"
}

log_step() {
    echo -e "${cCyan}===== $1 =====${cNone}"
}

# 显示帮助信息
usage() {
    echo "Usage: $0 -p <project_dir> [-o output_dir] [-clean] [-j] [-u] [-n] [-a] [-b] [--prepare-only]"
    echo
    echo "Options:"
    echo "  -p    地区工程目录名 (例如: ZXSJ_DEV, ZXSJ_TW_DEV, Demo_Dev) - 位于 Channels/ 目录下"
    echo "  -o    输出目录 (可选，默认为 ./build/Archive)"
    echo "  -clean 构建完成后清理临时文件"
    echo "  -j    Jenkins 环境打包"
    echo "  -u    生成升级 appcast.xml 文件"
    echo "  -n    对 dmg 进行公证"
    echo "  -a    根据 appcast.xml 自动升级 app 版本"
    echo "  -b    根据 appcast.xml 自动升级 build 版本"
    echo "  --prepare-only 仅准备模式，只执行配置和依赖安装，不进行实际构建"
    echo
    echo "示例:"
    echo "  $0 -p ZXSJ_DEV -clean -u -n"
    echo "  $0 -p ZXSJ_TW_DEV -o ./custom_output"
    echo "  $0 -p Demo_Dev -clean"
    echo "  $0 -p ZXSJ_DEV --prepare-only"
    echo "  $0 -c Channels/ZXSJ_DEV/RegionConfig.plist -clean"
    exit 1
}

# 初始化变量
CONFIG_FILE=""
PROJECT_DIR=""
OUTPUT_DIR="./build/Archive"
CLEAN_TEMP="0"
JENKINS="0"
UPGRADE="0"
NOTARIZE="0"
UPGRADE_APP_VERSION="0"
UPGRADE_BUILD_VERSION="0"
PREPARE_ONLY="0"
TEMP_PROJECT_DIR=""

# 解析命令行参数
while getopts 'p:c:o:junab-:' OPT; do
    case $OPT in
        p) PROJECT_DIR=$OPTARG;;
        c) CONFIG_FILE=$OPTARG;;
        o) OUTPUT_DIR=$OPTARG;;
        j) JENKINS="1";;
        u) UPGRADE="1";;
        n) NOTARIZE="1";;
        a) UPGRADE_APP_VERSION="1";;
        b) UPGRADE_BUILD_VERSION="1";;
        -)
            case $OPTARG in
                clean) CLEAN_TEMP="1";;
                prepare-only) PREPARE_ONLY="1";;
                *) log_error "Unknown option --$OPTARG"; usage;;
            esac
            ;;
        ?) usage;;
    esac
done

# 获取脚本目录和项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 验证参数并确定配置文件路径
if [[ -n "$PROJECT_DIR" ]]; then
    # 使用项目目录方式（新的 Channels 目录结构）
    CHANNEL_PROJECT_PATH="$PROJECT_ROOT/Channels/$PROJECT_DIR"
    if [[ ! -d "$CHANNEL_PROJECT_PATH" ]]; then
        log_error "项目目录不存在: $CHANNEL_PROJECT_PATH"
        log_error "请确认项目已移动到 Channels/ 目录下"
        exit 1
    fi
    CONFIG_FILE="$CHANNEL_PROJECT_PATH/RegionConfig.plist"
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_error "项目目录下没有 RegionConfig.plist 文件: $CONFIG_FILE"
        log_error "请先运行 ./Scripts/extract-region-configs.sh 生成配置文件"
        exit 1
    fi
    log_info "使用项目目录: Channels/$PROJECT_DIR"
    log_info "配置文件: $CONFIG_FILE"
else
    log_error "必须指定项目目录 (-p 参数) "
    usage
fi

log_step "模板化构建启动器 v2.0"
if [[ -n "$PROJECT_DIR" ]]; then
    log_info "项目目录: $PROJECT_DIR"
fi
log_info "配置文件: $CONFIG_FILE"
log_info "输出目录: $OUTPUT_DIR"
log_info "项目根目录: $PROJECT_ROOT"

# 验证必要的模版工程目录
if [[ ! -d "$PROJECT_ROOT/LauncherMaster" ]]; then
    log_error "LauncherMaster 模板目录不存在: $PROJECT_ROOT/LauncherMaster"
    exit 1
fi

# 配置读取函数
read_plist_value() {
    local key="$1"
    /usr/libexec/PlistBuddy -c "Print :$key" "$CONFIG_FILE" 2>/dev/null || echo ""
}

read_plist_dict_value() {
    local dict="$1"
    local key="$2"
    /usr/libexec/PlistBuddy -c "Print :$dict:$key" "$CONFIG_FILE" 2>/dev/null || echo ""
}

read_plist_array_value() {
    local dict="$1"
    local key="$2"
    local index="$3"
    /usr/libexec/PlistBuddy -c "Print :$dict:$key:$index" "$CONFIG_FILE" 2>/dev/null || echo ""
}

# 验证配置文件格式
validate_config_file() {
    log_step "验证配置文件"

    if ! plutil -lint "$CONFIG_FILE" >/dev/null 2>&1; then
        log_error "配置文件格式错误: $CONFIG_FILE"
        exit 1
    fi

    # 检查必需的配置节
    local required_sections=("InfoPlistConfig" "PodfileConfig" "ProjectConfig")
    for section in "${required_sections[@]}"; do
        if ! /usr/libexec/PlistBuddy -c "Print :$section" "$CONFIG_FILE" >/dev/null 2>&1; then
            log_error "配置文件缺少必需的配置节: $section"
            exit 1
        fi
    done

    log_info "配置文件验证通过"
}

# 验证配置文件
validate_config_file

# 读取配置信息
read_config() {
    log_step "读取配置信息"

    # 从 InfoPlistConfig 读取
    BUNDLE_ID=$(read_plist_dict_value "InfoPlistConfig" "CFBundleIdentifier")
    PRODUCT_NAME=$(read_plist_dict_value "InfoPlistConfig" "CFBundleName")
    DISPLAY_NAME=$(read_plist_dict_value "InfoPlistConfig" "CFBundleDisplayName")
    VERSION=$(read_plist_dict_value "InfoPlistConfig" "CFBundleShortVersionString")
    BUILD_NUMBER=$(read_plist_dict_value "InfoPlistConfig" "CFBundleVersion")
    SPARKLE_FEED_URL=$(read_plist_dict_value "InfoPlistConfig" "SUFeedURL")
    DEVELOPMENT_REGION=$(read_plist_dict_value "InfoPlistConfig" "CFBundleDevelopmentRegion")

    # 从 PodfileConfig 读取
    SCENE_ENV=$(read_plist_dict_value "PodfileConfig" "SCENCE_ENV")
    PACKAGE_NAME=$(read_plist_dict_value "PodfileConfig" "PACKAGE_NAME")
    LAUNCHER_MACRO_ENV=$(read_plist_dict_value "PodfileConfig" "LAUNCHER_MACRO_ENV")
    AREA_ENV=$(read_plist_dict_value "PodfileConfig" "AREA_ENV")

    # 从 ProjectConfig 读取
    PROJECT_DEVELOPMENT_REGION=$(read_plist_dict_value "ProjectConfig" "developmentRegion")

    # 读取 APP_KEY 配置
    APP_KEY=$(read_plist_dict_value "InfoPlistConfig" "APP_KEY")

    # 设置默认值
    [[ -z "$DISPLAY_NAME" ]] && DISPLAY_NAME="$PRODUCT_NAME"
    [[ -z "$BUILD_NUMBER" ]] && BUILD_NUMBER="1"
    [[ -z "$DEVELOPMENT_REGION" ]] && DEVELOPMENT_REGION="zh-Hans"
    [[ -z "$PROJECT_DEVELOPMENT_REGION" ]] && PROJECT_DEVELOPMENT_REGION="$DEVELOPMENT_REGION"
    [[ -z "$APP_KEY" ]] && APP_KEY="ggeaj0zcfv5fno50cnbap6wmwoi0flfs"

    log_info "Bundle ID: $BUNDLE_ID"
    log_info "产品名称: $PRODUCT_NAME"
    log_info "显示名称: $DISPLAY_NAME"
    log_info "版本: $VERSION"
    log_info "构建号: $BUILD_NUMBER"
    log_info "环境: $SCENE_ENV"
    log_info "包名: $PACKAGE_NAME"
    log_info "启动器宏: $LAUNCHER_MACRO_ENV"
    log_info "地区环境: $AREA_ENV"
    log_info "开发地区: $DEVELOPMENT_REGION"
    log_info "应用密钥: $APP_KEY"

    # 验证必要配置
    if [[ -z "$BUNDLE_ID" || -z "$PRODUCT_NAME" || -z "$PACKAGE_NAME" ]]; then
        log_error "配置文件中缺少必要信息"
        exit 1
    fi
}

read_config

# 清理 build 目录
clean_build_directory() {
    log_step "清理构建目录"

    local build_dir="$PROJECT_ROOT/build"

    if [[ -d "$build_dir" ]]; then
        log_info "发现现有构建目录: $build_dir"
        log_info "开始清理构建目录..."

        # 首先尝试修改权限，确保可以删除
        log_info "修改目录权限以确保可删除性..."
        chmod -R 755 "$build_dir" 2>/dev/null || true

        # 尝试普通删除
        log_info "尝试普通删除方式..."
        if rm -rf "$build_dir" 2>/dev/null; then
            log_info "✅ 构建目录删除成功"
        else
            log_warn "普通删除失败，尝试强制删除..."

            # 强制删除所有文件
            find "$build_dir" -type f -exec rm -f {} \; 2>/dev/null || true

            # 删除所有空目录
            find "$build_dir" -type d -empty -delete 2>/dev/null || true

            # 最后尝试删除根目录
            if rmdir "$build_dir" 2>/dev/null; then
                log_info "✅ 强制删除成功"
            else
                log_warn "⚠️  无法完全删除构建目录，尝试重命名..."
                local backup_dir="${build_dir}_backup_$(date +%s)"
                if mv "$build_dir" "$backup_dir" 2>/dev/null; then
                    log_info "已将旧构建目录重命名为: $backup_dir"
                    log_warn "请手动清理备份目录: $backup_dir"
                else
                    log_error "❌ 无法清理构建目录，请检查文件权限或进程占用"
                    log_error "请手动删除目录: $build_dir"
                    exit 1
                fi
            fi
        fi
    else
        log_info "构建目录不存在，无需清理"
    fi

    log_info "✅ 构建目录清理完成"
}

# 创建临时工程目录
create_temp_project() {
    log_step "创建临时工程目录"

    TEMP_PROJECT_DIR="$PROJECT_ROOT/build/${PROJECT_DIR}_$(date +%s)"
    log_info "临时目录: $TEMP_PROJECT_DIR"

    # 创建 build 目录
    mkdir -p "$PROJECT_ROOT/build"

    # 复制 LauncherMaster 到临时目录
    log_info "复制 LauncherMaster 模板..."
    cp -R "$PROJECT_ROOT/LauncherMaster" "$TEMP_PROJECT_DIR"

    log_info "临时工程创建完成"
}

# 项目重命名逻辑已移除，保持 LauncherMaster 的原始项目结构

# 清理函数
cleanup() {
    if [[ "$PREPARE_ONLY" == "1" ]]; then
        log_info "仅准备模式：保留临时工程目录 $TEMP_PROJECT_DIR"
        return 0
    fi

    if [[ "$CLEAN_TEMP" == "1" && -n "$TEMP_PROJECT_DIR" && -d "$TEMP_PROJECT_DIR" ]]; then
        log_step "清理临时文件"
        rm -rf "$TEMP_PROJECT_DIR"
        log_info "临时文件清理完成"
    fi
}

# 设置退出时清理
trap cleanup EXIT

# 清理 build 目录并创建临时工程
clean_build_directory
create_temp_project

# 更新 Podfile
update_podfile() {
    log_step "更新 Podfile"

    cd "$TEMP_PROJECT_DIR"

    log_info "生成新的 Podfile..."

    cat > Podfile << EOF
source 'http://gitlab.sys.wanmei.com/iOS/sdk-dev-cocoapods-specs.git'
source 'https://cdn.cocoapods.org/'

use_frameworks! :linkage => :static
platform :macos, '12.0'

# archiveApp.sh打包时会获取环境(dev、test)和包名字段
ENV['SCENCE_ENV'] = '$SCENE_ENV'
ENV['PACKAGE_NAME'] = '$PACKAGE_NAME'
EOF

    # 添加可选的环境变量
    if [[ -n "$LAUNCHER_MACRO_ENV" ]]; then
        echo "ENV['LAUNCHER_MACRO_ENV'] = '$LAUNCHER_MACRO_ENV'" >> Podfile
        log_info "添加 LAUNCHER_MACRO_ENV: $LAUNCHER_MACRO_ENV"
    fi

    if [[ -n "$AREA_ENV" ]]; then
        echo "ENV['AREA_ENV'] = '$AREA_ENV'" >> Podfile
        log_info "添加 AREA_ENV: $AREA_ENV"
    fi

    # 添加 target 配置
    cat >> Podfile << EOF

target 'Launcher' do
  pod 'WMLauncherUI', path: '../../LauncherSDK/'
  pod 'WMLauncherCore', path: '../../LauncherSDK/'
  pod 'WMLauncherDependency', path: '../../LauncherSDK/'
  pod 'LauncherResource', path: './'

  # Apply settings for Apple Silicon only
  post_install do |installer|
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['ARCHS'] = 'arm64'
        config.build_settings['EXCLUDED_ARCHS[sdk=macosx*]'] = 'x86_64'
      end
    end
  end
end
EOF

    log_info "Podfile 更新完成"
}

# 更新 Info.plist
update_info_plist() {
    log_step "更新 Info.plist"

    cd "$TEMP_PROJECT_DIR"

    # 确定 Info.plist 路径（保持原始的 Launcher 目录结构）
    local info_plist="Launcher/Info.plist"

    if [[ ! -f "$info_plist" ]]; then
        log_error "Info.plist 文件不存在: $info_plist"
        exit 1
    fi

    log_info "使用 PlistBuddy 逐项更新 Info.plist: $info_plist"

    # 备份原文件
    cp "$info_plist" "$info_plist.backup"

    # 使用 PlistBuddy 逐项更新可变配置
    update_plist_key() {
        local key="$1"
        local value="$2"
        local type="$3"  # string, bool, etc.

        if /usr/libexec/PlistBuddy -c "Print :$key" "$info_plist" >/dev/null 2>&1; then
            # 键存在，更新值
            /usr/libexec/PlistBuddy -c "Set :$key $value" "$info_plist"
            log_info "更新 $key = $value"
        else
            # 键不存在，添加新键
            /usr/libexec/PlistBuddy -c "Add :$key $type $value" "$info_plist"
            log_info "添加 $key = $value"
        fi
    }

    # 更新可变配置项
    if [[ -n "$DISPLAY_NAME" ]]; then
        update_plist_key "CFBundleDisplayName" "$DISPLAY_NAME" "string"
    fi

    if [[ -n "$SPARKLE_FEED_URL" ]]; then
        update_plist_key "SUFeedURL" "$SPARKLE_FEED_URL" "string"
    fi

    if [[ -n "$DEVELOPMENT_REGION" ]]; then
        update_plist_key "CFBundleDevelopmentRegion" "$DEVELOPMENT_REGION" "string"
    fi

    if [[ -n "$APP_KEY" ]]; then
        update_plist_key "APP_KEY" "$APP_KEY" "string"
        log_info "应用密钥已设置到 Info.plist"
    fi

    # 验证 Info.plist 格式
    if plutil -lint "$info_plist" >/dev/null 2>&1; then
        log_info "Info.plist 更新完成，保留了原始固定配置"
    else
        log_error "Info.plist 格式错误，恢复备份文件"
        mv "$info_plist.backup" "$info_plist"
        exit 1
    fi

    # 修改 main.swift 中的 getAppKey 默认值
    modify_main_swift_appkey_default
}

# 修改 main.swift 中的 getAppKey 默认值
modify_main_swift_appkey_default() {
    log_step "修改 main.swift 中的 getAppKey 默认值"

    cd "$TEMP_PROJECT_DIR"

    local main_swift_file="Launcher/main.swift"

    if [[ ! -f "$main_swift_file" ]]; then
        log_error "main.swift 文件不存在: $main_swift_file"
        exit 1
    fi

    log_info "开始修改 main.swift 文件: $main_swift_file"

    # 备份原文件
    cp "$main_swift_file" "$main_swift_file.backup"

    # 使用 sed 将默认值从硬编码字符串改为空字符串
    log_info "将 getAppKey() 方法的默认返回值修改为空字符串"

    # 查找并替换默认值
    if sed -i '' 's/return "ggeaj0zcfv5fno50cnbap6wmwoi0flfs"/return ""/' "$main_swift_file"; then
        log_info "✅ main.swift 默认值修改成功"

        # 验证修改结果
        if grep -q 'return ""' "$main_swift_file"; then
            log_info "✅ 验证成功：getAppKey() 默认值已改为空字符串"
        else
            log_warn "⚠️  验证警告：未找到预期的空字符串返回值"
        fi

        # 显示修改后的相关代码行
        log_info "修改后的 getAppKey() 方法片段："
        grep -A 3 -B 3 'return ""' "$main_swift_file" || log_warn "无法显示修改后的代码片段"

    else
        log_error "❌ main.swift 默认值修改失败"
        log_error "恢复备份文件..."
        mv "$main_swift_file.backup" "$main_swift_file"
        exit 1
    fi

    log_info "main.swift 文件修改完成"
}

# 更新项目文件配置
update_project_config() {
    log_step "更新项目文件配置"

    cd "$TEMP_PROJECT_DIR"

    # 确定项目文件路径（保持原始的 Launcher 项目结构）
    local project_file="Launcher.xcodeproj/project.pbxproj"

    if [[ ! -f "$project_file" ]]; then
        log_error "项目文件不存在: $project_file"
        exit 1
    fi

    log_info "更新项目文件: $project_file"

    # 备份原文件
    cp "$project_file" "$project_file.backup"

    # 更新 developmentRegion
    sed -i '' "s/developmentRegion = \"[^\"]*\"/developmentRegion = \"$PROJECT_DEVELOPMENT_REGION\"/" "$project_file"
    log_info "更新 developmentRegion: $PROJECT_DEVELOPMENT_REGION"

    # 更新 PRODUCT_BUNDLE_IDENTIFIER
    sed -i '' "s/PRODUCT_BUNDLE_IDENTIFIER = [^;]*/PRODUCT_BUNDLE_IDENTIFIER = $BUNDLE_ID/" "$project_file"
    log_info "更新 PRODUCT_BUNDLE_IDENTIFIER: $BUNDLE_ID"

    # 更新 PRODUCT_NAME
    sed -i '' "s/PRODUCT_NAME = \"[^\"]*\"/PRODUCT_NAME = \"$PRODUCT_NAME\"/" "$project_file"
    log_info "更新 PRODUCT_NAME: $PRODUCT_NAME"

    # 更新 MARKETING_VERSION
    sed -i '' "s/MARKETING_VERSION = [^;]*/MARKETING_VERSION = $VERSION/" "$project_file"
    log_info "更新 MARKETING_VERSION: $VERSION"

    # 更新 CURRENT_PROJECT_VERSION
    sed -i '' "s/CURRENT_PROJECT_VERSION = [^;]*/CURRENT_PROJECT_VERSION = $BUILD_NUMBER/" "$project_file"
    log_info "更新 CURRENT_PROJECT_VERSION: $BUILD_NUMBER"

    # 验证关键配置是否更新成功
    if grep -q "PRODUCT_BUNDLE_IDENTIFIER = $BUNDLE_ID" "$project_file" && \
       grep -q "PRODUCT_NAME = \"$PRODUCT_NAME\"" "$project_file" && \
       grep -q "MARKETING_VERSION = $VERSION" "$project_file"; then
        log_info "项目配置更新完成"
    else
        log_error "项目配置更新失败"
        exit 1
    fi
}

# 复制 Release 资源文件
copy_release_resources() {
    log_step "复制 Release 资源文件"

    cd "$TEMP_PROJECT_DIR"

    # 确定源 Release 目录
    local source_release_dir="$PROJECT_ROOT/Channels/$PROJECT_DIR/Release"

    if [[ -d "$source_release_dir" ]]; then
        log_info "从项目目录复制 Release 资源: $source_release_dir"

        # 确保目标 Release 目录存在
        mkdir -p "./Release"

        # 复制所有资源文件
        cp -R "$source_release_dir/"* "./Release/"

        log_info "Release 资源复制完成"

        # 显示复制的文件
        log_info "复制的资源文件:"
        ls -la "./Release/" | head -10
    else
        log_warn "项目 Release 目录不存在: $source_release_dir"
        log_warn "将使用默认的 Release 配置"
    fi
}

# 处理应用图标
process_app_icon() {
    log_step "处理应用图标"

    cd "$TEMP_PROJECT_DIR"

    # 查找源图标文件
    local source_icon_path=""

    # 优先使用项目目录下的图标
    if [[ -f "$PROJECT_ROOT/Channels/$PROJECT_DIR/icon_1024x1024.png" ]]; then
        source_icon_path="$PROJECT_ROOT/Channels/$PROJECT_DIR/icon_1024x1024.png"
        log_info "找到项目图标: $source_icon_path"
    else
        log_warn "未找到项目图标文件: $PROJECT_ROOT/Channels/$PROJECT_DIR/icon_1024x1024.png"
        return 0
    fi

    # 生成 AppIcon.appiconset（保持原始的 Launcher 目录结构）
    local appicon_dir="Launcher/Assets.xcassets/AppIcon.appiconset"
    mkdir -p "$appicon_dir"

    log_info "生成应用图标集: $appicon_dir"

    # 生成不同尺寸的图标
    generate_icon() {
        local size=$1
        local scale=$2
        local filename=$3
        local pixel_size=$((size * scale))

        if sips -z $pixel_size $pixel_size "$source_icon_path" --out "$appicon_dir/$filename" >/dev/null 2>&1; then
            log_info "生成图标: $filename (${pixel_size}x${pixel_size})"
        else
            log_warn "生成图标失败: $filename"
        fi
    }

    # 生成各种尺寸的图标
    generate_icon 16 1 "icon_16x16.png"
    generate_icon 16 2 "<EMAIL>"
    generate_icon 32 1 "icon_32x32.png"
    generate_icon 32 2 "<EMAIL>"
    generate_icon 128 1 "icon_128x128.png"
    generate_icon 128 2 "<EMAIL>"
    generate_icon 256 1 "icon_256x256.png"
    generate_icon 256 2 "<EMAIL>"
    generate_icon 512 1 "icon_512x512.png"
    generate_icon 512 2 "<EMAIL>"

    # 生成 Contents.json
    cat > "$appicon_dir/Contents.json" << 'EOF'
{
  "images" : [
    {
      "filename" : "icon_16x16.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "16x16"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "16x16"
    },
    {
      "filename" : "icon_32x32.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "32x32"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "32x32"
    },
    {
      "filename" : "icon_128x128.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "128x128"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "128x128"
    },
    {
      "filename" : "icon_256x256.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "256x256"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "256x256"
    },
    {
      "filename" : "icon_512x512.png",
      "idiom" : "mac",
      "scale" : "1x",
      "size" : "512x512"
    },
    {
      "filename" : "<EMAIL>",
      "idiom" : "mac",
      "scale" : "2x",
      "size" : "512x512"
    }
  ],
  "info" : {
    "author" : "xcode",
    "version" : 1
  }
}
EOF

    # 生成 DMG 图标
    if command -v iconutil >/dev/null 2>&1; then
        if iconutil -c icns "$appicon_dir" -o "Release/dmg-icon.icns" 2>/dev/null; then
            log_info "生成 DMG 图标: Release/dmg-icon.icns"
        else
            log_warn "生成 DMG 图标失败"
        fi
    else
        log_warn "iconutil 工具不可用，跳过 DMG 图标生成"
    fi

    log_info "应用图标处理完成"
}

# 处理描述文件配置
update_provisioning_profile() {
    log_step "处理描述文件配置"

    cd "$TEMP_PROJECT_DIR"

    # 读取对应地区工程的 ExportOptions.plist
    local source_export_options="$PROJECT_ROOT/Channels/$PROJECT_DIR/Release/ExportOptions.plist"

    if [[ ! -f "$source_export_options" ]]; then
        log_warn "源 ExportOptions.plist 不存在: $source_export_options"
        log_warn "跳过描述文件配置"
        return 0
    fi

    log_info "读取描述文件配置: $source_export_options"

    # 从 ExportOptions.plist 中获取描述文件名
    local provisioning_profile=""
    if /usr/libexec/PlistBuddy -c "Print :provisioningProfiles:$BUNDLE_ID" "$source_export_options" >/dev/null 2>&1; then
        provisioning_profile=$(/usr/libexec/PlistBuddy -c "Print :provisioningProfiles:$BUNDLE_ID" "$source_export_options")
        log_info "找到描述文件: $provisioning_profile (Bundle ID: $BUNDLE_ID)"
    else
        log_warn "未找到 Bundle ID $BUNDLE_ID 对应的描述文件配置"
        return 0
    fi

    if [[ -z "$provisioning_profile" ]]; then
        log_warn "描述文件名为空，跳过配置"
        return 0
    fi

    # 更新项目文件中的描述文件配置
    local project_file="Launcher.xcodeproj/project.pbxproj"

    if [[ ! -f "$project_file" ]]; then
        log_error "项目文件不存在: $project_file"
        exit 1
    fi

    log_info "更新项目文件中的描述文件配置: $project_file"

    # 备份项目文件
    cp "$project_file" "$project_file.provisioning.backup"

    # 更新 PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*] 配置
    # 确保 provisioning profile 名称被正确引用（处理包含空格的情况）
    sed -i '' "s/\"PROVISIONING_PROFILE_SPECIFIER\[sdk=macosx\*\]\" = [^;]*/\"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]\" = \"$provisioning_profile\"/" "$project_file"

    # 验证更新结果
    if grep -q "\"PROVISIONING_PROFILE_SPECIFIER\[sdk=macosx\*\]\" = \"$provisioning_profile\"" "$project_file"; then
        log_info "描述文件配置更新成功: $provisioning_profile"
    else
        log_warn "描述文件配置更新可能失败，请检查项目文件"

        # 显示实际的配置行用于调试
        log_info "当前配置行:"
        grep "PROVISIONING_PROFILE_SPECIFIER\[sdk=macosx\*\]" "$project_file" || log_warn "未找到配置行"
    fi

    # 处理 DEVELOPMENT_TEAM 配置
    log_info "开始处理开发团队配置..."

    # 从 ExportOptions.plist 中读取 teamID
    local team_id=""
    if /usr/libexec/PlistBuddy -c "Print :teamID" "$source_export_options" >/dev/null 2>&1; then
        team_id=$(/usr/libexec/PlistBuddy -c "Print :teamID" "$source_export_options")
        log_info "找到开发团队 ID: $team_id"
    else
        log_warn "未找到开发团队 ID 配置，跳过 DEVELOPMENT_TEAM 设置"
        return 0
    fi

    if [[ -z "$team_id" ]]; then
        log_warn "开发团队 ID 为空，跳过 DEVELOPMENT_TEAM 设置"
        return 0
    fi

    # 更新项目文件中的 DEVELOPMENT_TEAM 配置
    log_info "更新项目文件中的开发团队配置..."

    # 备份项目文件（如果还没有备份）
    if [[ ! -f "$project_file.team.backup" ]]; then
        cp "$project_file" "$project_file.team.backup"
    fi

    # 更新 DEVELOPMENT_TEAM[sdk=macosx*] 配置
    sed -i '' "s/\"DEVELOPMENT_TEAM\[sdk=macosx\*\]\" = [^;]*/\"DEVELOPMENT_TEAM[sdk=macosx*]\" = $team_id/" "$project_file"

    # 验证更新结果
    if grep -q "\"DEVELOPMENT_TEAM\[sdk=macosx\*\]\" = $team_id" "$project_file"; then
        log_info "开发团队配置更新成功: $team_id"
    else
        log_warn "开发团队配置更新可能失败，请检查项目文件"
    fi
}

# 安装 CocoaPods 依赖
install_cocoapods_dependencies() {
    log_step "安装 CocoaPods 依赖"

    cd "$TEMP_PROJECT_DIR"

    # 检查 Podfile 是否存在
    if [[ ! -f "Podfile" ]]; then
        log_error "Podfile 不存在，无法安装依赖"
        exit 1
    fi

    # 检查 pod 命令是否可用
    if ! command -v pod >/dev/null 2>&1; then
        log_error "CocoaPods 未安装，请先安装 CocoaPods"
        log_error "安装命令: sudo gem install cocoapods"
        exit 1
    fi

    log_info "开始安装 CocoaPods 依赖..."
    log_info "当前目录: $(pwd)"
    log_info "执行命令: pod install --no-repo-update"

    # 执行 pod install
    if pod install --no-repo-update; then
        log_info "CocoaPods 依赖安装成功"
    else
        log_error "CocoaPods 依赖安装失败"
        log_error "请检查 Podfile 配置和网络连接"
        exit 1
    fi
}

# 加载更新配置文件
load_update_appcast_xml() {
    log_step "加载升级配置文件"

    cd "$TEMP_PROJECT_DIR"

    local archive_appcast="./build/Archive/appcast.xml"
    local cache_appcast="./Release/appcast.xml"

    # 获取 SUFeedURL
    local appcast_xml_url=""
    if [[ -f "Launcher/Info.plist" ]]; then
        appcast_xml_url=$(/usr/libexec/PlistBuddy -c "Print :SUFeedURL" "Launcher/Info.plist" 2>/dev/null || echo "")
    fi

    # 如果缓存文件存在，优先使用缓存的 appcast.xml
    if [[ -f "$cache_appcast" ]]; then
        log_info "检测到本地缓存 appcast.xml，复制到构建目录"
        cp "$cache_appcast" "$archive_appcast"
        log_info "✅ 本地缓存 appcast.xml 加载成功"
        return 0
    fi

    # 如果没有缓存文件且有 URL，尝试下载
    if [[ -n "$appcast_xml_url" ]]; then
        log_info "尝试从远程下载 appcast.xml: $appcast_xml_url"

        # 确保目标目录存在
        mkdir -p "$(dirname "$archive_appcast")"

        # 使用 curl 下载
        local http_status=$(curl -s -S -f -o "$archive_appcast" -w "%{http_code}" "$appcast_xml_url" 2>/dev/null || echo "000")

        if [[ "$http_status" == "200" ]]; then
            log_info "✅ appcast.xml 下载成功，状态码: $http_status"
            return 0
        else
            log_warn "⚠️  appcast.xml 下载失败，状态码: $http_status"
            log_warn "URL: $appcast_xml_url"
            # 删除不完整文件
            rm -f "$archive_appcast"
        fi
    else
        log_warn "⚠️  未找到 SUFeedURL 配置，无法下载 appcast.xml"
    fi

    log_warn "⚠️  无法加载 appcast.xml 文件，版本自动升级功能将跳过"
    return 1
}

# 自动升级版本号
auto_upgrade_version() {
    if [[ "$UPGRADE_APP_VERSION" == "1" || "$UPGRADE_BUILD_VERSION" == "1" ]]; then
        log_step "根据 appcast.xml 自动升级版本号"

        cd "$TEMP_PROJECT_DIR"

        # 获取当前版本信息
        local project_file="Launcher.xcodeproj/project.pbxproj"
        local app_version=$(grep "MARKETING_VERSION = " "$project_file" | head -1 | sed -E "s/.*MARKETING_VERSION = ([^;]*).*/\1/")
        local build_number=$(grep "CURRENT_PROJECT_VERSION = " "$project_file" | head -1 | sed -E "s/.*CURRENT_PROJECT_VERSION = ([^;]*).*/\1/")

        log_info "当前应用版本: $app_version"
        log_info "当前构建版本: $build_number"

        # 加载 appcast.xml 文件
        if ! load_update_appcast_xml; then
            log_warn "⚠️  无法加载 appcast.xml，跳过版本自动升级"
            return 0
        fi

        # 检查 appcast.xml 文件
        local archive_appcast="./build/Archive/appcast.xml"
        if [[ -f "$archive_appcast" ]]; then
            log_info "找到 appcast.xml 文件，开始解析版本信息"

            if [[ "$UPGRADE_BUILD_VERSION" == "1" ]]; then
                log_info "自动升级构建版本号..."
                local remote_build_version=$(sed -n 's/.*<sparkle:version>\([^<]*\).*/\1/p' "$archive_appcast" | head -1)
                if [[ -n "$remote_build_version" ]]; then
                    build_number=$(echo "$remote_build_version" | awk '{print $1 + 1}')
                    sed -i '' "s/\(CURRENT_PROJECT_VERSION = \)[^;]*/\1$build_number/" "$project_file"
                    log_info "构建版本号已更新为: $build_number"
                fi
            fi

            if [[ "$UPGRADE_APP_VERSION" == "1" ]]; then
                log_info "自动升级应用版本号..."
                local remote_short_version=$(sed -n 's/.*<sparkle:shortVersionString>\([^<]*\).*/\1/p' "$archive_appcast" | head -1)
                if [[ -n "$remote_short_version" ]]; then
                    app_version=$(echo "$remote_short_version" | awk -F'.' '{
                        $NF = $NF + 1; OFS=".";
                        for (i=1; i<=NF; i++) printf("%s%s", (i==1 ? "" : OFS), $i);
                    }')
                    sed -i '' "s/\(MARKETING_VERSION = \)[^;]*/\1$app_version/" "$project_file"
                    log_info "应用版本号已更新为: $app_version"
                fi
            fi
        else
            log_warn "未找到 appcast.xml 文件，跳过版本自动升级"
        fi

        log_info "版本升级完成 - 应用版本: $app_version, 构建版本: $build_number"
    fi
}

# 执行 Xcode 构建和归档
execute_xcode_build() {
    log_step "执行 Xcode 构建和归档"

    cd "$TEMP_PROJECT_DIR"

    # 获取构建环境
    local build_env=$(grep "ENV\['SCENCE_ENV'\]" Podfile | sed -E "s/.*ENV\['SCENCE_ENV'\] = '(.*)'.*/\1/" || echo "ob")
    build_env=$(echo "$build_env" | tr 'a-z' 'A-Z')
    log_info "构建环境: $build_env"

    # 获取包名
    local package_name=$(grep "ENV\['PACKAGE_NAME'\]" Podfile | sed -E "s/.*ENV\['PACKAGE_NAME'\] = '(.*)'.*/\1/" || echo "ZXLauncher")
    log_info "包名: $package_name"

    # 获取版本信息
    local project_file="Launcher.xcodeproj/project.pbxproj"
    local app_version=$(grep "MARKETING_VERSION = " "$project_file" | head -1 | sed -E "s/.*MARKETING_VERSION = ([^;]*).*/\1/")
    local commit_number=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")

    # 生成归档名称
    local archive_name="${package_name}_${build_env}_${app_version}_${commit_number}"
    local archive_path="./build/${archive_name}.xcarchive"
    local configuration="Release"

    log_info "归档名称: $archive_name"
    log_info "归档路径: $archive_path"

    # 创建构建目录
    mkdir -p "./build"
    mkdir -p "./build/Archive"

    # 执行 xcodebuild archive
    log_info "开始执行 Xcode 归档..."

    local xcodebuild_cmd="xcodebuild archive -workspace Launcher.xcworkspace -scheme Launcher -configuration $configuration -archivePath $archive_path -arch arm64 CLANG_USE_RESPONSE_FILE=NO"

    if [[ "$JENKINS" == "1" ]]; then
        log_info "Jenkins 环境构建，使用 xcpretty 格式化输出"
        if $xcodebuild_cmd | xcpretty -r json-compilation-database --output ../compile_commands.json; then
            log_info "✅ Launcher 编译成功"
        else
            log_error "❌ Launcher 编译失败"
            return 1
        fi
    else
        log_info "本地环境构建"
        if $xcodebuild_cmd; then
            log_info "✅ Launcher 编译成功"
        else
            log_error "❌ Launcher 编译失败"
            return 1
        fi
    fi

    # 导出应用
    log_info "开始导出应用..."
    if xcodebuild -exportArchive -archivePath "$archive_path" -exportPath "./build/${archive_name}" -exportOptionsPlist "./Release/ExportOptions.plist"; then
        log_info "✅ 应用导出成功"
    else
        log_error "❌ 应用导出失败"
        return 1
    fi

    # 保存归档信息供后续使用
    echo "$archive_name" > "./build/archive_name.txt"

    return 0
}

# 执行打包流程
execute_build() {
    log_step "开始执行完整构建流程"

    cd "$TEMP_PROJECT_DIR"

    # 1. 自动升级版本号（如果需要）
    auto_upgrade_version

    # 2. 执行 Xcode 构建和归档
    if ! execute_xcode_build; then
        log_error "❌ Xcode 构建失败"
        return 1
    fi

    # 3. 创建 DMG
    if ! create_dmg_package; then
        log_error "❌ DMG 创建失败"
        return 1
    fi

    # 4. 执行公证（如果需要）
    if [[ "$NOTARIZE" == "1" ]]; then
        if ! execute_notarization; then
            log_error "❌ 公证失败"
            return 1
        fi
    fi

    # 5. 生成升级配置文件（如果需要）
    if [[ "$UPGRADE" == "1" ]]; then
        if ! generate_appcast; then
            log_error "❌ 升级配置文件生成失败"
            return 1
        fi
    fi

    # 6. 创建 dSYM 压缩包
    create_dsym_archive

    log_info "✅ 完整构建流程执行成功"
    return 0
}

# 创建 DMG 包
create_dmg_package() {
    log_step "创建 DMG 包"

    cd "$TEMP_PROJECT_DIR"

    # 读取归档名称
    local archive_name=$(cat "./build/archive_name.txt" 2>/dev/null || echo "unknown")
    local dmg_name="${archive_name}.dmg"

    log_info "DMG 名称: $dmg_name"

    # 检查导出的应用是否存在
    local app_path="./build/${archive_name}/*.app"
    if ! ls $app_path >/dev/null 2>&1; then
        log_error "未找到导出的应用文件: $app_path"
        return 1
    fi

    # 检查 createdmg.sh 脚本
    if [[ ! -f "$SCRIPT_DIR/createdmg.sh" ]]; then
        log_error "createdmg.sh 脚本不存在: $SCRIPT_DIR/createdmg.sh"
        return 1
    fi

    # 检查资源目录
    if [[ ! -d "./Release" ]]; then
        log_error "Release 资源目录不存在"
        return 1
    fi

    log_info "开始创建 DMG 包..."
    local make_dmg_cmd="$SCRIPT_DIR/createdmg.sh -r ./Release -i $app_path -o ./build/Archive/${dmg_name}"
    log_info "执行命令: $make_dmg_cmd"

    if $make_dmg_cmd; then
        log_info "✅ DMG 包创建成功: $dmg_name"
        return 0
    else
        log_error "❌ DMG 包创建失败"
        return 1
    fi
}

# 执行公证
execute_notarization() {
    log_step "开始对 DMG 进行公证"

    cd "$TEMP_PROJECT_DIR"

    # 读取归档名称
    local archive_name=$(cat "./build/archive_name.txt" 2>/dev/null || echo "unknown")
    local dmg_name="${archive_name}.dmg"
    local dmg_path="./build/Archive/${dmg_name}"

    # 检查 DMG 文件是否存在
    if [[ ! -f "$dmg_path" ]]; then
        log_error "DMG 文件不存在: $dmg_path"
        return 1
    fi

    # 检查公证配置文件
    local notarization_plist="./Release/notarization.plist"
    if [[ ! -f "$notarization_plist" ]]; then
        log_error "公证配置文件不存在: $notarization_plist"
        return 1
    fi

    # 读取公证配置
    local code_sign_identity=$(/usr/libexec/PlistBuddy -c "Print :CODE_SIGN_IDENTITY" "$notarization_plist" 2>/dev/null || echo "")
    local keychain_profile=$(/usr/libexec/PlistBuddy -c "Print :KEYCHAIN_PROFILE" "$notarization_plist" 2>/dev/null || echo "")

    if [[ -z "$code_sign_identity" || -z "$keychain_profile" ]]; then
        log_error "公证配置不完整，请检查 notarization.plist 文件"
        return 1
    fi

    log_info "代码签名身份: $code_sign_identity"
    log_info "钥匙串配置: $keychain_profile"

    # 检查公证脚本
    if [[ ! -f "$SCRIPT_DIR/notarization.sh" ]]; then
        log_error "公证脚本不存在: $SCRIPT_DIR/notarization.sh"
        return 1
    fi

    log_info "开始执行公证..."
    if "$SCRIPT_DIR/notarization.sh" -d "$dmg_path" -o "./build/Archive/" -s "$code_sign_identity" -k "$keychain_profile"; then
        log_info "✅ DMG 公证成功"
        return 0
    else
        log_error "❌ DMG 公证失败"
        return 1
    fi
}

# 生成升级配置文件
generate_appcast() {
    log_step "开始生成升级配置文件"

    cd "$TEMP_PROJECT_DIR"

    # 加载现有的 appcast.xml（如果存在）
    log_info "加载现有的升级配置文件..."
    load_update_appcast_xml

    # 检查 Sparkle 工具
    local sparkle_tool="../../LauncherSDK/WMLauncherDependency/Sparkle/bin/generate_appcast"
    if [[ ! -f "$sparkle_tool" ]]; then
        log_error "Sparkle 工具不存在: $sparkle_tool"
        return 1
    fi

    log_info "使用 Sparkle 工具生成 appcast.xml..."
    if "$sparkle_tool" "./build/Archive"; then
        log_info "✅ 升级配置文件生成成功"

        # 检查生成的文件
        if [[ -f "./build/Archive/appcast.xml" ]]; then
            log_info "appcast.xml 文件已生成"
        else
            log_warn "⚠️  appcast.xml 文件未找到"
        fi

        return 0
    else
        log_error "❌ 升级配置文件生成失败"
        return 1
    fi
}

# 创建 dSYM 压缩包
create_dsym_archive() {
    log_step "创建 dSYM 压缩包"

    cd "$TEMP_PROJECT_DIR"

    # 读取归档名称
    local archive_name=$(cat "./build/archive_name.txt" 2>/dev/null || echo "unknown")
    local dsym_path="./build/${archive_name}.xcarchive/dSYMs/*.app.dSYM"

    if ls $dsym_path >/dev/null 2>&1; then
        log_info "找到 dSYM 文件，开始创建压缩包..."
        if zip -r "./build/Archive/${archive_name}.dSYM.zip" $dsym_path; then
            log_info "✅ dSYM 压缩包创建成功"
        else
            log_warn "⚠️  dSYM 压缩包创建失败"
        fi
    else
        log_warn "⚠️  未找到 dSYM 文件: $dsym_path"
    fi
}

# 归档构建结果到地区工程目录
archive_build_results() {
    log_step "归档构建结果"

    cd "$TEMP_PROJECT_DIR"

    local temp_archive_dir="./build/Archive"
    local target_archive_dir="$PROJECT_ROOT/Channels/$PROJECT_DIR/Archive"

    if [[ ! -d "$temp_archive_dir" ]]; then
        log_warn "临时构建目录不存在: $temp_archive_dir"
        return 1
    fi

    log_info "归档目标目录: $target_archive_dir"

    # 创建目标归档目录
    rm -rf "$target_archive_dir"
    mkdir -p "$target_archive_dir"

    # 复制所有构建结果
    log_info "复制构建结果到地区工程目录..."
    cp -R "$temp_archive_dir/"* "$target_archive_dir/" 2>/dev/null || true

    # 查找并提取 dSYM 文件
    log_info "提取 dSYM 文件..."
    local xcarchive_files=($(find "$temp_archive_dir" -name "*.xcarchive" -type d))

    for xcarchive in "${xcarchive_files[@]}"; do
        if [[ -d "$xcarchive" ]]; then
            log_info "处理 xcarchive: $(basename "$xcarchive")"

            # 查找 dSYMs 目录
            local dsyms_dir="$xcarchive/dSYMs"
            if [[ -d "$dsyms_dir" ]]; then
                log_info "找到 dSYMs 目录: $dsyms_dir"

                # 复制 dSYM 文件到归档目录
                local dsym_files=($(find "$dsyms_dir" -name "*.dSYM" -type d))
                for dsym in "${dsym_files[@]}"; do
                    local dsym_name=$(basename "$dsym")
                    log_info "提取 dSYM: $dsym_name"
                    cp -R "$dsym" "$target_archive_dir/"
                done
            else
                log_warn "xcarchive 中未找到 dSYMs 目录: $xcarchive"
            fi
        fi
    done

    # 显示归档结果
    log_info "归档完成，内容:"
    ls -la "$target_archive_dir/" | head -15

    return 0
}

# 处理构建结果
handle_build_result() {
    local build_result=$1

    if [[ $build_result -eq 0 ]]; then
        log_step "构建成功"

        cd "$TEMP_PROJECT_DIR"

        # 复制构建结果到指定输出目录
        if [[ "$OUTPUT_DIR" != "./build/Archive" ]]; then
            log_info "复制构建结果到: $OUTPUT_DIR"
            mkdir -p "$OUTPUT_DIR"
            if [[ -d "./build/Archive" ]]; then
                cp -R "./build/Archive/"* "$OUTPUT_DIR/" 2>/dev/null || true
            fi
        fi

        # 归档构建结果到地区工程目录
        archive_build_results

        # 显示构建结果
        if [[ -d "./build/Archive" ]]; then
            log_info "临时构建结果:"
            ls -la "./build/Archive/" | head -10
        fi

        log_step "构建完成"
        log_info "产品名称: $PRODUCT_NAME"
        log_info "Bundle ID: $BUNDLE_ID"
        log_info "版本: $VERSION"
        log_info "构建号: $BUILD_NUMBER"
        log_info "环境: $SCENE_ENV"
        log_info "归档目录: $PROJECT_ROOT/Channels/$PROJECT_DIR/Archive"

        return 0
    else
        log_step "构建失败"
        log_error "构建过程中发生错误"
        return 1
    fi
}

# 主执行流程
main() {
    if [[ "$PREPARE_ONLY" == "1" ]]; then
        log_step "开始模板化构建流程 (仅准备模式)"
        log_info "⚠️  仅准备模式：只执行配置和依赖安装，不进行实际构建"
    else
        log_step "开始模板化构建流程"
    fi

    # 执行各个步骤
    update_podfile
    update_info_plist
    update_project_config
    copy_release_resources
    process_app_icon
    update_provisioning_profile
    install_cocoapods_dependencies

    if [[ "$PREPARE_ONLY" == "1" ]]; then
        # 仅准备模式，跳过构建步骤
        log_step "🎉 仅准备模式完成"
        log_info "临时工程路径: $TEMP_PROJECT_DIR"
        log_info "可以手动进入该目录进行开发或调试"
        log_info "注意: 临时工程目录已保留，请手动清理"

        # 显示临时工程内容
        log_info "临时工程内容:"
        ls -la "$TEMP_PROJECT_DIR" | head -10

        return 0
    else
        # 正常模式，执行构建
        if execute_build; then
            handle_build_result 0
        else
            handle_build_result 1
            exit 1
        fi
    fi
}

# 执行主流程
main
