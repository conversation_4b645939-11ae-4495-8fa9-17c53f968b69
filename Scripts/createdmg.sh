#!/bin/bash -l

# 设置错误处理
set -e

# 颜色定义
cNone='\033[00m'
cRed='\033[00;31m'
cGreen='\033[00;32m'
cLightGreen='\033[01;32m'
cYellow='\033[00;33m'
cCyan='\033[00;36m'

log_info() {
    echo -e "${cLightGreen}[信息]${cNone} $1"
}

log_warn() {
    echo -e "${cYellow}[警告]${cNone} $1"
}

log_error() {
    echo -e "${cRed}[错误]${cNone} $1"
}

log_step() {
    echo -e "${cCyan}===== $1 =====${cNone}"
}

# 显示帮助信息的函数
usage() {
    echo "Usage: $0 [-n volname] [-r resourcDirPath] [-i Application.app] [-o output]"
    echo
    echo "Options:"
    echo "  -n    volume name, displayed in the Finder sidebar and window title"
    echo "  -r    the dir path to icon.png and background.png inside the .dmg image"
    echo "  -i    the path to the .app file inside the disk image"
    echo "  -o    the path to the output dmg file"
    exit 1
}

while getopts 'n:i:o:r:' OPT; do
    case $OPT in
        n) VOLNAME=$OPTARG;;
        r) RESOURCT_DIR=$OPTARG;;
        i) INPUT_APP_FILE=$OPTARG;;
        o) OUTPUT_DMG=$OPTARG;;
        ?) usage;;
    esac
done

application_name="${INPUT_APP_FILE##*/}"
# 判断VOLNAME是否只有，没有值是将VOLNAME=application_name
if [ -z "$VOLNAME" ]; then
    VOLNAME="${application_name%.*}"
fi

echo "VOLNAME=${VOLNAME},application_name=${application_name}, RESOURCT_DIR=${RESOURCT_DIR}"

log_step "验证输入参数"

# 验证必需的参数
if [ -z "$RESOURCT_DIR" ]; then
    log_error "未指定资源目录 (-r 参数)"
    exit 1
fi

if [ -z "$INPUT_APP_FILE" ]; then
    log_error "未指定应用文件 (-i 参数)"
    exit 1
fi

if [ -z "$OUTPUT_DMG" ]; then
    log_error "未指定输出文件 (-o 参数)"
    exit 1
fi

log_info "参数验证通过"
log_info "  卷名称: $VOLNAME"
log_info "  应用程序: $application_name"
log_info "  资源目录: $RESOURCT_DIR"
log_info "  输出文件: $OUTPUT_DMG"

log_step "验证资源文件"

# 验证资源文件是否存在
if [ ! -d "$RESOURCT_DIR" ]; then
    log_error "资源目录不存在: $RESOURCT_DIR"
    exit 1
fi

if [ ! -f "${RESOURCT_DIR}/dmg-icon.icns" ]; then
    log_warn "DMG 图标文件不存在: ${RESOURCT_DIR}/dmg-icon.icns"
fi

if [ ! -f "${RESOURCT_DIR}/dmg-background.png" ]; then
    log_warn "DMG 背景文件不存在: ${RESOURCT_DIR}/dmg-background.png"
fi

if [ ! -d "$INPUT_APP_FILE" ]; then
    log_error "应用文件不存在: $INPUT_APP_FILE"
    exit 1
fi

log_info "资源文件验证完成"

log_step "使用 create-dmg 创建 DMG"

# 构建 create-dmg 命令参数
CREATE_DMG_ARGS=(
    --volname "${VOLNAME}"
    --window-pos 600 600
    --window-size 800 450
    --icon-size 100
    --icon "${application_name}" 200 200
    --hide-extension "${application_name}"
    --app-drop-link 575 200
    --hdiutil-verbose
)

# 添加可选的图标和背景
if [ -f "${RESOURCT_DIR}/dmg-icon.icns" ]; then
    CREATE_DMG_ARGS+=(--volicon "${RESOURCT_DIR}/dmg-icon.icns")
    log_info "使用 DMG 图标: ${RESOURCT_DIR}/dmg-icon.icns"
fi

if [ -f "${RESOURCT_DIR}/dmg-background.png" ]; then
    CREATE_DMG_ARGS+=(--background "${RESOURCT_DIR}/dmg-background.png")
    log_info "使用 DMG 背景: ${RESOURCT_DIR}/dmg-background.png"
fi

# 添加输出文件和源目录
CREATE_DMG_ARGS+=("${OUTPUT_DMG}" "${INPUT_APP_FILE}")

log_info "调用 create-dmg 工具..."
log_info "命令: create-dmg ${CREATE_DMG_ARGS[*]}"

if create-dmg "${CREATE_DMG_ARGS[@]}"; then
    log_info "✅ DMG 创建成功: ${OUTPUT_DMG}"
    DMG_CREATION_SUCCESS=true
else
    log_error "❌ DMG 创建失败"
    DMG_CREATION_SUCCESS=false
fi

log_step "验证 DMG 文件"

# 验证 DMG 文件
if [ "$DMG_CREATION_SUCCESS" = true ] && [ -f "$OUTPUT_DMG" ]; then
    log_info "验证 DMG 文件..."
    DMG_SIZE=$(du -h "$OUTPUT_DMG" | cut -f1)
    log_info "✅ DMG 文件创建完成"
    log_info "   文件路径: $OUTPUT_DMG"
    log_info "   文件大小: $DMG_SIZE"

    # 测试 DMG 挂载
    log_info "测试 DMG 挂载..."
    if hdiutil attach "$OUTPUT_DMG" -readonly -nobrowse -quiet; then
        log_info "✅ DMG 挂载测试成功"

        # 验证挂载内容
        if [ -d "/Volumes/$VOLNAME/$application_name" ]; then
            log_info "✅ 应用程序在 DMG 中验证成功"
        else
            log_warn "⚠️  应用程序在 DMG 中未找到"
        fi

        # 卸载测试挂载
        log_info "卸载测试挂载..."
        if hdiutil detach "/Volumes/$VOLNAME" -quiet 2>/dev/null; then
            log_info "✅ DMG 卸载成功"
        else
            log_warn "⚠️  DMG 卸载可能失败"
        fi
    else
        log_warn "⚠️  DMG 挂载测试失败，但文件已创建"
    fi

    log_step "🎉 DMG 创建完成"
    exit 0
else
    log_error "❌ DMG 创建最终验证失败"
    exit 1
fi
